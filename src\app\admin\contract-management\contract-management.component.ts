import { Compo<PERSON>, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CommonModule, NgIf, NgFor } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PartnerService } from '../../services/partner.service';
import { Partner, Contract } from '../../components/partner-contracts/partner-contracts.component';
import { ThemeService } from '../../services/theme.service';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-contract-management',
  templateUrl: './contract-management.component.html',
  styleUrls: ['./contract-management.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule,
    NgIf,
    NgFor,
    FormsModule
  ]
})
export class ContractManagementComponent implements OnInit, OnDestroy {
  partnerId: number;
  partner: Partner | null = null;
  contracts: Contract[] = [];
  loading = false;
  contractForm: FormGroup;
  editMode = false;
  currentContractId: number | null = null;
  selectedFile: File | null = null;
  isDarkMode = false;
  private destroy$ = new Subject<void>();

  constructor(
    private partnerService: PartnerService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private themeService: ThemeService
  ) {
    this.partnerId = 0;
    this.contractForm = this.fb.group({
      name: ['', Validators.required],
      description: [''],
      file: ['']
    });
  }

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    this.route.params.subscribe(params => {
      this.partnerId = +params['partnerId'];
      this.loadPartnerDetails();
      this.loadContracts();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadPartnerDetails(): void {
    this.loading = true;
    this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.success) {
          this.partner = response.partners.find(p => p.id === this.partnerId) || null;
          if (!this.partner) {
            this.showMessage('Partner not found', 'error');
          }
        } else {
          this.showMessage('Failed to load partner details', 'error');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading partner details:', error);
        this.showMessage('Failed to load partner details', 'error');
        this.loading = false;
      }
    });
  }

  loadContracts(): void {
    this.loading = true;
    this.partnerService.getPartnerContracts(this.partnerId).subscribe({
      next: (response) => {
        console.log('Contracts response:', response); // Debug log
        if (response.success) {
          this.contracts = response.contracts;
          console.log('Loaded contracts:', this.contracts); // Debug log
        } else {
          this.showMessage('Failed to load contracts', 'error');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading contracts:', error);
        this.showMessage('Failed to load contracts', 'error');
        this.loading = false;
      }
    });
  }

  trackByContractId(index: number, contract: Contract): any {
    return contract.id || index;
  }

  getRowStyles(): any {
    if (this.isDarkMode) {
      return {
        'background-color': '#1a1a1a !important',
        'color': '#ffffff !important'
      };
    }
    return {};
  }

  getHoverRowStyles(): any {
    if (this.isDarkMode) {
      return {
        'background-color': '#2c2c2c !important',
        'color': '#ffffff !important'
      };
    }
    return {};
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
    }
  }

  onSubmit(): void {
    if (this.contractForm.invalid) {
      return;
    }

    const formData = new FormData();
    formData.append('name', this.contractForm.get('name')?.value);
    formData.append('description', this.contractForm.get('description')?.value || '');

    if (this.selectedFile) {
      formData.append('file', this.selectedFile);
    } else if (!this.editMode) {
      this.showMessage('Please select a file', 'error');
      return;
    }

    if (this.editMode && this.currentContractId) {
      this.updateContract(this.currentContractId, formData);
    } else {
      this.createContract(formData);
    }
  }

  createContract(formData: FormData): void {
    this.loading = true;
    this.partnerService.createContract(this.partnerId, formData).subscribe({
      next: (response) => {
        if (response.success) {
          this.showMessage('Contract created successfully', 'success');
          this.loadContracts();
          this.resetForm();
        } else {
          this.showMessage('Failed to create contract', 'error');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error creating contract:', error);
        this.showMessage('Failed to create contract', 'error');
        this.loading = false;
      }
    });
  }

  updateContract(id: number, formData: FormData): void {
    this.loading = true;
    this.partnerService.updateContract(id, formData).subscribe({
      next: (response) => {
        if (response.success) {
          this.showMessage('Contract updated successfully', 'success');
          this.loadContracts();
          this.resetForm();
        } else {
          this.showMessage('Failed to update contract', 'error');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error updating contract:', error);
        this.showMessage('Failed to update contract', 'error');
        this.loading = false;
      }
    });
  }

  deleteContract(id: number): void {
    if (confirm('Are you sure you want to delete this contract?')) {
      this.loading = true;
      this.partnerService.deleteContract(id).subscribe({
        next: (response) => {
          if (response.success) {
            this.showMessage('Contract deleted successfully', 'success');
            this.loadContracts();
          } else {
            this.showMessage('Failed to delete contract', 'error');
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error deleting contract:', error);
          this.showMessage('Failed to delete contract', 'error');
          this.loading = false;
        }
      });
    }
  }

  editContract(contract: Contract): void {
    this.editMode = true;
    // Make sure contract.id is not undefined before assigning
    this.currentContractId = contract.id !== undefined ? contract.id : null;
    this.contractForm.patchValue({
      name: contract.name || '',
      description: contract.description || ''
    });
    // File can only be set when a new file is selected
  }

  resetForm(): void {
    this.editMode = false;
    this.currentContractId = null;
    this.contractForm.reset();
    this.selectedFile = null;
    const fileInput = document.getElementById('file') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  /**
   * Show a message to the user
   * @param message The message to show
   * @param type The type of message (success or error)
   */
  private showMessage(message: string, type: 'success' | 'error'): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: type === 'success' ? ['snackbar-success'] : ['snackbar-error'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }
}
