import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule, Router } from '@angular/router';
import { PartnerService } from '../../services/partner.service';
import { AuthService } from '../../services/auth.service';
import { Partner } from '../../components/partner-contracts/partner-contracts.component';

@Component({
  selector: 'app-partner-management',
  templateUrl: './partner-management.component.html',
  styleUrls: ['./partner-management.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatCardModule,
    MatTooltipModule,
    NgIf,
    FormsModule
  ]
})
export class PartnerManagementComponent implements OnInit {

  partners: Partner[] = [];
  loading = false;
  partnerForm: FormGroup;
  editMode = false;
  currentPartnerId: number | null = null;

  constructor(
    private partnerService: PartnerService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private router: Router,
    private authService: AuthService
  ) {
    this.partnerForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      logo: [''], // Make logo optional for now
      description: ['']
    });
  }

  ngOnInit(): void {
    this.loadPartners();

    // Set default values for better UX
    this.partnerForm.patchValue({
      description: ''
    });

    // Debug form state changes
    this.partnerForm.statusChanges.subscribe(status => {
      console.log('Form status changed:', status);
    });

    this.partnerForm.valueChanges.subscribe(value => {
      console.log('Form value changed:', value);
    });
  }

  // Helper method to check if a field has errors
  hasFieldError(fieldName: string): boolean {
    const field = this.partnerForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  // Helper method to get field error message
  getFieldError(fieldName: string): string {
    const field = this.partnerForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  // Test method for debugging
  testSubmit(): void {
    console.log('Test submit clicked!');
    console.log('Current form state:', {
      valid: this.partnerForm.valid,
      invalid: this.partnerForm.invalid,
      value: this.partnerForm.value,
      errors: this.partnerForm.errors,
      loading: this.loading
    });

    // Force submit regardless of validation
    const partnerData = this.partnerForm.value;
    if (partnerData.name && partnerData.name.trim()) {
      this.createPartner(partnerData);
    } else {
      console.log('No name provided, cannot submit');
    }
  }

  // Debug info method
  debugInfo(): void {
    console.log('=== DEBUG INFO ===');
    console.log('Form valid:', this.partnerForm.valid);
    console.log('Form value:', this.partnerForm.value);
    console.log('Form errors:', this.partnerForm.errors);

    // Check authentication
    const token = this.authService.getAuthToken();
    console.log('Auth token exists:', !!token);
    console.log('Auth token (first 20 chars):', token ? token.substring(0, 20) + '...' : 'No token');

    // Check user info
    this.authService.currentUser$.subscribe(user => {
      console.log('Current user:', user);
      console.log('User is admin:', user?.is_admin);
      console.log('User email:', user?.email);
    }).unsubscribe();

    // Check if logged in
    console.log('Is logged in:', this.authService.isLoggedIn());

    // Test API endpoint manually
    console.log('Testing API endpoint...');
    const testData = {
      name: 'Test Partner',
      logo: null,
      description: null
    };

    this.partnerService.createPartner(testData).subscribe({
      next: (response) => {
        console.log('Test API call successful:', response);
      },
      error: (error) => {
        console.error('Test API call failed:', error);
        console.error('Error status:', error.status);
        console.error('Error message:', error.message);
        console.error('Error details:', error.error);
      }
    });
  }

  loadPartners(): void {
    this.loading = true;
    this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.success) {
          this.partners = response.partners;
        } else {
          this.showMessage('Failed to load partners', 'error');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading partners:', error);
        this.showMessage('Failed to load partners', 'error');
        this.loading = false;
      }
    });
  }

  onSubmit(): void {
    console.log('Form submitted!');
    console.log('Form valid:', this.partnerForm.valid);
    console.log('Form value:', this.partnerForm.value);
    console.log('Form errors:', this.partnerForm.errors);

    // Mark all fields as touched to show validation errors
    this.partnerForm.markAllAsTouched();

    if (this.partnerForm.invalid) {
      console.log('Form is invalid, not submitting');
      this.showMessage('Please fill in all required fields correctly', 'error');
      return;
    }

    const partnerData = this.partnerForm.value;
    console.log('Submitting partner data:', partnerData);

    if (this.editMode && this.currentPartnerId) {
      this.updatePartner(this.currentPartnerId, partnerData);
    } else {
      this.createPartner(partnerData);
    }
  }

  createPartner(partnerData: Partial<Partner>): void {
    console.log('Creating partner with data:', partnerData);

    // Check authentication and admin status
    const token = this.authService.getAuthToken();
    console.log('Auth token available:', !!token);

    this.authService.currentUser$.subscribe(user => {
      console.log('Current user:', user);
      console.log('Is admin:', user?.is_admin);
    }).unsubscribe();

    if (!token) {
      this.showMessage('Authentication required. Please log in again.', 'error');
      return;
    }

    // Clean up the data before sending to API
    const cleanedData = {
      name: partnerData.name?.trim(),
      logo: partnerData.logo?.trim() || null, // Convert empty string to null
      description: partnerData.description?.trim() || null // Convert empty string to null
    };

    console.log('Cleaned partner data:', cleanedData);
    this.loading = true;

    this.partnerService.createPartner(cleanedData).subscribe({
      next: (response) => {
        console.log('Partner creation response:', response);
        if (response.success) {
          this.showMessage('Partner created successfully', 'success');
          this.loadPartners();
          this.resetForm();
        } else {
          console.error('Partner creation failed:', response);
          this.showMessage(`Failed to create partner: ${response.message || 'Unknown error'}`, 'error');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error creating partner:', error);

        let errorMessage = 'Failed to create partner';

        if (error.status === 401) {
          errorMessage = 'Authentication required. Please log in again.';
        } else if (error.status === 403) {
          errorMessage = 'Access denied. Admin privileges required.';
        } else if (error.status === 422) {
          console.log('Validation errors:', error.error);
          if (error.error && error.error.errors) {
            console.log('Detailed validation errors:', error.error.errors);
            // Show specific validation errors
            const validationErrors = Object.values(error.error.errors).flat();
            errorMessage = `Validation error: ${validationErrors.join(', ')}`;
          } else {
            errorMessage = 'Validation error. Please check your input.';
          }
        } else if (error.error && error.error.message) {
          errorMessage = error.error.message;
        }

        this.showMessage(errorMessage, 'error');
        this.loading = false;
      }
    });
  }

  updatePartner(id: number, partnerData: Partial<Partner>): void {
    this.loading = true;
    this.partnerService.updatePartner(id, partnerData).subscribe({
      next: (response) => {
        if (response.success) {
          this.showMessage('Partner updated successfully', 'success');
          this.loadPartners();
          this.resetForm();
        } else {
          this.showMessage('Failed to update partner', 'error');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error updating partner:', error);
        this.showMessage('Failed to update partner', 'error');
        this.loading = false;
      }
    });
  }

  deletePartner(id: number): void {
    if (confirm('Are you sure you want to delete this partner? This will also delete all associated contracts.')) {
      this.loading = true;
      this.partnerService.deletePartner(id).subscribe({
        next: (response) => {
          if (response.success) {
            this.showMessage('Partner deleted successfully', 'success');
            this.loadPartners();
          } else {
            this.showMessage('Failed to delete partner', 'error');
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error deleting partner:', error);
          this.showMessage('Failed to delete partner', 'error');
          this.loading = false;
        }
      });
    }
  }

  editPartner(partner: Partner): void {
    this.editMode = true;
    this.currentPartnerId = partner.id;
    this.partnerForm.patchValue({
      name: partner.name,
      logo: partner.logo,
      description: partner.description
    });
  }

  viewPartnerContracts(partnerId: number): void {
    this.router.navigate(['/admin/contracts', partnerId]);
  }

  resetForm(): void {
    this.editMode = false;
    this.currentPartnerId = null;
    this.partnerForm.reset();
    this.partnerForm.markAsUntouched();
    this.partnerForm.markAsPristine();

    // Reset to initial values
    this.partnerForm.patchValue({
      name: '',
      logo: '',
      description: ''
    });
  }

  /**
   * Show a message to the user
   * @param message The message to show
   * @param type The type of message (success or error)
   */
  private showMessage(message: string, type: 'success' | 'error'): void {
    const panelClass = type === 'success'
      ? ['snackbar-success', 'custom-snackbar']
      : ['snackbar-error', 'custom-snackbar'];

    this.snackBar.open(message, 'Close', {
      duration: 4000,
      panelClass: panelClass,
      horizontalPosition: 'center',
      verticalPosition: 'bottom'
    });
  }
}
