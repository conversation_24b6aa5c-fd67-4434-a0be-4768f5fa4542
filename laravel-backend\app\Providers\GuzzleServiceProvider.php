<?php

namespace App\Providers;

use GuzzleHttp\Client;
use Illuminate\Support\ServiceProvider;

class GuzzleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('guzzle', function () {
            return new Client([
                'verify' => false, // Disable SSL verification in development
                'timeout' => 30,
            ]);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Configure Socialite to use our custom Guzzle client
        // Only configure if Socialite class exists
        if (class_exists('\Laravel\Socialite\Facades\Socialite')) {
            $this->configureSocialite();
        }
    }

    /**
     * Configure Socialite to use our custom Guzzle client
     */
    protected function configureSocialite(): void
    {
        // Skip if Socialite is not available
        if (!class_exists('\Laravel\Socialite\Facades\Socialite')) {
            return;
        }

        $guzzle = app('guzzle');

        // Configure Google driver
        \Laravel\Socialite\Facades\Socialite::extend('google', function ($app) use ($guzzle) {
            $config = $app['config']['services.google'];
            return \Laravel\Socialite\Facades\Socialite::buildProvider(
                \Laravel\Socialite\Two\GoogleProvider::class,
                $config
            )->setHttpClient($guzzle);
        });
    }
}
