<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | This file controls how your Laravel API handles CORS requests, including
    | from your frontend (e.g. Angular on localhost:4200) and external services
    | like Google OAuth. You can adjust paths, origins, headers, etc. here.
    |
    */

    'paths' => [
        'api/*',
        'auth/*',
        'sanctum/csrf-cookie',
        'login/google',
        'login/google/callback'
    ],

    'allowed_methods' => ['*'], // Allow all HTTP methods (GET, POST, PUT, etc.)

    'allowed_origins' => [
        '*',  // Allow all origins during development
        // 'http://localhost:4200',   // Angular frontend (dev)
        // 'http://localhost:8000',   // Local Laravel backend
        // 'capacitor://localhost',   // If you're using mobile wrapper like Capacitor
        // 'ionic://localhost'
    ],

    'allowed_origins_patterns' => [
        '^https:\/\/accounts\.google\.com.*$',
        '^https:\/\/www\.googleapis\.com.*$'
    ],

    'allowed_headers' => [
        'Content-Type',
        'X-Auth-Token',
        'Origin',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'X-File-Size',
        'X-File-Name',
        'X-File-Type'
    ],

    'exposed_headers' => [
        'Authorization',
        'X-Auth-Token',
        'Content-Disposition'
    ],

    'max_age' => 0,

    'supports_credentials' => true, // Allow cookies (if you're using Sanctum or session)

];
