/* Contract Management Styles - Theme Adaptive */
:host {
  display: block;
  width: 100%;
  background: var(--background);
  color: var(--text);
  transition: background-color 0.3s ease, color 0.3s ease;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: var(--text);
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  padding: 1.5rem 0;
  border-bottom: 3px solid transparent;
  background: linear-gradient(90deg, var(--border), transparent) bottom/100% 3px no-repeat;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--primary), var(--primary-light, #5fd4cc));
  border-radius: 2px;
}

.page-title {
  font-size: 2.2rem;
  font-weight: 800;
  color: var(--text);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.page-title i {
  color: var(--primary);
  font-size: 2rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-light, #5fd4cc));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(78, 205, 196, 0.2));
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 0.6rem;
  padding: 0.8rem 1.6rem;
  background: linear-gradient(135deg, var(--surface), var(--surface-hover));
  color: var(--text);
  text-decoration: none;
  border-radius: 12px;
  border: 2px solid var(--border);
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.back-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(78, 205, 196, 0.1), transparent);
  transition: left 0.4s ease;
}

.back-button:hover::before {
  left: 100%;
}

.back-button:hover {
  background: linear-gradient(135deg, var(--surface-hover), var(--primary));
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  color: white;
  text-decoration: none;
  border-color: var(--primary);
}

.back-button i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.back-button:hover i {
  transform: translateX(-2px);
}

/* Loading Spinner */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem 2rem;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Cards */
.card {
  background: var(--surface);
  border-radius: 16px;
  border: 1px solid var(--border);
  box-shadow: var(--shadow-md);
  margin-bottom: 2rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  padding: 1.5rem;
  border-bottom: none;
  position: relative;
  overflow: hidden;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.card-header h5 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.card-body {
  padding: 2rem;
  background: var(--surface);
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text);
  font-size: 0.95rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border);
  border-radius: 8px;
  background: var(--surface);
  color: var(--text);
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

.form-control::placeholder {
  color: var(--text-secondary);
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.form-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.text-danger {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* File Input Styling */
input[type="file"].form-control {
  padding: 0.5rem;
  cursor: pointer;
}

input[type="file"].form-control::-webkit-file-upload-button {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  margin-right: 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

input[type="file"].form-control::-webkit-file-upload-button:hover {
  background: var(--primary-dark);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.btn-secondary {
  background: var(--surface);
  color: var(--text);
  border: 2px solid var(--border);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--surface-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.btn-sm {
  padding: 0.6rem 1.2rem;
  font-size: 0.85rem;
  border-radius: 8px;
  min-width: 80px;
  position: relative;
  overflow: hidden;
}

.btn-sm::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.4s ease;
}

.btn-sm:hover::before {
  left: 100%;
}

.btn i {
  font-size: 0.9rem;
  margin-right: 0.4rem;
}

/* Enhanced action buttons */
.btn-sm.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark, #3dbdb5));
  border: 1px solid rgba(78, 205, 196, 0.3);
  box-shadow: 0 3px 12px rgba(78, 205, 196, 0.25);
}

.btn-sm.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark, #3dbdb5), var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.35);
}

.btn-sm.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border: 1px solid rgba(239, 68, 68, 0.3);
  box-shadow: 0 3px 12px rgba(239, 68, 68, 0.25);
}

.btn-sm.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.35);
}

/* Button Groups */
.btn-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

/* Actions column specific styling */
.table tbody td:last-child {
  padding: 1rem 1.5rem;
}

.table tbody td:last-child .btn-group {
  justify-content: flex-start;
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background: var(--surface);
  border: 1px solid var(--border);
  margin-top: 1.5rem;
}

.table {
  width: 100%;
  margin-bottom: 0;
  background: transparent;
  border-collapse: collapse;
  border-radius: 16px;
  overflow: hidden;
}

.table thead {
  background: linear-gradient(135deg, var(--primary), var(--primary-light, #5fd4cc));
  position: relative;
}

.table thead::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.9), rgba(95, 212, 204, 0.9));
  backdrop-filter: blur(10px);
}

.table thead th {
  padding: 1.25rem 1.5rem;
  font-weight: 700;
  color: white;
  border: none;
  text-align: left;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.table thead th i {
  margin-right: 0.5rem;
  opacity: 0.9;
  font-size: 0.85rem;
}

.table tbody tr {
  border-bottom: 1px solid var(--border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--surface) !important;
}

.table tbody tr:hover {
  background: var(--surface-hover) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table tbody tr:last-child {
  border-bottom: none;
}

.table tbody td {
  padding: 1.5rem 1.5rem;
  color: var(--text);
  vertical-align: middle;
  font-size: 0.95rem;
  line-height: 1.5;
}

.table tbody td:first-child {
  font-weight: 700;
  color: var(--primary);
  font-size: 1rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-light, #5fd4cc));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.table tbody td strong {
  color: var(--text);
  font-weight: 600;
  font-size: 1rem;
}

.table tbody td i.fas {
  margin-right: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Dark mode specific overrides */
:host-context(.dark-mode) .table tbody td {
  color: #e5e7eb !important;
}

:host-context(.dark-mode) .table tbody td strong {
  color: #f9fafb !important;
}

:host-context(.dark-mode) .table thead th {
  color: #ffffff !important;
}

:host-context(.dark-mode) .table tbody tr {
  background: var(--surface) !important;
}

:host-context(.dark-mode) .table tbody tr:hover {
  background: var(--surface-hover) !important;
}

:host-context(.dark-mode) .table-responsive {
  background: var(--surface) !important;
  border-color: var(--border) !important;
}

:host-context(.dark-mode) .table {
  background: transparent !important;
}

/* Additional dark mode support for different selectors */
body.dark-mode .table tbody td,
.dark-mode .table tbody td,
[data-theme="dark"] .table tbody td {
  color: #e5e7eb !important;
  background: transparent !important;
}

body.dark-mode .table tbody td strong,
.dark-mode .table tbody td strong,
[data-theme="dark"] .table tbody td strong {
  color: #f9fafb !important;
}

body.dark-mode .table tbody tr,
.dark-mode .table tbody tr,
[data-theme="dark"] .table tbody tr {
  background: var(--surface, #1f2937) !important;
  border-color: var(--border, #374151) !important;
}

body.dark-mode .table tbody tr:hover,
.dark-mode .table tbody tr:hover,
[data-theme="dark"] .table tbody tr:hover {
  background: var(--surface-hover, #374151) !important;
}

body.dark-mode .table-responsive,
.dark-mode .table-responsive,
[data-theme="dark"] .table-responsive {
  background: var(--surface, #1f2937) !important;
  border-color: var(--border, #374151) !important;
}

/* Badge Styles */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  min-width: 70px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.badge:hover::before {
  left: 100%;
}

.badge.bg-primary {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.badge.bg-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.badge.bg-secondary {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

/* File type specific badges */
.badge.bg-primary::after {
  content: '📄';
  margin-left: 0.5rem;
  font-size: 0.8rem;
}

.badge.bg-danger::after {
  content: '📕';
  margin-left: 0.5rem;
  font-size: 0.8rem;
}

.badge.bg-secondary::after {
  content: '📝';
  margin-left: 0.5rem;
  font-size: 0.8rem;
}

/* Alert Styles */
.alert {
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 1px solid;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: #1d4ed8;
}

.alert-info::before {
  content: "ℹ️";
  font-size: 1.2rem;
}

/* Empty State */
.no-data-message {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
  background: var(--surface);
  border-radius: 16px;
  border: 2px dashed var(--border);
  margin: 2rem 0;
  position: relative;
  overflow: hidden;
}

.no-data-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(78, 205, 196, 0.03) 50%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.no-data-message i {
  font-size: 4rem;
  color: var(--primary);
  margin-bottom: 1.5rem;
  display: block;
  opacity: 0.7;
  background: linear-gradient(135deg, var(--primary), var(--primary-light, #5fd4cc));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.no-data-message p {
  font-size: 1.2rem;
  margin: 0;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* Utility Classes */
.d-flex {
  display: flex;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-center {
  justify-content: center;
}

.align-items-center {
  align-items: center;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.me-2 {
  margin-right: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
    padding: 1rem 0;
  }

  .page-title {
    font-size: 1.8rem;
    text-align: center;
    gap: 0.8rem;
  }

  .page-title i {
    font-size: 1.6rem;
  }

  .back-button {
    justify-content: center;
    padding: 0.7rem 1.4rem;
    font-size: 0.9rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .btn-group {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn {
    justify-content: center;
    width: 100%;
  }

  .table-responsive {
    font-size: 0.875rem;
    border-radius: 12px;
    margin-top: 1rem;
  }

  .table thead th,
  .table tbody td {
    padding: 1rem 0.75rem;
  }

  .table thead th {
    font-size: 0.8rem;
    letter-spacing: 0.5px;
  }

  .table thead th i {
    font-size: 0.75rem;
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    min-width: 70px;
  }

  .badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
    min-width: 60px;
  }

  .badge.bg-primary::after,
  .badge.bg-danger::after,
  .badge.bg-secondary::after {
    font-size: 0.7rem;
    margin-left: 0.3rem;
  }

  .no-data-message {
    padding: 3rem 1.5rem;
  }

  .no-data-message i {
    font-size: 3rem;
  }

  .no-data-message p {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  .page-title {
    font-size: 1.25rem;
  }

  .card-header h5 {
    font-size: 1.1rem;
  }

  .form-control {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .table {
    font-size: 0.8rem;
  }

  .table thead th,
  .table tbody td {
    padding: 0.5rem 0.25rem;
  }

  /* Stack table actions vertically on very small screens */
  .table tbody td:last-child {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .table tbody td:last-child .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  .alert-info {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.4);
    color: #60a5fa;
  }
}

/* Additional dark mode overrides for better compatibility */
body.dark-mode .table tbody td,
body.dark-mode .table thead th {
  color: #ffffff !important;
}

body.dark-mode .table tbody td strong {
  color: #ffffff !important;
}

body.dark-mode .page-title {
  color: #ffffff !important;
}

body.dark-mode .form-label {
  color: #ffffff !important;
}

/* Dark mode badge improvements */
body.dark-mode .badge,
.dark-mode .badge,
[data-theme="dark"] .badge {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Dark mode card improvements */
body.dark-mode .card,
.dark-mode .card,
[data-theme="dark"] .card {
  background: var(--surface, #1f2937) !important;
  border-color: var(--border, #374151) !important;
}

body.dark-mode .card-body,
.dark-mode .card-body,
[data-theme="dark"] .card-body {
  background: var(--surface, #1f2937) !important;
}

/* Dark mode no-data message */
body.dark-mode .no-data-message,
.dark-mode .no-data-message,
[data-theme="dark"] .no-data-message {
  background: var(--surface, #1f2937) !important;
  border-color: var(--border, #374151) !important;
  color: var(--text-secondary, #9ca3af) !important;
}

body.dark-mode .alert-info {
  color: #60a5fa !important;
}

/* Comprehensive dark mode fix for table */
body.dark-mode .table tbody tr,
body.dark-mode .table tbody tr td {
  background: var(--surface, #1a1a1a) !important;
  color: var(--text, #ffffff) !important;
}

body.dark-mode .table tbody tr:hover,
body.dark-mode .table tbody tr:hover td {
  background: var(--surface-hover, #2c2c2c) !important;
  color: var(--text, #ffffff) !important;
}

body.dark-mode .table tbody td strong {
  color: var(--text, #ffffff) !important;
}

body.dark-mode .table-responsive {
  background: var(--surface, #1a1a1a) !important;
  border-color: var(--border, #2e2e2e) !important;
}

/* Force override for any conflicting styles */
body.dark-mode .contract-management-component .table tbody tr {
  background: var(--surface, #1a1a1a) !important;
}

body.dark-mode .contract-management-component .table tbody tr:hover {
  background: var(--surface-hover, #2c2c2c) !important;
}

/* Comprehensive dark mode fix for table - HIGHEST SPECIFICITY */
body.dark-mode .contract-management-container .table tbody tr,
body.dark-mode .contract-management-container .table tbody tr td,
body.dark-mode .contract-management-container .table tbody td {
  background-color: #1a1a1a !important;
  background: #1a1a1a !important;
  color: #ffffff !important;
}

body.dark-mode .contract-management-container .table tbody tr:hover,
body.dark-mode .contract-management-container .table tbody tr:hover td {
  background-color: #2c2c2c !important;
  background: #2c2c2c !important;
  color: #ffffff !important;
}

body.dark-mode .contract-management-container .table tbody td strong {
  color: #ffffff !important;
}

body.dark-mode .contract-management-container .table-responsive {
  background-color: #1a1a1a !important;
  background: #1a1a1a !important;
  border-color: #2e2e2e !important;
}

body.dark-mode .contract-management-container .table {
  background-color: transparent !important;
  background: transparent !important;
}

/* Additional class-based dark mode styles */
.table-row.dark-mode-row {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
}

.table-row.dark-mode-row:hover {
  background-color: #2c2c2c !important;
  color: #ffffff !important;
}

.table-row.dark-mode-row td {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
}

.table-row.dark-mode-row:hover td {
  background-color: #2c2c2c !important;
  color: #ffffff !important;
}

.table-row.dark-mode-row td strong {
  color: #ffffff !important;
}

/* Force override for any conflicting styles */
body.dark-mode .contract-management-component .table tbody tr {
  background: var(--surface, #1a1a1a) !important;
}

body.dark-mode .contract-management-component .table tbody tr:hover {
  background: var(--surface-hover, #2c2c2c) !important;
}

/* ULTIMATE DARK MODE FIX - NUCLEAR OPTION */
body.dark-mode .contract-management-container .table tbody tr,
body.dark-mode .contract-management-container .table tbody tr *,
body.dark-mode .contract-management-container .table tbody td,
body.dark-mode .contract-management-container .table tbody td * {
  background-color: #1a1a1a !important;
  background: #1a1a1a !important;
  color: #ffffff !important;
}

body.dark-mode .contract-management-container .table tbody tr:hover,
body.dark-mode .contract-management-container .table tbody tr:hover *,
body.dark-mode .contract-management-container .table tbody tr:hover td,
body.dark-mode .contract-management-container .table tbody tr:hover td * {
  background-color: #2c2c2c !important;
  background: #2c2c2c !important;
  color: #ffffff !important;
}

/* Force text visibility in all scenarios */
.table tbody td {
  color: var(--text, #333333) !important;
}

.table thead th {
  color: var(--text, #333333) !important;
}

/* Dark mode class-based overrides */
:host-context(body.dark-mode) .table tbody td,
:host-context(body.dark-mode) .table thead th {
  color: #ffffff !important;
}

:host-context(body.dark-mode) .page-title {
  color: #ffffff !important;
}

:host-context(body.dark-mode) .form-label {
  color: #ffffff !important;
}

/* Print styles */
@media print {
  .btn,
  .back-button {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .page-header {
    border-bottom: 2px solid #000;
  }
}
