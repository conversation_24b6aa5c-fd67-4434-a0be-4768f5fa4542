/* Modern Profile Page Styles */
:host {
  --primary: #4ECDC4;
  --primary-light: #6CDED6;
  --primary-dark: #3DACA4;
  --primary-gradient: linear-gradient(135deg, var(--primary), var(--primary-light));
  --secondary: #64748b;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Theme-adaptive variables - default light mode */
  --profile-bg: #f8fafc;
  --profile-container-bg: #ffffff;
  --profile-card-bg: #ffffff;
  --profile-header-bg: #ffffff;
  --profile-text-primary: #1e293b;
  --profile-text-secondary: #64748b;
  --profile-text-tertiary: #94a3b8;
  --profile-border-color: #e2e8f0;
  --profile-input-bg: #ffffff;
  --profile-input-border: #d1d5db;
  --profile-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --profile-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --profile-hover-bg: #f8fafc;
  --profile-cover-gradient: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
}

/* Dark Mode */
body.dark-mode :host,
:host-context(body.dark-mode) {
  --profile-bg: #1a1a1a;
  --profile-container-bg: #252525;
  --profile-card-bg: #252525;
  --profile-header-bg: #252525;
  --profile-text-primary: #ffffff;
  --profile-text-secondary: #cccccc;
  --profile-text-tertiary: #a0a0a0;
  --profile-border-color: #2e2e2e;
  --profile-input-bg: #2c2c2c;
  --profile-input-border: #3a3a3a;
  --profile-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --profile-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --profile-hover-bg: #2c2c2c;
  --profile-cover-gradient: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
}

.profile-page {
  min-height: 100vh;
  background: var(--profile-bg);
  padding: 2rem;
  color: var(--profile-text-primary);
}

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: var(--profile-card-bg);
  border-radius: 16px;
  box-shadow: var(--profile-shadow);
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--profile-border-color);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--profile-text-secondary);
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Alert Styles */
.alert {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-radius: 12px;
  border: 1px solid;
  font-size: 0.95rem;
  line-height: 1.5;
}

.alert-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-icon i {
  font-size: 24px;
}

.alert-content {
  flex: 1;
}

.alert-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.alert-content p {
  margin: 0;
}

.alert-danger {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.alert-danger .alert-icon i {
  color: #dc2626;
}

.alert-success {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
  color: #16a34a;
}

.alert-success .alert-icon i {
  color: #16a34a;
}

body.dark-mode .alert-danger,
:host-context(body.dark-mode) .alert-danger {
  background-color: rgba(220, 38, 38, 0.1);
  border-color: rgba(220, 38, 38, 0.3);
  color: #fca5a5;
}

body.dark-mode .alert-success,
:host-context(body.dark-mode) .alert-success {
  background-color: rgba(22, 163, 74, 0.1);
  border-color: rgba(22, 163, 74, 0.3);
  color: #86efac;
}

.error-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

/* Profile Container */
.profile-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Profile Header Card */
.profile-header-card {
  background: var(--profile-header-bg);
  border-radius: 20px;
  box-shadow: var(--profile-shadow-lg);
  overflow: hidden;
  position: relative;
}

.profile-cover {
  height: 200px;
  background: var(--profile-cover-gradient);
  position: relative;
  overflow: hidden;
}

.cover-gradient {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(78, 205, 196, 0.9) 0%,
    rgba(108, 222, 214, 0.8) 50%,
    rgba(61, 172, 164, 0.9) 100%);
}

.profile-info {
  padding: 2rem;
  position: relative;
  margin-top: -80px;
}

.avatar-section {
  display: flex;
  align-items: flex-end;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Avatar Styles */
.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.avatar-wrapper {
  position: relative;
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background: var(--profile-header-bg);
  padding: 6px;
  box-shadow: var(--profile-shadow-lg);
}

.profile-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--primary);
  transition: all 0.3s ease;
  background: var(--profile-input-bg);
}

.avatar-status {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 3px solid var(--profile-card-bg);
  background-color: #94a3b8;
}

.avatar-status.online {
  background-color: var(--success);
}

.avatar-overlay {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.avatar-upload-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.avatar-upload-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.avatar-upload-btn i {
  font-size: 24px;
}

.avatar-upload {
  position: absolute;
  bottom: 0;
  right: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-container:hover .avatar-upload {
  opacity: 1;
}

.btn-upload {
  background-color: var(--primary);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--profile-shadow);
}

.btn-upload:hover {
  background-color: var(--primary-dark);
  transform: scale(1.1);
}

.btn-upload i {
  font-size: 20px;
}

/* User Details */
.user-details {
  flex: 1;
}

.user-name {
  font-size: 2rem;
  font-weight: 700;
  color: var(--profile-text-primary);
  margin: 0 0 0.5rem 0;
}

.user-email {
  color: var(--profile-text-secondary);
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
}

.user-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.badge.verified {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.badge.user-role {
  background-color: rgba(78, 205, 196, 0.1);
  color: var(--primary);
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.badge i {
  font-size: 16px;
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--profile-text-primary);
  margin: 0 0 5px 0;
}

.profile-email {
  color: var(--profile-text-secondary);
  font-size: 0.9rem;
  margin: 0 0 20px 0;
}

.profile-info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.info-card {
  background: var(--profile-hover-bg);
  border: 1px solid var(--profile-border-color);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--profile-shadow);
}

.info-icon {
  width: 40px;
  height: 40px;
  background: rgba(78, 205, 196, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-icon i {
  font-size: 20px;
  color: var(--primary);
}

.info-content {
  flex: 1;
  min-width: 0;
}

.info-label {
  font-size: 0.75rem;
  color: var(--profile-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 0.95rem;
  color: var(--profile-text-primary);
  font-weight: 600;
  word-break: break-word;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

/* Form Card */
.form-card, .actions-card {
  background: var(--profile-card-bg);
  border-radius: 16px;
  box-shadow: var(--profile-shadow);
  border: 1px solid var(--profile-border-color);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--profile-border-color);
  background: var(--profile-hover-bg);
}

.card-header h2, .card-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--profile-text-primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.card-header p {
  margin: 0;
  color: var(--profile-text-secondary);
  font-size: 0.95rem;
}

.card-body {
  padding: 2rem;
}

.profile-content {
  padding: 2rem;
  background-color: var(--profile-content-bg);
}

.content-header {
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--profile-border-color);
  padding-bottom: 1rem;
}

.content-header h2 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--profile-text-primary);
}

.content-header p {
  color: var(--profile-text-secondary);
  font-size: 1rem;
}

.profile-form {
  max-width: 100%;
}

/* Form Styling */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--profile-text-primary);
  font-size: 0.95rem;
}

.form-label i {
  font-size: 18px;
  color: var(--primary);
}

.form-label .optional {
  color: var(--profile-text-secondary);
  font-weight: 400;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  border: 2px solid var(--profile-input-border);
  border-radius: 12px;
  transition: all 0.3s ease;
  background-color: var(--profile-input-bg);
  color: var(--profile-text-primary);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.form-input.error {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  color: var(--error);
  font-size: 0.875rem;
}

.error-message i {
  font-size: 16px;
}

.form-hint {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  color: var(--profile-text-secondary);
  font-size: 0.875rem;
}

.form-hint i {
  font-size: 16px;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.1rem;
  min-width: 200px;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 2rem;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--profile-shadow-lg);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background: var(--profile-hover-bg);
  color: var(--profile-text-primary);
  border: 2px solid var(--profile-border-color);
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
}

.btn-secondary:hover {
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
}

/* Action List */
.action-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: var(--profile-hover-bg);
  border: 1px solid var(--profile-border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.action-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--profile-shadow);
  border-color: var(--primary);
}

.action-icon {
  width: 48px;
  height: 48px;
  background: rgba(78, 205, 196, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-icon i {
  font-size: 24px;
  color: var(--primary);
}

.action-content {
  flex: 1;
}

.action-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--profile-text-primary);
}

.action-content p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--profile-text-secondary);
}

.action-item .arrow {
  color: var(--profile-text-tertiary);
  font-size: 18px;
  transition: all 0.3s ease;
}

.action-item:hover .arrow {
  color: var(--primary);
  transform: translateX(4px);
}

/* Alerts */
.alert {
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 8px;
}

.alert-danger {
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.alert-success {
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

/* Activity Section */
.profile-activity {
  background-color: var(--profile-activity-bg);
  padding: 2rem;
  border-left: 1px solid var(--profile-border-color);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--profile-border-color);
}

.activity-header h3 {
  font-size: 1.3rem;
  color: var(--profile-text-primary);
  margin: 0;
}

.view-all {
  color: #4ECDC4; /* Teal/Turquoise color */
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-all:hover {
  color: var(--profile-text-primary);
  text-decoration: underline;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: var(--profile-activity-item-bg);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background-color: var(--profile-activity-item-hover);
  transform: translateX(5px);
}

.activity-icon {
  width: 40px;
  height: 40px;
  background-color: rgba(78, 205, 196, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon i {
  color: #4ECDC4; /* Teal/Turquoise color */
  font-size: 1.2rem;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: var(--profile-text-primary);
  margin-bottom: 5px;
}

.activity-desc {
  color: var(--profile-text-secondary);
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.activity-time {
  color: var(--profile-text-tertiary);
  font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .profile-info-cards {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .profile-page {
    padding: 1rem;
  }

  .profile-info {
    padding: 1.5rem;
    margin-top: -40px;
  }

  .avatar-wrapper {
    width: 100px;
    height: 100px;
  }

  .user-name {
    font-size: 1.5rem;
  }

  .profile-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .form-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-primary, .btn-secondary {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .profile-cover {
    height: 80px;
  }

  .avatar-wrapper {
    width: 80px;
    height: 80px;
  }

  .card-header, .card-body {
    padding: 1.5rem;
  }

  .user-badges {
    justify-content: center;
  }
}
