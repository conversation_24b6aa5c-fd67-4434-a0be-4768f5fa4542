<!-- Sidebar -->
<aside class="sidebar" [class.expanded]="isExpanded">
  <!-- Logo -->
  <div class="sidebar-header" (click)="toggleSidebar()">
    <a class="logo" routerLink="/">
      <img src="assets/images/logo.png" alt="INSOMEA" >
      <span >ContractPro</span>
    </a>
    <div class="sidebar-toggle"></div>
  </div>

  <!-- Error Alert -->
  <div class="auth-error" *ngIf="authError" role="alert">
    {{ authError }}
  </div>

  <!-- Navigation -->
  <nav class="sidebar-nav">
    <ul>
      <!-- Public Section (visible to all) -->
      <div class="nav-category">Main</div>

      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">
        <a routerLink="/">
          <i class="material-icons">home</i>
          <span>Home</span>
        </a>
      </li>

      <li routerLinkActive="active" *ngIf="isLoggedIn$ | async">
        <a routerLink="/dashboard">
          <i class="material-icons">dashboard</i>
          <span>Dashboard</span>
        </a>
      </li>


      <!-- Personal Section (requires auth) -->
      <div class="nav-category" *ngIf="isLoggedIn$ | async">Personal</div>

      <li routerLinkActive="active" *ngIf="isLoggedIn$ | async">
        <a routerLink="/profile">
          <i class="material-icons">person</i>
          <span>Profile</span>
        </a>
      </li>

     

      <li routerLinkActive="active" *ngIf="isLoggedIn$ | async">
        <a routerLink="/ai-chatbot">
          <i class="material-icons">smart_toy</i>
          <span>AI Assistant</span>
        </a>
      </li>



      <!-- Documents Section (requires auth) -->
      <div class="nav-category" *ngIf="isLoggedIn$ | async">Documents</div>

      <li routerLinkActive="active" *ngIf="isLoggedIn$ | async">
        <a routerLink="/partner-contracts">
          <i class="material-icons">handshake</i>
          <span>Partner Contracts</span>
        </a>
      </li>

      <li routerLinkActive="active" *ngIf="isLoggedIn$ | async">
        <a routerLink="/file-upload">
          <i class="material-icons">cloud_upload</i>
          <span>Upload Documents</span>
        </a>
      </li>

      <li *ngIf="isLoggedIn$ | async" class="non-clickable-item" [class.active]="isSignatureActive()">
        <div class="nav-item-display">
          <i class="material-icons">draw</i>
          <span>Electronic Signature</span>
        </div>
      </li>





      <!-- Admin Section (requires admin privileges) -->
      <div class="nav-category" *ngIf="(isLoggedIn$ | async) && (isAdmin() | async)">Administration</div>

      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="(isLoggedIn$ | async) && (isAdmin() | async)">
        <a routerLink="/admin">
          <i class="material-icons">admin_panel_settings</i>
          <span>Admin Panel</span>
        </a>
      </li>

      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="(isLoggedIn$ | async) && (isAdmin() | async)">
        <a routerLink="/admin/notifications">
          <i class="material-icons">notifications</i>
          <span>Notifications</span>
        </a>
      </li>

      <li routerLinkActive="active" *ngIf="(isLoggedIn$ | async) && (isAdmin() | async)">
        <a routerLink="/admin/partners">
          <i class="material-icons">business</i>
          <span>Partner Management</span>
        </a>
      </li>
      <!-- Support Section (always visible) -->
      <div class="nav-category">Support</div>

      <li routerLinkActive="active">
        <a routerLink="/contact">
          <i class="material-icons">contact_support</i>
          <span>Contact</span>
        </a>
      </li>
    </ul>
  </nav>

  <!-- Mobile Overlay -->
  <div class="sidebar-overlay" [class.show]="isExpanded" (click)="toggleSidebar()"></div>
</aside>
