/* Forgot Password Styles - Matching the screenshot exactly */
:host {
  display: block;
  height: 100%;
  width: 100%;
}

.forgot-password-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  border-radius: 20px;
}

/* Left Side - Magenta Section */
.forgot-password-left {
  flex: 1;
  background: #4ECDC4; /* Teal/Turquoise color */
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: white;
  position: relative;
  overflow: hidden;
}

/* Professional contract-themed overlay */
.forgot-password-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M0 0h20L0 20z'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
}

/* Document-like pattern overlay */
.forgot-password-left::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20.83l2.83-2.83 1.41 1.41L1.41 22H0v-1.17zM0 3.07l2.83-2.83 1.41 1.41L1.41 4.24H0V3.07zm28.24 35.52l1.41-1.41 2.83 2.83V40h-1.41l-2.83-2.83zm14.18-14.17l1.41-1.41 2.83 2.83V26h-1.41l-2.83-2.83zM3.07 0l1.41 1.41-2.83 2.83H.24V3.07L3.07 0zm39.17 0l-1.41 1.41 2.83 2.83h1.17V3.07L42.24 0zm-16.93 0l-1.41 1.41 2.83 2.83h1.17V3.07L25.31 0zM20.83 0l-1.41 1.41 2.83 2.83h1.17V3.07L20.83 0zm-16.93 0l-1.41 1.41 2.83 2.83h1.17V3.07L3.9 0zM40 14.18V16h-1.17l-2.83-2.83 1.41-1.41L40 14.18zm0-11.11V4.24h-1.17l-2.83-2.83 1.41-1.41L40 3.07zm0 16.93v1.17h-1.17l-2.83-2.83 1.41-1.41L40 20zm0 16.93v1.17h-1.17l-2.83-2.83 1.41-1.41L40 36.93zM26 40h1.17l2.83-2.83-1.41-1.41L26 38.59V40zm-11.17 0h1.17l2.83-2.83-1.41-1.41-2.59 2.59V40zM14.18 40h1.17l2.83-2.83-1.41-1.41-2.59 2.59V40zM3.07 40h1.17l2.83-2.83-1.41-1.41L3.07 38.34V40z'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.1;
}

/* Position content above the background patterns */
.forgot-password-left .logo-section,
.forgot-password-left .hero-content {
  position: relative;
  z-index: 2;
}

.logo-section {
  margin-bottom: 2rem;
}

.logo {
  height: 40px;
}

.hero-content {
  margin-top: auto;
  margin-bottom: 3rem;
  position: relative;
}

.hero-content h2 {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.5px;
}

.hero-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.5;
  max-width: 400px;
  margin-bottom: 2rem;
}

.contract-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 5rem;
  opacity: 0.2;
  transform: rotate(10deg);
}

/* Right Side - Dark Section */
.forgot-password-right {
  flex: 1;
  background-color: #1a1a1a; /* Exact dark gray from the image */
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.forgot-password-form-container,
.success-container {
  max-width: 400px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.forgot-password-form {
  width: 100%;
  margin-top: 2rem;
}

.form-field {
  margin-bottom: 1.5rem;
  width: 100%;
  text-align: left;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

input:focus {
  outline: none;
  border-color: #4ECDC4; /* Teal/Turquoise color */
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.25);
}

input.is-invalid {
  border-color: #ff5252;
}

.error-message {
  color: #ff5252;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.submit-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #4ECDC4; /* Teal/Turquoise color */
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-button:hover:not(:disabled) {
  background-color: #3DACA4; /* Darker shade of teal/turquoise */
}

.submit-button:disabled {
  background-color: rgba(78, 205, 196, 0.5); /* Teal/Turquoise with opacity */
  cursor: not-allowed;
}

/* Success View Styles - Exactly matching the screenshot */
.success-container {
  padding: 2rem;
  max-width: 400px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.email-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(78, 205, 196, 0.15); /* Teal/Turquoise with opacity */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.email-icon i {
  font-size: 1.5rem;
  color: #4ECDC4; /* Teal/Turquoise color */
}

.success-container h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #10b981; /* Green color for success message exactly as in screenshot */
  margin-bottom: 1rem;
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  background-color: rgba(16, 185, 129, 0.1);
}

.success-message i {
  font-size: 1rem;
}

.instruction-text {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  font-size: 0.9rem;
  line-height: 1.5;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  width: 100%;
  margin-top: 1rem;
}

.resend-button,
.login-button {
  flex: 1;
  padding: 0.75rem;
  border-radius: 8px; /* Rounded corners exactly as in screenshot */
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resend-button {
  background-color: transparent;
  border: 1px solid #4ECDC4; /* Teal/Turquoise color */
  color: #4ECDC4; /* Teal/Turquoise color */
}

.resend-button:hover:not(:disabled) {
  background-color: rgba(78, 205, 196, 0.05); /* Teal/Turquoise with opacity */
}

.login-button {
  background-color: #4ECDC4; /* Teal/Turquoise color */
  border: none;
  color: white;
  text-decoration: none;
}

.login-button:hover {
  background-color: #3DACA4; /* Darker shade of teal/turquoise */
}

.message.error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(255, 82, 82, 0.1);
  color: #ff5252;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  font-size: 0.9rem;
  width: 100%;
  text-align: left;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .forgot-password-container {
    flex-direction: column;
  }

  .forgot-password-left, .forgot-password-right {
    flex: none;
  }

  .forgot-password-left {
    max-height: 30vh;
  }

  .forgot-password-right {
    padding: 2rem;
  }

  .action-buttons {
    flex-direction: column;
  }
}
