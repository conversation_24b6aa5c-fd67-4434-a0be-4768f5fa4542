import { Component, OnInit, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  contractId?: string;
  analysisType?: string;
}

interface Contract {
  id: string;
  displayName: string;
  fileName: string;
  uploadDate: Date;
}

interface QuickAction {
  label: string;
  action: string;
  icon: string;
}

@Component({
  selector: 'app-ai-chatbot',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './ai-chatbot.component.html',
  styleUrls: ['./ai-chatbot.component.css']
})
export class AiChatbotComponent implements OnInit, AfterViewChecked {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;

  messages: ChatMessage[] = [];
  currentMessage: string = '';
  isTyping: boolean = false;
  isLoading: boolean = false;
  selectedContractId: string | null = null;
  availableContracts: Contract[] = [];

  quickActions: QuickAction[] = [
    { label: 'Risk Analysis', action: 'analyze_risks', icon: 'warning' },
    { label: 'Compliance Check', action: 'check_compliance', icon: 'verified_user' },
    { label: 'Key Terms', action: 'extract_terms', icon: 'list_alt' },
    { label: 'Improvements', action: 'suggest_improvements', icon: 'lightbulb' }
  ];

  private shouldScrollToBottom = false;

  constructor() {}

  ngOnInit(): void {
    this.loadContracts();
    this.addWelcomeMessage();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  loadContracts(): void {
    this.isLoading = true;

    // Simulate loading contracts - replace with actual service call
    setTimeout(() => {
      this.availableContracts = [
        {
          id: '1',
          displayName: 'Service Agreement - ABC Corp',
          fileName: 'service_agreement_abc.pdf',
          uploadDate: new Date('2024-01-15')
        },
        {
          id: '2',
          displayName: 'Employment Contract - John Doe',
          fileName: 'employment_john_doe.pdf',
          uploadDate: new Date('2024-01-20')
        },
        {
          id: '3',
          displayName: 'NDA - Tech Partnership',
          fileName: 'nda_tech_partnership.pdf',
          uploadDate: new Date('2024-01-25')
        }
      ];
      this.isLoading = false;
    }, 1000);
  }

  addWelcomeMessage(): void {
    const welcomeMessage: ChatMessage = {
      id: this.generateMessageId(),
      role: 'assistant',
      content: `Hello! I'm your AI Contract Assistant. I can help you analyze contracts, identify risks, check compliance, and suggest improvements.

To get started:
1. Select a contract from the dropdown above
2. Use the quick action buttons for common analyses
3. Or ask me specific questions about your contracts

How can I assist you today?`,
      timestamp: new Date()
    };

    this.messages.push(welcomeMessage);
    this.shouldScrollToBottom = true;
  }

  selectContract(contractId: string): void {
    if (!contractId) {
      this.selectedContractId = null;
      return;
    }

    this.selectedContractId = contractId;
    const contract = this.availableContracts.find(c => c.id === contractId);

    if (contract) {
      const message: ChatMessage = {
        id: this.generateMessageId(),
        role: 'assistant',
        content: `Great! I've loaded "${contract.displayName}" for analysis. You can now use the quick action buttons or ask me specific questions about this contract.`,
        timestamp: new Date(),
        contractId: contractId
      };

      this.messages.push(message);
      this.shouldScrollToBottom = true;
    }
  }

  handleQuickAction(action: string): void {
    if (!this.selectedContractId) {
      return;
    }

    const actionMessages: { [key: string]: string } = {
      analyze_risks: 'Please analyze the potential risks in this contract.',
      check_compliance: 'Check this contract for compliance with standard regulations.',
      extract_terms: 'Extract and summarize the key terms and conditions.',
      suggest_improvements: 'Suggest improvements to make this contract more favorable.'
    };

    const message = actionMessages[action];
    if (message) {
      this.currentMessage = message;
      this.sendMessage();
    }
  }

  sendMessage(): void {
    if (!this.currentMessage.trim() || this.isTyping) {
      return;
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: this.generateMessageId(),
      role: 'user',
      content: this.currentMessage.trim(),
      timestamp: new Date(),
      contractId: this.selectedContractId || undefined
    };

    this.messages.push(userMessage);
    this.shouldScrollToBottom = true;

    // Clear input and show typing indicator
    const messageToProcess = this.currentMessage.trim();
    this.currentMessage = '';
    this.isTyping = true;

    // Simulate AI response
    setTimeout(() => {
      this.generateAIResponse(messageToProcess, userMessage);
    }, 1500 + Math.random() * 1000);
  }

  generateAIResponse(userMessage: string, originalMessage: ChatMessage): void {
    let response = '';
    let analysisType = '';

    // Generate contextual responses based on user input
    if (userMessage.toLowerCase().includes('risk')) {
      analysisType = 'Risk Analysis';
      response = this.generateRiskAnalysisResponse();
    } else if (userMessage.toLowerCase().includes('compliance')) {
      analysisType = 'Compliance Check';
      response = this.generateComplianceResponse();
    } else if (userMessage.toLowerCase().includes('terms') || userMessage.toLowerCase().includes('key')) {
      analysisType = 'Key Terms';
      response = this.generateKeyTermsResponse();
    } else if (userMessage.toLowerCase().includes('improve')) {
      analysisType = 'Improvement Suggestions';
      response = this.generateImprovementResponse();
    } else {
      response = this.generateGeneralResponse(userMessage);
    }

    const aiMessage: ChatMessage = {
      id: this.generateMessageId(),
      role: 'assistant',
      content: response,
      timestamp: new Date(),
      contractId: originalMessage.contractId,
      analysisType: analysisType || undefined
    };

    this.messages.push(aiMessage);
    this.isTyping = false;
    this.shouldScrollToBottom = true;
  }

  generateRiskAnalysisResponse(): string {
    return `Based on my analysis of the selected contract, I've identified several key risk areas:

**High Priority Risks:**
• **Liability Limitations**: The contract may have insufficient liability caps that could expose your organization to significant financial risk
• **Termination Clauses**: Unclear termination conditions could lead to disputes

**Medium Priority Risks:**
• **Payment Terms**: Extended payment periods (45+ days) may impact cash flow
• **Intellectual Property**: Some IP ownership clauses need clarification

**Recommendations:**
1. Review and negotiate liability limitations
2. Clarify termination procedures and notice periods
3. Consider adding force majeure clauses for unforeseen circumstances

Would you like me to elaborate on any of these risk areas?`;
  }

  generateComplianceResponse(): string {
    return `I've completed a compliance review of your contract. Here's what I found:

**✅ Compliant Areas:**
• GDPR data protection clauses are present and adequate
• Standard industry terms are properly included
• Required legal disclosures are present

**⚠️ Areas Needing Attention:**
• **Accessibility Compliance**: Consider adding ADA/WCAG compliance requirements
• **Regulatory Updates**: Some clauses may need updating for recent regulatory changes

**📋 Recommendations:**
1. Add specific compliance monitoring procedures
2. Include regular compliance review schedules
3. Consider adding penalty clauses for non-compliance

The contract appears to meet most standard compliance requirements, but I recommend having a legal professional review the highlighted areas.`;
  }

  generateKeyTermsResponse(): string {
    return `Here are the key terms and conditions I've extracted from the contract:

**📄 Contract Overview:**
• **Duration**: 12-month initial term with auto-renewal
• **Value**: $50,000 - $75,000 annually
• **Scope**: Professional services and consulting

**💰 Financial Terms:**
• Payment schedule: Net 30 days
• Late payment fees: 1.5% per month
• Currency: USD

**🔧 Service Terms:**
• Response time: 24-48 hours for standard requests
• Availability: 99.5% uptime guarantee
• Support hours: Business days 9 AM - 6 PM EST

**⚖️ Legal Terms:**
• Governing law: [State/Country]
• Dispute resolution: Binding arbitration
• Confidentiality: 5-year non-disclosure period

Would you like me to explain any of these terms in more detail?`;
  }

  generateImprovementResponse(): string {
    return `I've analyzed the contract and identified several improvement opportunities:

**🎯 High Impact Improvements:**
1. **Add Performance Metrics**: Include specific KPIs and service level agreements
2. **Strengthen IP Protection**: Add clearer intellectual property ownership clauses
3. **Improve Termination Terms**: Add mutual termination rights with reasonable notice

**💡 Suggested Additions:**
• **Force Majeure Clause**: Protection against unforeseen circumstances
• **Regular Review Schedule**: Quarterly contract performance reviews
• **Escalation Procedures**: Clear dispute resolution pathways

**📈 Business Benefits:**
• Reduced legal risks and exposure
• Clearer expectations for all parties
• Better protection of business interests
• Improved relationship management

**Next Steps:**
1. Prioritize high-impact improvements
2. Consult with legal counsel for implementation
3. Negotiate changes with the counterparty

Would you like me to draft specific language for any of these improvements?`;
  }

  generateGeneralResponse(userMessage: string): string {
    const responses = [
      `I understand you're asking about "${userMessage}". Based on the contract analysis, I can provide insights on various aspects including terms, risks, compliance, and potential improvements. Could you be more specific about what aspect you'd like me to focus on?`,

      `That's an interesting question about "${userMessage}". For the most accurate analysis, I'd recommend selecting a specific contract from the dropdown above. This allows me to provide detailed, context-specific insights rather than general information.`,

      `I can help you with that! To provide the most relevant analysis for "${userMessage}", please ensure you have a contract selected. I can then examine the specific clauses, terms, and conditions related to your question.`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  clearChat(): void {
    this.messages = [];
    this.selectedContractId = null;
    this.addWelcomeMessage();
  }

  getSelectedContractName(): string {
    if (!this.selectedContractId) return '';
    const contract = this.availableContracts.find(c => c.id === this.selectedContractId);
    return contract ? contract.displayName : '';
  }

  getMessageTime(timestamp: Date): string {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  formatMessageContent(content: string): string {
    // Convert markdown-like formatting to HTML
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/•/g, '&bull;')
      .replace(/\n/g, '<br>');
  }

  trackByMessageId(_index: number, message: ChatMessage): string {
    return message.id;
  }

  private generateMessageId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }

  private scrollToBottom(): void {
    try {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }
}