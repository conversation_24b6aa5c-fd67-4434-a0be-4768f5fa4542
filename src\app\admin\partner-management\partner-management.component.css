:host {
  /* Use global theme variables */
  --primary-color: var(--primary, #4ecdc4);
  --primary-dark: var(--primary-dark, #3dbdb5);
  --accent-color: var(--accent, #4ecdc4);
  --danger-color: var(--error, #dc3545);
  --success-color: var(--success, #28a745);
  --text-color: var(--text, #333333);
  --text-secondary: var(--text-secondary, #666666);
  --border-color: var(--border, #e0e0e0);
  --card-bg: var(--surface, #ffffff);
  --card-header-bg: var(--elevation2, #f5f5f5);
  --hover-bg: var(--surface-hover, #f9f9f9);
  --input-bg: var(--elevation2, #f5f5f5);
  --shadow: var(--shadow-md, 0 2px 8px rgba(0, 0, 0, 0.1));
  --border-radius: var(--radius-md, 8px);
  --transition: all 0.3s ease;
}

.partner-management-container {
  padding: 20px;
  max-width: 100%;
  margin: 0 auto;
  color: var(--text-color);
  background-color: transparent;
  transition: color var(--transition);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
  transition: border-color var(--transition);
}

.page-header h1 {
  margin-bottom: 8px;
  color: var(--primary-color);
  font-weight: 500;
  font-size: 24px;
}

.page-header p {
  color: var(--text-secondary);
  font-size: 14px;
  max-width: 600px;
  transition: color var(--transition);
}

/* Dashboard Layout */
.dashboard-layout {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 16px;
}

/* Card Styles */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  transition: background-color var(--transition),
              border-color var(--transition),
              box-shadow var(--transition);
}

.card-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: var(--card-header-bg);
  transition: background-color var(--transition),
              border-color var(--transition);
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: #ffffff;
}

.card-header h2 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  transition: color var(--transition);
}

.card-body {
  padding: 16px;
  flex: 1;
  transition: background-color var(--transition);
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-input {
  width: 100%;
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 10px 12px;
  color: var(--text-color);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s, background-color 0.2s, color 0.2s;
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
}

.form-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.add-partner-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.2s;
}

.add-partner-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.add-partner-btn:active {
  transform: translateY(1px);
}

.add-partner-btn:disabled {
  background-color: var(--border-color);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
}

.partners-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  color: var(--text-color);
  transition: color var(--transition);
}

.partners-table th {
  background-color: var(--card-header-bg);
  color: var(--text-color);
  font-weight: 500;
  text-align: left;
  padding: 10px 12px;
  font-size: 13px;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition),
              color var(--transition),
              border-color var(--transition);
}

.partners-table td {
  padding: 10px 12px;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
  font-size: 13px;
  transition: border-color var(--transition);
}

.partners-table tr:hover {
  background-color: var(--hover-bg);
  transition: background-color var(--transition);
}

.partner-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.partner-logo {
  width: 16px;
  height: 16px;
  object-fit: contain;
  background-color: white;
  border-radius: 2px;
}

.name-cell {
  min-width: 120px;
}

.description-cell {
  max-width: 200px;
  color: var(--text-secondary);
  transition: color var(--transition);
}

.description-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.action-cell {
  white-space: nowrap;
  display: flex;
  gap: 4px;
}

.action-icon {
  background: transparent;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: color var(--transition);
}

.action-icon:hover {
  color: var(--primary-color);
}

.action-icon mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  gap: 12px;
}

.loading-container p {
  color: var(--text-secondary);
  font-size: 13px;
  transition: color var(--transition);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 13px;
  transition: color var(--transition);
}

.no-data-message mat-icon {
  font-size: 32px;
  height: 32px;
  width: 32px;
  margin-bottom: 12px;
  color: var(--border-color);
  transition: color var(--transition);
}

/* Icon Styles */
.icon-primary {
  color: white;
}

/* Input field styles */
::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: var(--border-color);
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: var(--primary-color);
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
  color: var(--text-secondary);
}

::ng-deep .mat-input-element {
  color: var(--text-color);
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.5em 0;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
  padding: 0 0.75em 0 0.75em;
}

::ng-deep .mat-form-field-subscript-wrapper {
  font-size: 12px;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-wrapper {
  margin: 0;
}

::ng-deep .mat-form-field-hint-wrapper, ::ng-deep .mat-form-field-error-wrapper {
  padding: 0;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
  }

  .form-section {
    order: 1;
  }

  .list-section {
    order: 2;
  }
}

@media (max-width: 768px) {
  .partner-management-container {
    padding: 16px;
  }

  .card-header, .card-body {
    padding: 16px;
  }

  .action-buttons {
    flex-wrap: wrap;
  }

  .description-cell {
    max-width: 150px;
  }
}
