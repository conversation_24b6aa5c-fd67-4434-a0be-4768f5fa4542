/* ========== LAYOUT WRAPPER ========== */
.wrapper {
  display: flex;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--background);
  transition: background-color 0.3s ease;
}

/* ========== SIDEBAR ========== */
app-sidebar {
  width: 240px;
  background-color: var(--surface);
  border-right: 1px solid var(--border);
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
  transition: width 0.3s ease;
  z-index: 100;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
}

.dark-mode app-sidebar {
  background-color: var(--elevation1);
  border-right: 1px solid var(--border);
}

.wrapper:not(.sidebar-open) app-sidebar {
  width: 0;
  overflow: hidden;
}

/* ========== MAIN CONTENT ========== */
.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
  padding: 0;
  width: calc(100% - 80px); /* Adjust width based on sidebar */
  background-color: var(--background);
  margin-left: 80px; /* Match sidebar width */
  position: relative;
  min-height: 100vh;
}

.main-content.sidebar-open {
  margin-left: 250px;
}

.dark-mode .main-content {
  background-color: var(--background);
}

/* Navbar styling */
app-navbar {
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 99;
  background-color: rgba(var(--elevation1-rgb), 0.95);
  backdrop-filter: blur(15px);
  box-shadow: var(--shadow-md);
}

.dark-mode app-navbar {
  background-color: rgba(var(--elevation1-rgb), 0.95);
  box-shadow: var(--shadow-md);
}

/* Content wrapper */
.page-container {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 1.5rem;
  box-sizing: border-box;
  transition: all 0.3s ease;
  padding-top: 0.75rem;
  background-color: transparent;
  width: 100%;
  max-width: 1400px; /* Set a max-width */
  margin-left: auto;
  margin-right: auto;
}

/* Content container for proper spacing and layout */
.content-container {
  width: 100%;
  max-width: none; /* Remove max-width constraint */
  background-color: var(--surface);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  padding: 1.5rem;
  box-sizing: border-box;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.content-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Dark mode styling for content container */
.dark-mode .content-container {
  background-color: var(--elevation1);
  box-shadow: var(--shadow-lg);
}

/* Shift content over if sidebar is visible */
.sidebar-open .main-content {
  margin-left: 240px;
  width: calc(100% - 240px);
  max-width: none; /* Remove any max-width constraints */
}

/* Content wrapper with additional styles */
.content-wrapper {
  width: 100%;
  background-color: var(--surface-card, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  border: 1px solid var(--border);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  animation: fadeIn 0.4s ease-in-out;
  padding: 1.5rem;
}

.content-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Dark Mode Support */
.dark-mode .content-wrapper {
  background-color: var(--elevation1);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ========== DARK MODE ========== */
.dark-mode {
  background-color: var(--background);
  color: var(--text);
}

/* ========== RESPONSIVE ========== */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    width: 100%;
    max-width: none;
  }

  .main-content.sidebar-open {
    margin-left: 0;
  }

  .page-container {
    padding: 1rem;
    max-width: none;
    width: 100%;
  }

  .content-container {
    padding: 1.5rem;
    border-radius: 8px;
    max-width: 100%;
  }

  .content-wrapper {
    padding: 1.5rem;
  }

  /* Add overlay when sidebar is open on mobile */
  .sidebar-open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(3px);
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 99;
    pointer-events: none;
  }
}
