import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CommonModule } from '@angular/common';

@Component({
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCheckboxModule,

],
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.css']
})
export class SignupComponent {
onFacebookSignup() {
throw new Error('Method not implemented.');
}
onGoogleSignup() {
throw new Error('Method not implemented.');
}
message: any;
onSubmit() {
throw new Error('Method not implemented.');
}
showPassword: any;
signupForm: any;
isLoading: boolean = false;
togglePasswordVisibility() {
throw new Error('Method not implemented.');
}


}
