import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';
import { Partner, Contract } from '../components/partner-contracts/partner-contracts.component';

// Response interfaces moved from partner.model.ts
export interface PartnerResponse {
  success: boolean;
  partners: Partner[];
}

export interface ContractResponse {
  success: boolean;
  contracts: Contract[];
}

@Injectable({
  providedIn: 'root'
})
export class PartnerService {
  private apiUrl = environment.apiUrl + '/api';
  // No mock data - using real API data only

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Get auth headers for API requests
   * @returns HttpHeaders with authentication token if available
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken();
    let headers = new HttpHeaders();

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.warn('No auth token available for partner request');
    }

    return headers;
  }

  /**
   * Get all partners
   * @returns Observable of PartnerResponse
   */
  getPartners(): Observable<PartnerResponse> {
    // Always use real API data

    // Otherwise, try the API with error handling
    return this.http.get<PartnerResponse>(`${this.apiUrl}/partners`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error('Error fetching partners:', error);
        return throwError(() => new Error(`Failed to fetch partners: ${error.message}`));
      })
    );
  }

  /**
   * Get contracts for a specific partner
   * @param partnerId The ID of the partner
   * @returns Observable of ContractResponse
   */
  getPartnerContracts(partnerId: number): Observable<ContractResponse> {
    return this.http.get<ContractResponse>(`${this.apiUrl}/partners/${partnerId}/contracts`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error(`Error fetching contracts for partner ${partnerId}:`, error);
        return throwError(() => new Error(`Failed to fetch partner contracts: ${error.message}`));
      })
    );
  }

  /**
   * Get a specific contract by ID
   * @param contractId The ID of the contract
   * @returns Observable of Contract
   */
  getContract(contractId: number): Observable<Contract> {
    return this.http.get<Contract>(`${this.apiUrl}/admin/contracts/${contractId}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error(`Error fetching contract ${contractId}:`, error);
        // We can't provide mock data here without knowing which contract to return
        return throwError(() => new Error(`Failed to fetch contract: ${error.message}`));
      })
    );
  }

  /**
   * Admin: Create a new partner
   * @param partner Partner data to create
   * @returns Observable of created partner
   */
  createPartner(partner: Partial<Partner>): Observable<any> {
    return this.http.post(`${this.apiUrl}/admin/partners`, partner, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error('Error creating partner:', error);
        return throwError(() => new Error(`Failed to create partner: ${error.message}`));
      })
    );
  }

  /**
   * Admin: Update an existing partner
   * @param id Partner ID
   * @param partner Partner data to update
   * @returns Observable of updated partner
   */
  updatePartner(id: number, partner: Partial<Partner>): Observable<any> {
    return this.http.put(`${this.apiUrl}/admin/partners/${id}`, partner, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error(`Error updating partner ${id}:`, error);
        return throwError(() => new Error(`Failed to update partner: ${error.message}`));
      })
    );
  }

  /**
   * Admin: Delete a partner
   * @param id Partner ID to delete
   * @returns Observable of deletion result
   */
  deletePartner(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/admin/partners/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error(`Error deleting partner ${id}:`, error);
        return throwError(() => new Error(`Failed to delete partner: ${error.message}`));
      })
    );
  }

  /**
   * Admin: Create a new contract
   * @param partnerId Partner ID
   * @param formData FormData with contract data and file
   * @returns Observable of created contract
   */
  createContract(partnerId: number, formData: FormData): Observable<any> {
    // Add partner ID to form data
    formData.append('partner_id', partnerId.toString());

    return this.http.post(`${this.apiUrl}/admin/contracts/upload`, formData, {
      headers: this.getAuthHeaders() // Don't set Content-Type, it will be set automatically for FormData
    }).pipe(
      catchError(error => {
        console.error('Error creating contract:', error);
        return throwError(() => new Error(`Failed to create contract: ${error.message}`));
      })
    );
  }

  /**
   * Admin: Update an existing contract
   * @param id Contract ID
   * @param formData FormData with contract data and optional file
   * @returns Observable of updated contract
   */
  updateContract(id: number, formData: FormData): Observable<any> {
    return this.http.post(`${this.apiUrl}/admin/contracts/${id}`, formData, {
      headers: this.getAuthHeaders() // Don't set Content-Type, it will be set automatically for FormData
    }).pipe(
      catchError(error => {
        console.error(`Error updating contract ${id}:`, error);
        return throwError(() => new Error(`Failed to update contract: ${error.message}`));
      })
    );
  }

  /**
   * Admin: Delete a contract
   * @param id Contract ID to delete
   * @returns Observable of deletion result
   */
  deleteContract(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/admin/contracts/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error(`Error deleting contract ${id}:`, error);
        return throwError(() => new Error(`Failed to delete contract: ${error.message}`));
      })
    );
  }

  /**
   * Get contract file path
   * @param contractId The ID of the contract
   * @returns Observable with contract file path info
   */
  getContractFilePath(contractId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/contracts/${contractId}/file-path`, {
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error(`Error getting contract file path ${contractId}:`, error);
        return throwError(() => new Error(`Failed to get contract file path: ${error.message}`));
      })
    );
  }

  /**
   * Download contract file as blob
   * @param contractId The ID of the contract
   * @returns Observable of Blob containing the contract file
   */
  downloadContractFile(contractId: number): Observable<Blob> {
    // Using POST instead of GET since the backend expects POST for this endpoint
    return this.http.post(`${this.apiUrl}/admin/contracts/${contractId}/download`, {}, {
      responseType: 'blob',
      headers: this.getAuthHeaders()
    }).pipe(
      catchError(error => {
        console.error(`Error downloading contract file ${contractId}:`, error);
        return throwError(() => new Error(`Failed to download contract file: ${error.message}`));
      })
    );
  }
}
