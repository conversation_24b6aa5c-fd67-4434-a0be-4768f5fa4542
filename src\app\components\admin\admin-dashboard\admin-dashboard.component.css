/* Admin Dashboard Styles */
.admin-dashboard-section {
  padding: var(--spacing-xl);
  background-color: var(--background);
  min-height: calc(100vh - 60px);
}

.admin-content {
  max-width: 1200px;
  margin: 0 auto;
}

.admin-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border);
  text-align: center;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.admin-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text);
  margin: 0 0 var(--spacing-xs) 0;
  position: relative;
  display: inline-block;
}

.admin-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: var(--radius-sm);
}

.admin-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: var(--spacing-md) 0 0;
}

.admin-dashboard-container {
  position: relative;
  background-color: var(--surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  border: 1px solid var(--border);
  transition: all var(--transition-normal);
}

.admin-dashboard-container.loading {
  min-height: 400px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--elevation1-rgb), 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: var(--radius-lg);
  backdrop-filter: blur(4px);
}

.error-message {
  background-color: rgba(var(--error), 0.1);
  border: 1px solid rgba(var(--error), 0.3);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  animation: fadeIn 0.5s ease;
}

.error-message i {
  font-size: 2rem;
  color: var(--error);
  margin-bottom: var(--spacing-md);
}

.error-message p {
  color: var(--text);
  font-size: 1rem;
  margin-bottom: var(--spacing-md);
}

.btn-retry {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.btn-retry:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.spinner {
  border: 4px solid rgba(var(--primary-rgb), 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dashboard Controls */
.dashboard-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  background-color: var(--elevation1);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
}

.filter-container {
  display: flex;
  align-items: center;
}

.filter-container label {
  margin-right: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-container select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  background-color: var(--elevation2);
  font-size: 0.9rem;
  color: var(--text);
  transition: all var(--transition-fast);
}

.filter-container select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.btn-refresh {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.btn-refresh i {
  margin-right: var(--spacing-sm);
}

.btn-refresh:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Section Titles */
.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text);
  margin: var(--spacing-xl) 0 var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: var(--radius-sm);
}

/* Stats Cards */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background-color: var(--elevation1);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  transition: all var(--transition-normal);
  border: 1px solid var(--border);
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.05) 0%, rgba(var(--primary-rgb), 0) 100%);
  z-index: 0;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: var(--spacing-lg);
  position: relative;
  z-index: 1;
  box-shadow: 0 0 15px rgba(var(--primary-rgb), 0.2);
  transition: all var(--transition-normal);
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
}

.contract-icon {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
}

.signed-icon {
  background-color: rgba(var(--success), 0.1);
  color: var(--success);
  box-shadow: 0 0 15px rgba(var(--success), 0.2);
}

.pending-icon {
  background-color: rgba(var(--warning), 0.1);
  color: var(--warning);
  box-shadow: 0 0 15px rgba(var(--warning), 0.2);
}



.stat-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.stat-title {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  margin: var(--spacing-xs) 0 0;
  color: var(--text);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Charts */
.charts-row {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.chart-card {
  background-color: var(--elevation1);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--primary-gradient);
  z-index: 1;
}

.chart-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.full-width {
  grid-column: 1 / -1;
}

.chart-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border);
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text);
  margin: 0;
  display: flex;
  align-items: center;
}

.chart-title::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  background: var(--primary);
  border-radius: 50%;
  margin-right: var(--spacing-sm);
}

.chart-canvas-container {
  height: 250px;
  position: relative;
  padding: var(--spacing-sm) 0;
}



/* Tables */
.table-container {
  overflow-x: auto;
  margin-top: var(--spacing-md);
  border-radius: var(--radius-sm);
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  color: var(--text);
}

.data-table th, .data-table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.data-table th {
  background-color: rgba(var(--primary-rgb), 0.05);
  font-weight: 600;
  color: var(--text);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-table th:first-child {
  border-top-left-radius: var(--radius-sm);
}

.data-table th:last-child {
  border-top-right-radius: var(--radius-sm);
}

.data-table tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-sm);
}

.data-table tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-sm);
}

.data-table tr:hover td {
  background-color: rgba(var(--primary-rgb), 0.03);
}

.no-data {
  text-align: center;
  color: var(--text-secondary);
  padding: var(--spacing-lg);
  font-style: italic;
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all var(--transition-fast);
}

.badge-admin {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
}

.badge-user {
  background-color: rgba(var(--secondary-rgb), 0.1);
  color: var(--secondary);
  border: 1px solid rgba(var(--secondary-rgb), 0.2);
}

.badge-success {
  background-color: rgba(var(--success), 0.1);
  color: var(--success);
  border: 1px solid rgba(var(--success), 0.2);
}

.badge-warning {
  background-color: rgba(var(--warning), 0.1);
  color: var(--warning);
  border: 1px solid rgba(var(--warning), 0.2);
}

.badge-pending {
  background-color: rgba(var(--info), 0.1);
  color: var(--info);
  border: 1px solid rgba(var(--info), 0.2);
}

/* Admin Actions */
.admin-actions {
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  animation: fadeIn 0.5s ease;
  animation-delay: 0.3s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.action-button {
  background-color: var(--elevation1);
  color: var(--text);
  border: 1px solid var(--border);
  padding: var(--spacing-xl);
  border-radius: var(--radius-md);
  cursor: pointer;
  text-decoration: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 600;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.action-button i {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
  color: var(--primary);
  position: relative;
  z-index: 2;
  transition: all var(--transition-normal);
}

.action-button span {
  position: relative;
  z-index: 2;
  transition: all var(--transition-normal);
}

.action-button:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.action-button:hover::before {
  opacity: 0.05;
}

.action-button:hover i {
  transform: scale(1.2);
  color: var(--primary-light);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .admin-dashboard-section {
    padding: var(--spacing-md);
  }

  .admin-dashboard-container {
    padding: var(--spacing-md);
  }

  .charts-row {
    grid-template-columns: 1fr;
  }

  .stats-cards {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .admin-header {
    padding-top: 60px;
  }

  .admin-title {
    font-size: 1.8rem;
  }

  .admin-subtitle {
    font-size: 1rem;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .dashboard-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-container {
    margin-bottom: var(--spacing-md);
    width: 100%;
  }

  .filter-container select {
    width: 100%;
  }

  .btn-refresh {
    width: 100%;
    justify-content: center;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    margin-right: var(--spacing-md);
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 1.3rem;
  }

  .chart-title {
    font-size: 1rem;
  }
}
