import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpEvent } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class ContractSigningService {
  private apiUrl = environment.apiUrl + '/api';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Get auth headers for API requests
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken();
    let headers = new HttpHeaders();

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.error('No auth token available for contract signing request');
    }

    return headers;
  }

  /**
   * Sign a PDF contract
   * @param formData Form data containing contract details and file
   * @returns Observable with the signed contract response
   */
  signPdfContract(formData: FormData): Observable<any> {
    console.log('Sending PDF contract signing request');

    // Get auth headers
    const authHeaders = this.getAuthHeaders();

    // Log what we're sending to help with debugging
    console.log('Form data entries for PDF signing:');
    formData.forEach((value, key) => {
      if (key === 'pdf_file') {
        const file = value as File;
        console.log(`${key}: ${file.name} (${file.type}, ${file.size} bytes)`);
      } else if (key === 'signature_data') {
        console.log(`${key}: [Signature data string]`);
      } else {
        console.log(`${key}: ${value}`);
      }
    });

    return this.http.post(`${this.apiUrl}/contrats/sign`, formData, {
      headers: authHeaders,
      reportProgress: true,
      observe: 'events',
      responseType: 'blob' as 'json', // Handle blob response for file download
      withCredentials: true // Include cookies in the request
    }).pipe(
      map((event: HttpEvent<any>) => {
        // For HttpEventType.Response, convert the blob to a success event
        if (event.type === 4) { // HttpEventType.Response
          console.log('PDF document signed successfully, received file download');

          // If we have a blob response, create a download
          if (event.body instanceof Blob) {
            // Get the file from the form data to determine the filename
            const file = formData.get('pdf_file') as File;
            if (file) {
              // Create a filename for the download that preserves the original extension
              const extension = file.name.split('.').pop() || 'pdf';
              const filename = file.name.replace(/\.[^/.]+$/, '') + '_signed.' + extension;

              console.log('Original file extension:', extension);
              console.log('Generated filename for download:', filename);

              // IMPORTANT: Always determine MIME type from file extension
              // This ensures the browser knows how to handle the file
              let mimeType = 'application/octet-stream';

              // Set the correct MIME type based on file extension
              if (extension.toLowerCase() === 'pdf') {
                mimeType = 'application/pdf';
              } else if (extension.toLowerCase() === 'doc') {
                mimeType = 'application/msword';
              } else if (extension.toLowerCase() === 'docx') {
                mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
              }

              // Log the MIME type we're using
              console.log('Using MIME type based on file extension:', mimeType);

              // Check if Content-Type header matches our expected MIME type
              if (event.headers && event.headers.get('Content-Type')) {
                const headerContentType = event.headers.get('Content-Type') || '';
                console.log('Content-Type from response headers:', headerContentType);

                // If header doesn't match our expected MIME type, log a warning but still use our extension-based type
                if (headerContentType !== mimeType && headerContentType !== 'application/octet-stream') {
                  console.warn('Content-Type header does not match expected MIME type for extension', {
                    extension: extension,
                    expectedMimeType: mimeType,
                    headerContentType: headerContentType
                  });
                }
              }

              // Create a new blob with the correct MIME type
              const newBlob = new Blob([event.body], { type: mimeType });
              console.log('Created blob with MIME type:', mimeType, 'Size:', newBlob.size, 'bytes');

              // Create a download link
              const url = window.URL.createObjectURL(newBlob);
              const a = document.createElement('a');
              a.href = url;
              a.download = filename;
              document.body.appendChild(a);
              a.click();
              window.URL.revokeObjectURL(url);
              document.body.removeChild(a);

              console.log(`Downloaded file: ${filename} with MIME type: ${mimeType}`);
            }
          }

          // Create a success response event
          return {
            type: 4,
            body: {
              success: true,
              message: 'Document signed successfully'
            }
          };
        }
        return event;
      }),
      catchError(error => {
        console.error('Error signing PDF document:', error);
        return throwError(() => new Error('Failed to sign PDF document. Please try again.'));
      })
    );
  }

  /**
   * Sign a Word document
   * @param formData Form data containing contract details and file
   * @returns Observable with the signed contract response
   */
  signWordDocument(formData: FormData): Observable<any> {
    console.log('Sending Word document signing request');

    // Get auth headers
    const authHeaders = this.getAuthHeaders();

    // First, log what we're sending to help with debugging
    console.log('Form data entries for Word signing:');
    formData.forEach((value, key) => {
      if (key === 'pdf_file') {
        const file = value as File;
        console.log(`${key}: ${file.name} (${file.type}, ${file.size} bytes)`);
      } else if (key === 'signature_data') {
        console.log(`${key}: [Signature data string]`);
      } else {
        console.log(`${key}: ${value}`);
      }
    });

    // Use the Word-specific endpoint
    return this.http.post(`${this.apiUrl}/contrats/sign/word`, formData, {
      headers: authHeaders,
      reportProgress: true,
      observe: 'events',
      responseType: 'blob' as 'json', // Handle blob response for file download
      withCredentials: true // Include cookies in the request
    }).pipe(
      map((event: HttpEvent<any>) => {
        // For HttpEventType.Response, convert the blob to a success event
        if (event.type === 4) { // HttpEventType.Response
          console.log('Word document signed successfully, received file download');

          // If we have a blob response, create a download
          if (event.body instanceof Blob) {
            // Get the file from the form data to determine the filename
            const file = formData.get('pdf_file') as File;
            if (file) {
              // Create a filename for the download that preserves the original extension
              const extension = file.name.split('.').pop() || 'docx';
              const filename = file.name.replace(/\.[^/.]+$/, '') + '_signed.' + extension;

              console.log('Original file extension:', extension);
              console.log('Generated filename for download:', filename);

              // IMPORTANT: Always determine MIME type from file extension
              // This ensures the browser knows how to handle the file
              let mimeType = 'application/octet-stream';

              // Set the correct MIME type based on file extension
              if (extension.toLowerCase() === 'pdf') {
                mimeType = 'application/pdf';
              } else if (extension.toLowerCase() === 'doc') {
                mimeType = 'application/msword';
              } else if (extension.toLowerCase() === 'docx') {
                mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
              }

              // Log the MIME type we're using
              console.log('Using MIME type based on file extension:', mimeType);

              // Check if Content-Type header matches our expected MIME type
              if (event.headers && event.headers.get('Content-Type')) {
                const headerContentType = event.headers.get('Content-Type') || '';
                console.log('Content-Type from response headers:', headerContentType);

                // If header doesn't match our expected MIME type, log a warning but still use our extension-based type
                if (headerContentType !== mimeType && headerContentType !== 'application/octet-stream') {
                  console.warn('Content-Type header does not match expected MIME type for extension', {
                    extension: extension,
                    expectedMimeType: mimeType,
                    headerContentType: headerContentType
                  });
                }
              }

              // Create a new blob with the correct MIME type
              const newBlob = new Blob([event.body], { type: mimeType });
              console.log('Created blob with MIME type:', mimeType, 'Size:', newBlob.size, 'bytes');

              // Create a download link
              const url = window.URL.createObjectURL(newBlob);
              const a = document.createElement('a');
              a.href = url;
              a.download = filename;
              document.body.appendChild(a);
              a.click();
              window.URL.revokeObjectURL(url);
              document.body.removeChild(a);

              console.log(`Downloaded file: ${filename} with MIME type: ${mimeType}`);
            }
          }

          // Create a success response event
          return {
            type: 4,
            body: {
              success: true,
              message: 'Document signed successfully'
            }
          };
        }
        return event;
      }),
      catchError(error => {
        console.error('Error signing Word document:', error);
        // More descriptive error message
        let errorMsg = 'Failed to sign Word document. ';

        if (error.status === 404) {
          errorMsg += 'The Word document signing endpoint was not found. ';
        } else if (error.status === 400) {
          errorMsg += 'The server rejected the request. ';
        } else if (error.status === 401 || error.status === 403) {
          errorMsg += 'Authentication or authorization error. ';
        } else if (error.status === 500) {
          errorMsg += 'Server error occurred. ';
        }

        errorMsg += 'Please try again or contact support.';
        return throwError(() => new Error(errorMsg));
      })
    );
  }
}
