<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class SocialiteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // This is a placeholder service provider to replace <PERSON><PERSON>\Socialite\SocialiteServiceProvider
        // It doesn't actually provide any functionality, just prevents errors
        $this->app->singleton('socialite', function ($app) {
            return new class {
                public function driver() {
                    return $this;
                }

                public function stateless() {
                    return $this;
                }

                public function redirect() {
                    return $this;
                }

                public function getTargetUrl() {
                    return '';
                }

                public function user() {
                    return new \stdClass();
                }

                public function extend() {
                    return $this;
                }

                public function buildProvider() {
                    return $this;
                }

                public function setHttpClient() {
                    return $this;
                }
            };
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
