<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\Auth\GoogleController;
use App\Http\Controllers\Auth\FacebookController;
use App\Http\Controllers\Api\SignedContractController;
use App\Http\Controllers\Api\UnsignedContractController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\ContactMessageController;
use App\Http\Controllers\Api\PartnerController;
use App\Http\Controllers\Api\ContractController;


// Auth routes
Route::prefix('auth')->group(function () {
    // Google OAuth routes - support both URL patterns
    Route::get('google', [GoogleController::class, 'redirectToGoogle']);
    Route::get('google/redirect', [GoogleController::class, 'redirectToGoogle']);
    Route::get('google/callback', [GoogleController::class, 'handleGoogleCallback']);

    // Facebook OAuth routes
    Route::get('facebook', [FacebookController::class, 'redirectToFacebook']);
    Route::get('facebook/redirect', [FacebookController::class, 'redirectToFacebook']);
    Route::get('facebook/callback', [FacebookController::class, 'handleFacebookCallback']);

    // Password Reset Routes
    Route::post('check-email', [ForgotPasswordController::class, 'checkEmail']);
    Route::post('forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail']);
    Route::post('reset-password', [ResetPasswordController::class, 'reset']);
    Route::get('reset-password/{token}', [ResetPasswordController::class, 'validateToken']);
});

// Protected routes
Route::middleware(['auth:sanctum'])->group(function () {
    // User routes
    Route::get('/profile', [UserController::class, 'getUser']);
    Route::post('/update-settings', [UserController::class, 'updateSettings']);
    Route::post('/accept-terms', [UserController::class, 'acceptTerms']);
    Route::put('/user/password', [UserController::class, 'updatePassword']);
    Route::get('/search-users', [UserController::class, 'search']);
    Route::get('/user/{user_id}', [UserController::class, 'getUser']);
    Route::post('/upload-profile-image', [UserController::class, 'uploadProfileImage']);

    // User dashboard route
    Route::get('/user-dashboard-stats', [App\Http\Controllers\Api\UserDashboardController::class, 'getDashboardStats']);

    // File upload routes - require authentication
    Route::post('/upload', [App\Http\Controllers\FileUploadController::class, 'upload']);
    Route::get('/files', [App\Http\Controllers\FileUploadController::class, 'listFiles']);
    Route::get('/files/download/{filename}', [App\Http\Controllers\FileUploadController::class, 'download']);
    Route::delete('/files/deleteAll', [App\Http\Controllers\FileUploadController::class, 'deleteAll']);
    Route::delete('/files/delete/{filename}', [App\Http\Controllers\FileUploadController::class, 'deleteFile']);
    Route::get('/files/extracted-data/{filename}', [App\Http\Controllers\FileUploadController::class, 'getExtractedData']);
    Route::get('/contracts/{contractId}/extracted-data', [App\Http\Controllers\FileUploadController::class, 'getContractExtractedData']);

    // Document generation routes - require authentication
    Route::post('/documents/generate', [App\Http\Controllers\Api\DocumentGenerationController::class, 'generate']);
    Route::get('/documents/download/{filename}', [App\Http\Controllers\Api\DocumentGenerationController::class, 'download']);

    // Contract signing routes - require authentication
    Route::post('/contrats/sign', [App\Http\Controllers\Api\ContractSigningController::class, 'signPdf']);
    Route::post('/contrats/sign/word', [App\Http\Controllers\Api\ContractSigningController::class, 'signWord']);

    // Signed Contract routes - require authentication
    Route::prefix('signed-contracts')->group(function () {
        Route::get('/', [SignedContractController::class, 'index']);
        Route::get('/{id}', [SignedContractController::class, 'show']);
        Route::get('/{id}/download', [SignedContractController::class, 'download']);
        Route::post('/', [SignedContractController::class, 'store']);
    });

    // Unsigned Contract routes - require authentication
    Route::prefix('unsigned-contracts')->group(function () {
        Route::get('/', [UnsignedContractController::class, 'index']);
        Route::get('/{id}', [UnsignedContractController::class, 'show']);
        Route::get('/{id}/download', [UnsignedContractController::class, 'download']);
        Route::get('/{id}/view', [UnsignedContractController::class, 'view']);
        Route::delete('/{id}', [UnsignedContractController::class, 'destroy']);
    });

    // Admin routes - protected by admin middleware
    Route::prefix('admin')->middleware('admin')->group(function () {
        Route::get('/dashboard-stats', [AdminController::class, 'getDashboardStats']);
        Route::get('/users', [AdminController::class, 'getUsers']);
        Route::get('/users/{id}', [AdminController::class, 'getUser']);
        Route::post('/users', [AdminController::class, 'createUser']);
        Route::put('/users/{id}', [AdminController::class, 'updateUser']);
        Route::delete('/users/{id}', [AdminController::class, 'deleteUser']);

        // Partner management routes
        Route::apiResource('partners', PartnerController::class);

        // Contract management routes
        Route::apiResource('contracts', ContractController::class);
        Route::post('/contracts/upload', [ContractController::class, 'store']);
    });

    // Partner routes (read-only for regular users)
    Route::get('/partners', [PartnerController::class, 'index']);
    Route::get('/partners/{id}', [PartnerController::class, 'show']);
    Route::get('/partners/{partnerId}/contracts', [ContractController::class, 'getPartnerContracts']);

});

// Handle OPTIONS preflight requests for all routes
Route::options('/{any}', function () {
    return response()->json(['status' => 'success'], 200);
})->where('any', '.*');

// Public routes
Route::post('/register', [UserController::class, 'register']);
Route::post('/login', [UserController::class, 'login']);
Route::get('/avatar/{filename}', [UserController::class, 'getAvatar']);

// No public routes for file upload, document generation, or contracts
// All these routes are now protected by authentication middleware

// Contact message routes
Route::prefix('contact')->group(function () {
    // Public route for submitting contact messages
    Route::post('/submit', [ContactMessageController::class, 'store']);

    // Admin-only routes for managing contact messages
    Route::middleware(['auth:sanctum', 'admin'])->group(function () {
        Route::get('/messages', [ContactMessageController::class, 'index']);
        Route::get('/messages/unread-count', [ContactMessageController::class, 'getUnreadCount']);
        Route::put('/messages/{id}/mark-read', [ContactMessageController::class, 'markAsRead']);
        Route::put('/messages/mark-all-read', [ContactMessageController::class, 'markAllAsRead']);
        Route::delete('/messages/{id}', [ContactMessageController::class, 'destroy']);
    });
});

// Health check endpoint
Route::get('/health', function() {
    return response()->json(['status' => 'ok', 'timestamp' => now()]);
});

// Test routes have been removed

