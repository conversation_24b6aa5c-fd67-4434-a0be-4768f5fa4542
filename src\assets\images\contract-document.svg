<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="450" viewBox="0 0 600 450" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1a1a1a"/>
      <stop offset="100%" stop-color="#2d2d2d"/>
    </linearGradient>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4ECDC4"/>
      <stop offset="100%" stop-color="#3DACA4"/>
    </linearGradient>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="4" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Main Background -->
  <rect width="600" height="450" rx="20" fill="url(#bgGradient)"/>

  <!-- Decorative Elements -->
  <circle cx="50" cy="400" r="100" fill="url(#primaryGradient)" opacity="0.05"/>
  <circle cx="550" cy="50" r="80" fill="url(#primaryGradient)" opacity="0.05"/>
  <path d="M0,200 Q150,150 300,200 T600,200" stroke="url(#primaryGradient)" stroke-width="1" fill="none" opacity="0.1"/>
  <path d="M0,250 Q150,300 300,250 T600,250" stroke="url(#primaryGradient)" stroke-width="1" fill="none" opacity="0.1"/>

  <!-- Document Stack -->
  <g transform="translate(300, 225)" filter="url(#shadow)">
    <!-- Bottom Document -->
    <rect x="-110" y="-30" width="220" height="280" rx="10" fill="#333333" transform="rotate(-8)"/>

    <!-- Middle Document -->
    <rect x="-110" y="-40" width="220" height="280" rx="10" fill="#444444" transform="rotate(-4)"/>

    <!-- Top Document -->
    <rect x="-110" y="-50" width="220" height="280" rx="10" fill="#555555" transform="rotate(0)"/>

    <!-- Document Content Lines -->
    <g transform="translate(-80, -20)">
      <!-- Header -->
      <rect x="0" y="0" width="160" height="20" rx="3" fill="url(#primaryGradient)" opacity="0.8"/>

      <!-- Text Lines -->
      <rect x="0" y="35" width="160" height="5" rx="2" fill="#777777"/>
      <rect x="0" y="50" width="140" height="5" rx="2" fill="#777777"/>
      <rect x="0" y="65" width="160" height="5" rx="2" fill="#777777"/>
      <rect x="0" y="80" width="120" height="5" rx="2" fill="#777777"/>
      <rect x="0" y="95" width="160" height="5" rx="2" fill="#777777"/>

      <!-- Signature Area -->
      <rect x="0" y="120" width="160" height="1" fill="#777777"/>
      <path d="M20,115 C30,125 40,105 50,115 C60,125 70,110 80,115" stroke="url(#primaryGradient)" stroke-width="2" fill="none"/>

      <!-- Form Fields -->
      <rect x="0" y="140" width="70" height="15" rx="3" stroke="#777777" stroke-width="1" fill="none"/>
      <rect x="90" y="140" width="70" height="15" rx="3" stroke="#777777" stroke-width="1" fill="none"/>
      <rect x="0" y="165" width="160" height="15" rx="3" stroke="#777777" stroke-width="1" fill="none"/>

      <!-- Checkbox -->
      <rect x="0" y="190" width="15" height="15" rx="2" stroke="#777777" stroke-width="1" fill="none"/>
      <path d="M3,198 L7,202 L12,193" stroke="url(#primaryGradient)" stroke-width="2" fill="none"/>
      <rect x="25" y="190" width="135" height="5" rx="2" fill="#777777"/>
    </g>

    <!-- Document Corner Fold -->
    <path d="M110,-50 L110,-20 L80,-50 Z" fill="#444444"/>
    <path d="M110,-50 L110,-20 L80,-50 Z" fill="none" stroke="#666666" stroke-width="1"/>
  </g>

  <!-- Magnifying Glass -->
  <g transform="translate(450, 150)" filter="url(#shadow)">
    <circle cx="0" cy="0" r="40" fill="none" stroke="url(#primaryGradient)" stroke-width="8" opacity="0.7"/>
    <line x1="30" y1="30" x2="60" y2="60" stroke="url(#primaryGradient)" stroke-width="8" stroke-linecap="round" opacity="0.7"/>
  </g>

  <!-- Pen -->
  <g transform="translate(150, 150) rotate(45)" filter="url(#shadow)">
    <rect x="-10" y="-60" width="20" height="120" rx="5" fill="url(#primaryGradient)" opacity="0.7"/>
    <path d="M-10,-60 L10,-60 L0,-80 Z" fill="url(#primaryGradient)" opacity="0.7"/>
    <rect x="-8" y="30" width="16" height="10" fill="#dddddd" opacity="0.9"/>
  </g>

  <!-- Text -->
  <g filter="url(#shadow)">
    <text x="300" y="380" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">DOCUMENT MANAGEMENT</text>
    <text x="300" y="415" font-family="Arial, sans-serif" font-size="18" font-weight="normal" fill="white" text-anchor="middle" opacity="0.8">Streamline Your Contract Workflow</text>
  </g>
</svg>
