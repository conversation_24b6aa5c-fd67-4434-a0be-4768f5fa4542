import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private darkMode = new BehaviorSubject<boolean>(false);
  isDarkMode$ = this.darkMode.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme) {
        this.darkMode.next(savedTheme === 'dark');
      } else {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        this.darkMode.next(prefersDark);
      }
      this.applyTheme();
    }
  }

  toggleTheme(): void {
    if (isPlatformBrowser(this.platformId)) {
      const newValue = !this.darkMode.getValue();
      this.darkMode.next(newValue);
      localStorage.setItem('theme', newValue ? 'dark' : 'light');
      this.applyTheme();
    }
  }

  setDarkMode(isDark: boolean): void {
    if (isPlatformBrowser(this.platformId)) {
      this.darkMode.next(isDark);
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
      this.applyTheme();
    }
  }

  private applyTheme(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (this.darkMode.getValue()) {
        document.body.classList.add('dark-mode');
      } else {
        document.body.classList.remove('dark-mode');
      }
    }
  }
}
