import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { Router } from '@angular/router';
import { BrowserStorageService } from '../../services/browser-storage.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class ProfileComponent implements OnInit {
  profileForm: FormGroup;
  isLoading = false;
  error: string | null = null;
  message: string | null = null;
  currentUser: any = null;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private storage: BrowserStorageService
  ) {
    this.profileForm = this.fb.group({
      firstname: ['', Validators.required],
      lastname: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.maxLength(20)]]
    });
  }

  ngOnInit(): void {
    console.log('Profile component initialized');

    // Listen for auth state changes
    window.addEventListener('auth-state-changed', (event: any) => {
      console.log('Auth state changed event received:', event.detail);
      if (event.detail && event.detail.isLoggedIn) {
        console.log('Auth state is now logged in, loading profile');
        this.loadUserProfile();
      }
    });

    this.loadUserProfile();
  }

  loadUserProfile(): void {
    console.log('Loading user profile...');
    this.isLoading = true;
    this.error = null;

    // Use the same storage keys as the auth service
    const STORAGE_KEYS = {
      USER: 'user',
      TOKEN: 'token'
    };

    // Check if user is logged in with detailed logging
    const isLoggedIn = this.authService.isLoggedIn();
    const token = this.storage.getItem(STORAGE_KEYS.TOKEN);
    const userStr = this.storage.getItem(STORAGE_KEYS.USER);

    console.log('Auth check result:', isLoggedIn);
    console.log('Token in storage:', token ? `exists (length: ${token.length})` : 'not found');
    console.log('User in storage:', userStr ? 'exists' : 'not found');

    // If we have user data in storage, try to parse it
    if (userStr) {
      try {
        const userData = JSON.parse(userStr);
        console.log('User data from storage:', userData);
      } catch (e) {
        console.error('Error parsing user data from storage:', e);
      }
    }

    // If we have a token in storage but isLoggedIn is false, try to recover
    if (!isLoggedIn && token) {
      console.log('Token exists in storage but auth state is false, attempting to recover');
      this.authService.setToken(token);

      // If we have user data, set it
      if (userStr) {
        try {
          const userData = JSON.parse(userStr);
          this.authService.setCurrentUser(userData);
        } catch (e) {
          console.error('Error parsing user data from storage during recovery:', e);
        }
      }

      // Check again after recovery attempt
      if (this.authService.isLoggedIn()) {
        console.log('Successfully recovered auth state from storage token');
      } else {
        console.error('Failed to recover auth state despite token in storage');
      }
    }

    // Check again after potential recovery
    if (!this.authService.isLoggedIn()) {
      console.error('User is not logged in after recovery attempts');
      this.error = 'You are not logged in. Please sign in to view your profile.';
      this.isLoading = false;

      // Try to recover the session if possible
      this.tryRecoverSession();
      return;
    }

    // Double check if we have a token but no user data
    if (!userStr && this.authService.getAuthToken()) {
      console.log('Token exists but no user data, attempting to fetch user data');
      this.fetchUserData();
      return;
    }

    this.authService.getCurrentUser().subscribe({
      next: (userData) => {
        console.log('Raw user data received:', userData);

        if (!userData) {
          console.error('No user data available');

          // Try to manually get user data from localStorage using the auth service
          const storedUser = this.authService.loadUserFromLocalStorage();
          if (storedUser) {
            console.log('Successfully loaded user from localStorage');
            this.currentUser = storedUser;
            this.updateFormWithUserData(storedUser);
            this.isLoading = false;
            return;
          }

          this.error = 'No user data available. Please sign in again.';
          this.isLoading = false;
          return;
        }

        this.currentUser = userData;
        this.updateFormWithUserData(userData);
        this.isLoading = false;
      },
      error: (err) => {
        this.error = 'Failed to load profile data. Please try again later.';
        this.isLoading = false;
        console.error('Error loading profile:', err);
      }
    });
  }

  updateFormWithUserData(user: any): void {
    if (!user) return;

    console.log('Updating form with user data:', user);

    const firstname = user.firstname || user.firstName || '';
    const lastname = user.lastname || user.lastName || '';
    const email = user.email || '';
    const phone = user.phone || '';

    console.log('Extracted values:', { firstname, lastname, email, phone });

    this.profileForm.patchValue({
      firstname,
      lastname,
      email,
      phone
    });
  }

  onSubmit(): void {
    if (this.profileForm.valid) {
      this.isLoading = true;
      this.error = null;
      this.message = null;

      const updateData = {
        firstname: this.profileForm.get('firstname')?.value?.trim(),
        lastname: this.profileForm.get('lastname')?.value?.trim(),
        email: this.profileForm.get('email')?.value?.trim(),
        phone: this.profileForm.get('phone')?.value?.trim()
      };

      console.log('Sending profile update with phone:', updateData);
      console.log('Phone value:', this.profileForm.get('phone')?.value);

      this.authService.updateProfile(updateData).subscribe({
        next: (response: any) => {
          console.log('Profile update response:', response);
          if (response.user) {
            this.currentUser = response.user;
            this.updateFormWithUserData(response.user);
          }
          this.message = 'Profile updated successfully';
          this.isLoading = false;
        },
        error: (err) => {
          this.error = 'Failed to update profile';
          this.isLoading = false;
          console.error('Error updating profile:', err);
        }
      });
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];

      if (!file.type.startsWith('image/')) {
        this.error = 'Please select an image file';
        return;
      }

      // Check file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        this.error = 'Image size should be less than 5MB';
        return;
      }

      const formData = new FormData();
      formData.append('avatar', file);

      this.isLoading = true;
      this.error = null;
      this.message = null;

      this.authService.updateAvatar(formData).subscribe({
        next: (response: any) => {
          console.log('Avatar update response:', response);
          if (response.user) {
            this.currentUser = response.user;
          }
          this.message = 'Profile picture updated successfully';
          this.isLoading = false;
        },
        error: (err) => {
          this.error = 'Failed to update profile picture';
          this.isLoading = false;
          console.error('Error updating avatar:', err);
        }
      });
    }
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = 'assets/images/default-avatar.png';
    }
  }

  getDisplayName(): string {
    if (this.currentUser) {
      console.log('Getting display name for user:', this.currentUser);
      const firstname = this.currentUser.firstname || this.currentUser.firstName || '';
      const lastname = this.currentUser.lastname || this.currentUser.lastName || '';
      const fullName = `${firstname} ${lastname}`.trim();
      return fullName || 'User';
    }
    return 'User';
  }

  getAvatarUrl(): string {
    if (!this.currentUser) {
      console.log('No current user, returning default avatar');
      return 'assets/images/default-avatar.png';
    }

    const avatar = this.currentUser.avatar || this.currentUser.avatar_url;
    console.log('Getting avatar URL for user:', this.currentUser.email, 'avatar:', avatar);

    if (!avatar) {
      console.log('No avatar found, returning default');
      return 'assets/images/default-avatar.png';
    }

    // If it's already a full URL (Google, Facebook, etc.)
    if (avatar.startsWith('http')) {
      console.log('Using full URL avatar:', avatar);
      return avatar;
    }

    // Use the auth service to get the proper URL for uploaded files
    const constructedUrl = this.authService.getAvatarUrl(avatar);
    console.log('Constructed avatar URL:', constructedUrl);
    return constructedUrl;
  }

  onImageLoad(event: Event): void {
    const img = event.target as HTMLImageElement;
    console.log('Avatar image loaded successfully:', img.src);
  }

  getAccountStatus(): string {
    if (this.currentUser?.is_active) {
      return 'Active';
    }
    return 'Inactive';
  }

  getSecurityLevel(): string {
    // Check if user has avatar (basic verification)
    const hasAvatar = this.currentUser?.avatar || this.currentUser?.avatar_url;
    const hasPhone = this.currentUser?.phone;

    if (hasAvatar && hasPhone) {
      return 'High';
    } else if (hasAvatar || hasPhone) {
      return 'Medium';
    }
    return 'Basic';
  }

  getJoinedDate(): string {
    if (this.currentUser?.created_at) {
      const date = new Date(this.currentUser.created_at);
      return date.getFullYear().toString();
    }
    return '2024';
  }

  resetForm(): void {
    if (this.currentUser) {
      this.updateFormWithUserData(this.currentUser);
      this.message = 'Form reset to original values';
      setTimeout(() => {
        this.message = null;
      }, 3000);
    }
  }

  navigateToDocuments(): void {
    this.router.navigate(['/dashboard']);
  }

  navigateToSecurity(): void {
    this.router.navigate(['/settings']);
  }

  navigateToNotifications(): void {
    this.router.navigate(['/notifications']);
  }

  // Helper method to manually set token and user data
  manuallySetAuthData(): void {
    try {
      // Check for token in storage with detailed logging
      console.log('Checking storage for auth data...');

      // Use the same storage keys as the auth service
      const STORAGE_KEYS = {
        USER: 'user',
        TOKEN: 'token'
      };

      // Check for token using the constant from auth service
      const token = this.storage.getItem(STORAGE_KEYS.TOKEN);
      console.log('Token found in storage:', token ? 'Yes (length: ' + token.length + ')' : 'No');

      if (!token) {
        console.error('No token found in storage');
        return;
      }

      // Check for user data in storage
      const userStr = this.storage.getItem(STORAGE_KEYS.USER);
      console.log('User data found in storage:', userStr ? 'Yes' : 'No');

      if (!userStr) {
        console.error('No user data found in storage');
        return;
      }

      const userData = JSON.parse(userStr);
      console.log('Manually setting auth data with token and user:', userData);

      // Use the auth service to set the token and user data
      this.authService.manualLogin(userData, token);

      // Reload the profile
      this.loadUserProfile();
    } catch (error) {
      console.error('Error manually setting auth data:', error);
    }
  }

  // Try to recover the session if possible
  private tryRecoverSession(): void {
    console.log('Attempting to recover session...');

    // Check if we have a token in any form
    const STORAGE_KEYS = {
      USER: 'user',
      TOKEN: 'token'
    };

    const token = this.storage.getItem(STORAGE_KEYS.TOKEN);
    if (token) {
      console.log('Found token in storage, attempting to use it');
      this.authService.setToken(token);

      // Try to get user data
      const userStr = this.storage.getItem(STORAGE_KEYS.USER);
      if (userStr) {
        try {
          const userData = JSON.parse(userStr);
          this.authService.setCurrentUser(userData);
          console.log('Recovered user data from storage');

          // Reload the profile after a short delay
          setTimeout(() => {
            this.loadUserProfile();
          }, 500);
          return;
        } catch (e) {
          console.error('Error parsing user data from storage:', e);
        }
      }

      // If we have a token but no user data, try to fetch the user data
      this.fetchUserData();
    } else {
      console.log('No token found, redirecting to sign in');
      this.redirectToSignIn();
    }
  }

  // Fetch user data from the API
  private fetchUserData(): void {
    console.log('Fetching user data from API...');
    this.isLoading = true;

    this.authService.fetchCurrentUser().subscribe({
      next: (userData) => {
        if (userData) {
          console.log('Successfully fetched user data from API:', userData);
          this.currentUser = userData;
          this.updateFormWithUserData(userData);
          this.isLoading = false;
        } else {
          console.warn('Failed to fetch user data from API');
          this.error = 'Failed to load user profile. Please try again.';
          this.isLoading = false;
          this.redirectToSignIn();
        }
      },
      error: (error) => {
        console.error('Error fetching user data from API:', error);
        this.error = 'Error loading profile: ' + (error.message || 'Unknown error');
        this.isLoading = false;
        this.redirectToSignIn();
      }
    });
  }

  // Redirect to sign in page
  redirectToSignIn(): void {
    console.log('Redirecting to sign in page');
    this.router.navigate(['/signin']);
  }
}

