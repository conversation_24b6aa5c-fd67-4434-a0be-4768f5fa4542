import { Routes } from '@angular/router';
import { SignupComponent } from './components/signup/signup.component';
import { SigninComponent } from './components/signin/sign-in.component';
import { HomeComponent } from './components/home/<USER>';
import { ContactComponent } from './components/contact/contact.component';
import { SettingsComponent } from './components/settings/settings.component';
import { ProfileComponent } from './components/profile/profile.component';
import { ForgotPasswordComponent } from './components/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './components/reset-password/reset-password.component';
import { AuthCallbackComponent } from './components/auth-callback/auth-callback.component';
import { FileUploadComponent } from './components/file-upload/file-upload.component';
import { SecurityComponent } from './components/security/security.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { AdminDashboardComponent } from './components/admin/admin-dashboard/admin-dashboard.component';
import { UserManagementComponent } from './components/admin/user-management/user-management.component';
import { NotificationsComponent } from './components/admin/notifications/notifications.component';
import { TermsAndConditionsComponent } from './components/terms-and-conditions/terms-and-conditions.component';
import { ContractManagementComponent } from './admin/contract-management/contract-management.component';
import { PartnerContractsComponent } from './components/partner-contracts/partner-contracts.component';
import { AiChatbotComponent } from './components/ai-chatbot/ai-chatbot.component';

import { IsAdminGuard } from './guards/is-admin.guard';


export const routes: Routes = [
  {
    path: '',
    component: HomeComponent,
    title: 'Home'
  },

  { path: 'signup', component: SignupComponent },
  { path: 'signin', component: SigninComponent },
  { path: 'forgot-password', component: ForgotPasswordComponent, title: 'Forgot Password' },
  { path: 'reset-password', component: ResetPasswordComponent, title: 'Reset Password' },
  {
    path: 'auth/callback',
    component: AuthCallbackComponent,
    title: 'Authentication'
  },
  { path: 'auth/error', component: AuthCallbackComponent, title: 'Authentication Error' },
  { path: 'contact', component: ContactComponent },
  { path: 'settings', component: SettingsComponent },
  {
    path: 'profile',
    component: ProfileComponent,
  },
  {
    path: 'partner-contracts',
    component: PartnerContractsComponent,
    title: 'Partner Contracts'
  },
  {
    path: 'file-upload',
    component: FileUploadComponent,
    title: 'Upload Documents'
  },
  {
    path: 'security',
    component: SecurityComponent,
    title: 'Electronic Signature'
  },
  {
    path: 'dashboard',
    component: DashboardComponent,
    title: 'Dashboard'
  },
  {
    path: 'terms',
    component: TermsAndConditionsComponent,
    title: 'Terms and Conditions'
  },
  {
    path: 'ai-chatbot',
    component: AiChatbotComponent,
    title: 'AI Contract Assistant'
  },
  // Admin routes - protected by IsAdminGuard
  {
    path: 'admin',
    canActivate: [IsAdminGuard],
    children: [
      {
        path: '',
        component: AdminDashboardComponent,
        title: 'Admin Dashboard'
      },
      {
        path: 'dashboard',
        component: AdminDashboardComponent,
        title: 'Admin Dashboard'
      },
      {
        path: 'users',
        component: UserManagementComponent,
        title: 'User Management'
      },
      {
        path: 'notifications',
        component: NotificationsComponent,
        title: 'Notifications'
      },
      {
        path: 'partners',
        loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule),
        title: 'Partner Management'
      },
      {
        path: 'contracts/:partnerId',
        component: ContractManagementComponent,
        title: 'Contract Management'
      }
    ]
  }
];
