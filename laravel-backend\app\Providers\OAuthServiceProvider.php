<?php

namespace App\Providers;

use App\Services\OAuthService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

class OAuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(OAuthService::class, function ($app) {
            return new OAuthService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
