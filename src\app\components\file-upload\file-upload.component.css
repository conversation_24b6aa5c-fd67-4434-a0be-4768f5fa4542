/* Modern Upload Page Styles */
:host {
  --signature-border-color: rgba(var(--primary-rgb, 78, 205, 196), 0.5);
  --signature-bg: rgba(var(--primary-rgb, 78, 205, 196), 0.05);
  --signature-border-width: 2px;
  --component-radius-sm: var(--radius-sm, 0.25rem);
  --component-radius-md: var(--radius-md, 0.5rem);
  --component-radius-lg: var(--radius-lg, 1rem);

  display: block;
  height: 100%;
  width: 100%;
}

/* Main Container */
.upload-page-container {
  min-height: 100vh;
  width: 100%;
  background-color: var(--background);
  color: var(--text);
  position: relative;
  overflow: hidden;
}

.upload-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Header Styles */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-top: 80px;
}

.section-header h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  width: 100%;
  white-space: nowrap;
  overflow: visible;
  color: var(--text);
  font-weight: bold;
  text-align: center;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.highlight {
  color: var(--primary);
  background: linear-gradient(90deg, var(--primary), var(--primary-light, #6CDED6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  display: inline-block;
}

/* Animation classes */
.animate__fadeInDown {
  animation: fadeInDown 1s ease forwards;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.upload-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  color: var(--text);
  background-color: var(--elevation2);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

@media (min-width: 992px) {
  .upload-container {
    grid-template-columns: 1fr 1fr;
  }
}

/* Modern Card Styling */
.modern-card {
  padding: 25px;
  background-color: var(--elevation1);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  margin-bottom: 3rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.modern-card:hover {
  box-shadow: var(--shadow-md);
}

.card-title {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--text);
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 10px;
}

.card-title i {
  color: var(--primary);
}

/* Card Header with Back Button */
.card-header-with-back {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.btn-back {
  background: var(--elevation2);
  border: 1px solid var(--border);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text);
  font-size: 1rem;
}

.btn-back:hover {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
  transform: translateX(-2px);
}

.upload-subtitle {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
  padding: 10px 0;
  border-top: 1px solid var(--border);
}

.upload-subtitle strong {
  color: var(--primary);
}

/* Extracted Data Form Styles */
.extracted-data-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: var(--elevation2);
  border-radius: 12px;
  border-left: 4px solid var(--primary);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 0.5rem 0;
  color: var(--text);
  font-size: 1.1rem;
  font-weight: 600;
}

.section-title i {
  color: var(--primary);
}

.section-description {
  color: var(--text-secondary);
  margin: 0 0 1.5rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.extracted-data-form {
  display: grid;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-weight: 500;
  color: var(--text);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.original-value {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 400;
  font-style: italic;
}

.field-input {
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: 8px;
  background: var(--background);
  color: var(--text);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.field-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.field-input.has-original {
  border-left: 3px solid var(--primary);
}

.field-input::placeholder {
  color: var(--text-secondary);
}

/* Upload Area */
.upload-form {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-area {
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--elevation2);
}

.upload-area:hover:not(.has-file),
.upload-area.drag-over:not(.has-file) {
  border-color: var(--primary);
  background: rgba(var(--primary-rgb, 78, 205, 196), 0.05);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 78, 205, 196), 0.2);
}

.upload-icon-container {
  width: 80px;
  height: 80px;
  background: rgba(var(--primary-rgb, 78, 205, 196), 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.upload-icon {
  font-size: 2.5rem;
  color: var(--primary);
  transition: all 0.3s ease;
}

.upload-area:hover:not(.has-file) .upload-icon {
  transform: translateY(-5px);
}

.upload-text {
  color: var(--text);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
}

.btn-choose {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: var(--radius-md);
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.btn-choose:hover {
  background-color: var(--primary-dark, #3DACA4);
  box-shadow: var(--shadow-md);
}

.supported-file-types {
  margin-top: 1rem;
  width: 100%;
}

.file-types-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.file-type-icons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.file-type-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.file-type-icon i {
  font-size: 1.5rem;
  color: var(--primary);
}

.file-size-limit {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Contract selected notification */
.contract-selected-notification {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background-color: rgba(78, 205, 196, 0.1);
  border: 1px dashed #4ECDC4;
  border-radius: 8px;
  margin: 1rem 0;
}

.notification-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(78, 205, 196, 0.2);
  border-radius: 50%;
  margin-right: 1rem;
  flex-shrink: 0;
}

.notification-icon i {
  font-size: 1.8rem;
  color: #4ECDC4;
}

.notification-icon i.fa-check-circle {
  color: #10b981;
}

.notification-icon i.fa-spinner {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Styles for contract notifications are below */

/* Style for loading notification */
.contract-selected-notification:has(.fa-spinner),
.contract-loading {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

/* Style for ready notification */
.contract-selected-notification:has(.fa-check-circle),
.contract-ready {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
}

/* Style for error notification */
.contract-selected-notification:has(.fa-exclamation-triangle),
.contract-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
}

.notification-icon i.fa-exclamation-triangle {
  color: #ef4444;
}

.btn-retry {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-retry:hover {
  background-color: rgba(239, 68, 68, 0.3);
}

.notification-content h4 {
  margin: 0 0 0.5rem;
  font-size: 1.2rem;
  color: var(--text);
}

.notification-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* File Preview */
.file-preview {
  width: 100%;
}

.file-preview-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  background: var(--elevation2);
  padding: 1rem;
  border-radius: 8px;
}

.file-icon-container {
  width: 40px;
  height: 40px;
  background: rgba(var(--primary-rgb, 78, 205, 196), 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.25rem;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: var(--text);
}

.file-meta {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contract-badge {
  background-color: rgba(78, 205, 196, 0.2);
  color: #4ECDC4;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.btn-remove-file {
  background: var(--overlay);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-remove-file:hover {
  background: rgba(var(--error-rgb, 239, 68, 68), 0.2);
  color: var(--text);
}

.file-preview-content {
  margin-top: 1rem;
  background: var(--elevation2);
  border-radius: 8px;
  overflow: hidden;
}

.pdf-preview {
  width: 100%;
  height: 300px;
}

.pdf-preview iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.word-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.document-icon {
  font-size: 3rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

/* Extracted data preview */
.extracted-data-preview {
  margin-top: 1rem;
  background-color: rgba(78, 205, 196, 0.05);
  border: 1px solid rgba(78, 205, 196, 0.2);
  border-radius: 8px;
  padding: 1rem;
}

.extracted-data-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary);
  font-weight: 500;
}

.extracted-data-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.extracted-data-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  background-color: var(--elevation2);
  border-radius: 4px;
  font-size: 0.875rem;
}

.field-key {
  font-weight: 500;
  color: var(--text);
}

.field-value {
  color: var(--text-secondary);
  font-style: italic;
  max-width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.extracted-data-more {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
  padding: 0.5rem;
}

/* Message Display */
.status-messages {
  margin-top: 20px;
}

.message {
  padding: 15px;
  border-radius: var(--radius-md);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.message.success {
  background-color: var(--success-bg);
  color: var(--success);
}

.message.error {
  background-color: var(--error-bg);
  color: var(--error);
}

.message-icon {
  font-size: 1.25rem;
}

.message.success .message-icon {
  color: var(--success);
}

.message.error .message-icon {
  color: var(--error);
}

.message-content {
  flex: 1;
}

.message-text {
  margin-bottom: 0.5rem;
  color: var(--text-white);
}

.btn-try-again {
  background: none;
  border: none;
  color: var(--primary);
  padding: 0;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-try-again:hover {
  text-decoration: underline;
}

/* Progress Bar */
.progress-container {
  margin: 1.5rem 0;
}

.progress-status {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.progress-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.progress-percentage {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--primary);
}

.progress-bar {
  height: 8px;
  background: var(--overlay);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.progress-value {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--primary-light, #6CDED6));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-value.complete {
  background: linear-gradient(90deg, #10b981, #34d399);
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.progress-info.success {
  color: var(--success);
}

/* Action Buttons */
.form-actions {
  margin-top: 30px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.btn {
  padding: 12px 25px;
  font-size: 1.1rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  border: none;
  width: 100%;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-md);
}

.btn-primary:disabled {
  background-color: rgba(78, 205, 196, 0.5);
  cursor: not-allowed;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Spinner */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Extracted Data Card */
.extracted-data-card {
  padding: 25px;
  background-color: var(--elevation1);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  margin-bottom: 3rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.extracted-data-card:hover {
  box-shadow: var(--shadow-md);
}

.info-text {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  line-height: 1.5;
}

.form-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Form Styling */
.form-group {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text);
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group label i {
  color: var(--primary);
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid var(--border);
  background-color: var(--elevation2);
  border-radius: var(--radius-md);
  color: var(--text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(119, 30, 118, 0.2);
}

.form-control::placeholder {
  color: var(--text-secondary);
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--error);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.generated-document-info {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.success-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.reload-countdown {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.generated-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.btn-download,
.btn-sign {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-download {
  background-color: transparent;
  border: 2px solid var(--success);
  color: var(--success);
}

.btn-download:hover {
  background-color: var(--success-bg);
  box-shadow: var(--shadow-sm);
}

.btn-sign {
  background-color: var(--primary);
  color: white;
  border: none;
}

.btn-sign:hover {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-md);
}

/* Files Card */
.files-card {
  padding: 25px;
  background-color: var(--elevation1);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.files-card:hover {
  box-shadow: var(--shadow-md);
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.btn-danger {
  background-color: var(--error-light);
  color: var(--error);
  border: 1px solid rgba(var(--error-rgb, 239, 68, 68), 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-danger:hover {
  background-color: rgba(var(--error-rgb, 239, 68, 68), 0.2);
}

.files-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.search-container {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-input {
  width: 100%;
  padding: 12px 15px 12px 2.5rem;
  border: 2px solid var(--border);
  background-color: var(--elevation2);
  border-radius: var(--radius-md);
  color: var(--text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(119, 30, 118, 0.2);
}

.btn-clear-search {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-option {
  background: var(--elevation2);
  border: 1px solid var(--border);
  color: var(--text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-option:hover {
  background: var(--surface-hover);
}

.filter-option.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.loading-files,
.no-files,
.no-search-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  text-align: center;
}

.loading-files app-spinner,
.no-files i,
.no-search-results i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.btn-clear-filter {
  background: none;
  border: none;
  color: var(--primary);
  margin-top: 1rem;
  cursor: pointer;
}

.btn-clear-filter:hover {
  text-decoration: underline;
}

/* Files Table */
.files-table {
  width: 100%;
  border-collapse: collapse;
}

.files-table th,
.files-table td {
  padding: 1rem;
  text-align: left;
}

.files-table th {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
  border-bottom: 1px solid var(--border);
}

.files-table td {
  border-bottom: 1px solid var(--border);
}

.file-row {
  transition: background-color 0.2s ease;
}

.file-row:hover {
  background-color: var(--surface-hover);
}

.file-info-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.file-icon {
  font-size: 1.25rem;
}

.file-icon.fa-file-pdf {
  color: var(--error);
}

.file-icon.fa-file-word {
  color: var(--info);
}

.file-name-text {
  font-weight: 500;
  color: var(--text);
}

.file-type-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.file-type-badge.pdf {
  background-color: var(--error-light);
  color: var(--error);
}

.file-type-badge.word {
  background-color: var(--info-light);
  color: var(--info);
}

.file-date {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-file-action {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--overlay);
  color: var(--text-secondary);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-file-action:hover {
  background: var(--surface-hover);
  color: var(--text);
}

.btn-file-action.delete {
  color: var(--error);
}

.btn-file-action.delete:hover {
  background: var(--error-light);
}

/* Animation */
.animate-in {
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Partners Card Styles */
.partners-card {
  margin-bottom: 2rem;
}

.partners-container {
  padding: 1rem 0;
}

.partners-description {
  margin-bottom: 1.5rem;
  color: var(--text-secondary);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  text-align: center;
  padding: 1rem;
  background: var(--elevation2);
  border-radius: 8px;
  border-left: 4px solid var(--primary);
}

.partners-description i {
  color: var(--primary);
  font-size: 1.1rem;
}

.partner-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.partner-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.25rem;
  border-radius: 8px;
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.partner-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: #d1d1d1;
}

.partner-item.selected {
  border-color: var(--primary);
  background-color: rgba(var(--primary-rgb, 78, 205, 196), 0.05);
  box-shadow: 0 5px 15px rgba(var(--primary-rgb, 78, 205, 196), 0.1);
}

.partner-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.partner-icon img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.partner-icon i {
  font-size: 2.5rem;
  color: var(--text-secondary);
}

.partner-details {
  text-align: center;
  width: 100%;
}

.partner-name {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--text);
}

.partner-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Contract section styles */
.contracts-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border);
}

.contracts-heading {
  font-size: 1.15rem;
  color: var(--text);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contracts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contract-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.contract-item:hover {
  background-color: var(--surface-hover);
  border-color: #d1d1d1;
}

.contract-item.selected {
  border-color: var(--primary);
  background-color: rgba(var(--primary-rgb, 78, 205, 196), 0.05);
}

.contract-icon {
  font-size: 1.75rem;
  color: var(--text-secondary);
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
}

.contract-details {
  flex: 1;
}

.contract-name {
  font-weight: 600;
  font-size: 1rem;
  margin: 0 0 0.25rem 0;
  color: var(--text);
}

.contract-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
}

.contract-type {
  display: inline-block;
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.contract-type.pdf {
  background-color: var(--error-light);
  color: var(--error);
}

.contract-type.word {
  background-color: var(--info-light);
  color: var(--info);
}

/* Loading and empty states */
.loading-partners,
.no-partners,
.loading-contracts,
.no-contracts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.loading-partners i,
.no-partners i,
.loading-contracts i,
.no-contracts i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.upload-suggestion {
  font-size: 0.9rem;
  color: var(--primary);
  font-weight: 500;
  margin-top: 0.5rem;
}

/* Responsive Styling */
@media (max-width: 992px) {
  .upload-container {
    grid-template-columns: 1fr;
  }

  .upload-content {
    padding: 1.5rem;
  }

  .section-header {
    padding-top: 60px;
  }

  .form-fields {
    grid-template-columns: 1fr;
  }

  .generated-actions {
    flex-direction: column;
  }

  .modern-card,
  .extracted-data-card,
  .files-card {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .section-header h2 {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .files-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .files-controls {
    flex-direction: column;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .position-options {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .upload-content {
    padding: 1rem;
  }

  .section-header {
    padding-top: 40px;
    margin-bottom: 2rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }

  .section-subtitle {
    font-size: 0.95rem;
  }

  .modern-card,
  .extracted-data-card,
  .files-card {
    padding: 15px;
  }

  .card-title {
    font-size: 1.25rem;
  }

  .upload-area {
    padding: 1.5rem;
    min-height: 250px;
  }

  .upload-icon-container {
    width: 60px;
    height: 60px;
  }

  .upload-icon {
    font-size: 2rem;
  }

  .btn {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }
}

/* Error message styling */
.error-message-container {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(220, 53, 69, 0.05);
  border-radius: 8px;
  border-left: 4px solid #dc3545;
}

.error-message {
  display: flex;
  align-items: flex-start;
  color: #dc3545;
  margin-bottom: 15px;
  font-weight: 500;
}

.error-message i {
  margin-right: 10px;
  font-size: 1.2em;
  margin-top: 2px;
}

.error-details {
  background-color: var(--elevation2);
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
  font-size: 0.9em;
}

.error-details p {
  margin-bottom: 8px;
}

.error-details ul {
  margin-left: 20px;
  margin-bottom: 10px;
}

.error-details li {
  margin-bottom: 5px;
}