import { ChangeDetectionStrategy, Component, ElementRef, ViewChild, AfterViewInit, OnInit, OnDestroy, NgZone, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormBuilder, Validators, FormGroup } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { catchError, debounceTime, finalize, takeUntil, throwError, of } from 'rxjs';
import { Router, ActivatedRoute } from '@angular/router';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Subject } from 'rxjs';
import { UnsignedContractService } from '../../services/unsigned-contract.service';
import { AuthService } from '../../services/auth.service';
import { ContractSigningService } from '../../services/contract-signing.service';
import { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';

// PDF viewer configuration

// Error messages constants
const ERROR_MESSAGES = {
  REQUIRED: 'Ce champ est requis',
  INVALID_EMAIL: 'Format email invalide',
  PDF_REQUIRED: 'Un fichier PDF est requis',
  PDF_TOO_LARGE: 'Le fichier ne doit pas dépasser 5MB',
  SIGNATURE_REQUIRED: 'Signature requise',
  CONSENT_REQUIRED: 'Vous devez accepter les termes',
  INVALID_NAME: 'Le nom doit contenir au moins 2 caractères alphabétiques',
  SESSION_EXPIRED: 'Session expirée. Veuillez vous reconnecter.',
  GENERIC_ERROR: 'Une erreur est survenue. Veuillez réessayer.'
};
// API base URL is now managed by the services

@Component({
  selector: 'app-security',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, NgxExtendedPdfViewerModule],
  templateUrl: './security.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./security.component.css'],
})
export class SecurityComponent implements AfterViewInit, OnInit, OnDestroy {
  private cleanupFilePreview?: () => void;
  signatureDataUrl?: string;
  @ViewChild('signaturePad') signaturePad!: ElementRef<HTMLCanvasElement>;
  private ctx!: CanvasRenderingContext2D;
  isDrawing = false;
  lastX = 0;
  lastY = 0;
  uploadedFileUrl: SafeResourceUrl | null = null;
  filePreviewType: 'pdf' | 'word' | null = null;
  errorMessage: string | null = null;
  successMessage: string | null = null;
  isLoading = false;
  fieldErrors: { [key: string]: string } = {};
  maxFileSize = 5 * 1024 * 1024; // 5MB
  originalFilename?: string; // Store the original filename
  showPdfFallback = false; // Flag to show PDF fallback view
  pdfViewerCheckTimeout: any = null; // Timeout for checking PDF viewer

  // PDF viewer properties
  pdfDoc: any = null;

  // Signature options
  signatureType: 'draw' | 'upload' = 'draw';
  penColor: string = '#1e3c72';
  penSize: number = 2;
  uploadedSignatureUrl: SafeResourceUrl | null = null;
  signaturePosition: 'bottom-left' | 'bottom-center' | 'bottom-right' = 'bottom-right';

  // Undo/Redo stacks
  private undoStack: ImageData[] = [];
  private redoStack: ImageData[] = [];
  private destroy$ = new Subject<void>();

  // Form configuration
  signForm!: FormGroup;

  pdfFile?: File;
  unsignedContractId?: number;

  get canUndo(): boolean {
    return this.undoStack.length > 0;
  }

  get canRedo(): boolean {
    return this.redoStack.length > 0;
  }

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    private unsignedContractService: UnsignedContractService,
    private authService: AuthService,
    private contractSigningService: ContractSigningService
  ) {
    // Initialize form in constructor
    this.signForm = this.fb.group({
      fullName: ['', [
        Validators.required,
        Validators.pattern(/^[\p{L} -]{2,}$/u)
      ]],
      email: ['', [Validators.required, Validators.email]],
      consent: [false, Validators.requiredTrue]
    });
  }

  ngOnDestroy(): void {
    // Clean up resources
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up file preview if needed
    if (this.cleanupFilePreview) {
      this.cleanupFilePreview();
    }

    // Clear any pending timeouts
    if (this.pdfViewerCheckTimeout) {
      clearTimeout(this.pdfViewerCheckTimeout);
      this.pdfViewerCheckTimeout = null;
    }

    // Clear resize timeout
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = null;
    }

    // Remove window resize event listener
    window.removeEventListener('resize', this.handleWindowResize);
  }

  // Resize timeout for debouncing
  private resizeTimeout: any = null;

  ngAfterViewInit(): void {
    // Wrap canvas setup in ngZone to fix hydration issues
    this.ngZone.run(() => {
      // First attempt at initialization
      setTimeout(() => {
        // Only setup canvas if we're in draw mode
        if (this.signatureType === 'draw') {
          this.setupCanvas();
          this.setupResizeObserver();
        }
        this.cdr.detectChanges();

        // Second attempt after a longer delay to ensure DOM is fully rendered
        setTimeout(() => {
          if (this.signatureType === 'draw') {
            this.setupCanvas();
            this.setupResizeObserver();
            this.cdr.detectChanges();
          }
        }, 500);
      }, 0);
    });

    // Add window resize event listener for PDF viewer
    window.addEventListener('resize', this.handleWindowResize);
  }

  /**
   * Handle window resize events to adjust PDF rendering
   */
  private handleWindowResize = (): void => {
    // Debounce the resize event
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
    }

    this.resizeTimeout = setTimeout(() => {
      // For iframe-based PDF viewer, we don't need to do anything on resize
      console.log('Window resized');
    }, 200);
  };

  ngOnInit(): void {
    this.signForm.valueChanges
      .pipe(
        debounceTime(300),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.fieldErrors = {};
      });

    // Check for unsigned contract ID in query params
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        // Support all parameter formats for backward compatibility
        const contractId = params['id'] || params['unsignedContractId'] || params['documentId'] || params['contractId'];
        const viewMode = params['view'] === 'true'; // Check if we're in view mode
        const source = params['source'] || 'unknown'; // Track where the request came from (dashboard or upload)
        const contractName = params['contractName'] || ''; // Get contract name if provided

        console.log('Query parameters:', params);
        console.log('Source:', source);
        console.log('Contract name from params:', contractName);

        if (contractId) {
          this.unsignedContractId = Number(contractId);
          console.log('Loading unsigned contract with ID:', this.unsignedContractId);
          console.log('View mode:', viewMode);

          // If we have a contract name, use it to create a better filename
          if (contractName) {
            // Create a sanitized filename from the contract name
            const sanitizedName = contractName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
            this.originalFilename = `${sanitizedName}.pdf`;
            console.log('Created filename from contract name param:', this.originalFilename);
          }

          // Always load the file from the unsigned contract with view mode set to true
          // This ensures we always view the document rather than downloading it
          this.loadUnsignedContract(this.unsignedContractId, true);

          // Pre-fill form data if available in query params
          if (params['email']) {
            this.signForm.get('email')?.setValue(params['email']);
          }

          if (params['fullName']) {
            this.signForm.get('fullName')?.setValue(params['fullName']);
          }

          // If the user is coming from the dashboard, try to pre-fill with user data
          if (source === 'dashboard' || !source || source === 'unknown') {
            // Try to get user info from auth service to pre-fill the form
            this.authService.currentUser$.subscribe(user => {
              if (user) {
                if (user.email && !this.signForm.get('email')?.value) {
                  this.signForm.get('email')?.setValue(user.email);
                  console.log('Pre-filled email from user data:', user.email);
                }
                if (user.name && !this.signForm.get('fullName')?.value) {
                  this.signForm.get('fullName')?.setValue(user.name);
                  console.log('Pre-filled name from user data:', user.name);
                }
              }
            });
          }
        } else {
          console.log('No contract ID found in query parameters');
        }
      });
  }

  /**
   * Load the unsigned contract file
   * @param id The contract ID
   * @param viewMode Whether to view the file (true) or download it (false)
   */
  private loadUnsignedContract(id: number, viewMode: boolean = false): void {
    console.log('Loading unsigned contract with ID:', id);
    console.log('View mode:', viewMode);

    this.isLoading = true;
    this.errorMessage = null;
    this.unsignedContractId = id; // Store the ID

    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.error('User is not authenticated. Redirecting to login.');
      this.errorMessage = 'Authentication required to access contracts. Please log in.';
      this.isLoading = false;
      this.cdr.detectChanges();

      // Redirect to signin page after a short delay
      setTimeout(() => {
        this.router.navigate(['/signin'], {
          queryParams: {
            returnUrl: this.router.url,
            id: id
          }
        });
      }, 2000);
      return;
    }

    // First get the contract details to ensure it exists
    console.log('Getting contract details for ID:', id);
    this.unsignedContractService.getUnsignedContract(id)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error: HttpErrorResponse) => {
          console.error('Error getting unsigned contract details:', error);

          if (error.status === 401) {
            this.errorMessage = 'Authentication required to access contracts. Please log in.';

            // Redirect to signin page after a short delay
            setTimeout(() => {
              this.router.navigate(['/signin'], {
                queryParams: {
                  returnUrl: this.router.url,
                  id: id
                }
              });
            }, 2000);
          } else if (error.status === 404) {
            this.errorMessage = 'Contract not found or already signed.';
          } else {
            this.errorMessage = 'Error accessing contract: ' + (error.error?.message || error.message || 'Unknown error');
          }

          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => error);
        })
      )
      .subscribe({
        next: (contractDetails) => {
          console.log('Contract details retrieved successfully:', contractDetails);

          // If contract details include user information, pre-fill the form
          if (contractDetails && typeof contractDetails === 'object') {
            // Check if contract details include user information
            if ('user' in contractDetails && contractDetails.user) {
              const user = contractDetails.user;

              // Pre-fill email if available
              if ('email' in user && user.email && this.signForm.get('email')?.value === '') {
                this.signForm.get('email')?.setValue(user.email);
              }

              // Pre-fill name if available
              if ('name' in user && user.name && this.signForm.get('fullName')?.value === '') {
                this.signForm.get('fullName')?.setValue(user.name);
              }
            }

            // If contract details include document information, store it
            if ('filename' in contractDetails && contractDetails.filename) {
              // Store original filename for reference
              this.originalFilename = contractDetails.filename;
              console.log('Original filename from contract details:', this.originalFilename);
            }

            // Extract and store additional document information
            if ('file_size' in contractDetails) {
              console.log('File size found in contract details:', contractDetails.file_size);
            }

            if ('created_at' in contractDetails) {
              console.log('Creation date found in contract details:', contractDetails.created_at);
            }

            if ('file_type' in contractDetails) {
              console.log('File type found in contract details:', contractDetails.file_type);
            }

            if ('nom_contrat' in contractDetails) {
              console.log('Contract name found in contract details:', contractDetails.nom_contrat);
              // If we have a contract name but no filename, use it to create a better filename
              if (!this.originalFilename && contractDetails.nom_contrat) {
                // Create a sanitized filename from the contract name
                const sanitizedName = contractDetails.nom_contrat.replace(/[^a-z0-9]/gi, '_').toLowerCase();
                this.originalFilename = `${sanitizedName}.pdf`;
                console.log('Created filename from contract name:', this.originalFilename);
              }
            }
          }

          // Always use the viewing method to avoid download prompts
          console.log('Using view mode to load file without downloading');
          this.getContractFileForViewing(id);
        },
        error: () => {
          // Error already handled in catchError
          this.isLoading = false;
        }
      });
  }

  /**
   * Get the contract file for viewing after verifying the contract exists
   * This uses the service method that calls the /view endpoint
   */
  private getContractFileForViewing(id: number): void {
    console.log('Getting contract file for viewing with ID:', id);
    this.isLoading = true;

    // Skip getting contract details again if we already have the filename
    if (this.originalFilename) {
      console.log('Already have original filename:', this.originalFilename);
      this.fetchContractFile(id);
      return;
    }

    // First get the contract details to ensure we have the correct filename
    this.unsignedContractService.getUnsignedContract(id)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error('Error getting contract details before viewing:', error);
          // Continue with file retrieval even if details fail
          return of(null);
        })
      )
      .subscribe(contractDetails => {
        // Extract filename from contract details if available
        if (contractDetails && typeof contractDetails === 'object') {
          if ('filename' in contractDetails && contractDetails.filename) {
            this.originalFilename = contractDetails.filename;
            console.log('Got original filename from contract details:', this.originalFilename);
          } else if ('nom_contrat' in contractDetails && contractDetails.nom_contrat) {
            // Create a sanitized filename from the contract name
            const sanitizedName = contractDetails.nom_contrat.replace(/[^a-z0-9]/gi, '_').toLowerCase();
            this.originalFilename = `${sanitizedName}.pdf`;
            console.log('Created filename from contract name:', this.originalFilename);
          }
        }

        // Now fetch the actual file
        this.fetchContractFile(id);
      });
  }

  /**
   * Fetch the contract file from the API
   * @param id The contract ID
   */
  private fetchContractFile(id: number): void {
    console.log('Fetching contract file with ID:', id);

    this.unsignedContractService.getUnsignedContractFile(id)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error: HttpErrorResponse) => {
          console.error('Error getting contract file for viewing:', error);

          if (error.status === 401) {
            this.errorMessage = 'Authentication required to access contracts. Please log in.';

            // Redirect to signin page after a short delay
            setTimeout(() => {
              this.router.navigate(['/signin'], {
                queryParams: {
                  returnUrl: this.router.url,
                  id: id
                }
              });
            }, 2000);
          } else {
            this.errorMessage = 'Error loading file: ' + (error.error?.message || error.message || 'Unknown error');
          }

          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => error);
        })
      )
      .subscribe({
        next: (blob) => {
          console.log('File blob received for viewing:', blob);
          console.log('Blob type:', blob.type);
          console.log('Blob size:', blob.size);

          if (!blob || blob.size === 0) {
            console.error('Empty blob received');
            this.errorMessage = 'Error: Empty file received';
            this.isLoading = false;
            this.cdr.detectChanges();
            return;
          }

          // IMPORTANT: Determine file type from blob MIME type AND original filename
          let fileType = blob.type;
          let fileExtension = 'pdf'; // Default to PDF

          console.log('Determining file type from blob:', blob);
          console.log('Blob MIME type:', fileType);
          console.log('Original filename:', this.originalFilename);

          // First try to determine from original filename if available
          if (this.originalFilename) {
            const ext = this.originalFilename.split('.').pop()?.toLowerCase();
            if (ext === 'pdf' || ext === 'doc' || ext === 'docx') {
              fileExtension = ext;
              console.log('Using extension from original filename:', fileExtension);
            }
          }

          // If we couldn't determine from filename, use MIME type
          if (fileExtension === 'pdf' && !fileType.includes('pdf') &&
              (fileType.includes('word') || fileType.includes('docx') || fileType.includes('doc'))) {
            // If the extension is PDF but MIME type is Word, use Word extension
            fileExtension = 'docx';
            console.log('Overriding extension based on Word MIME type:', fileExtension);
          } else if ((fileExtension === 'doc' || fileExtension === 'docx') && fileType.includes('pdf')) {
            // If the extension is Word but MIME type is PDF, use PDF extension
            fileExtension = 'pdf';
            console.log('Overriding extension based on PDF MIME type:', fileExtension);
          }

          // For generic MIME types, trust the extension we determined
          if (fileType === 'application/octet-stream') {
            console.log('Generic MIME type detected, using extension:', fileExtension);
          }

          // If we don't have the original filename, create one based on the determined extension
          if (!this.originalFilename) {
            this.originalFilename = `document_${id}.${fileExtension}`;
            console.log('Created default filename:', this.originalFilename);
          } else {
            // Make sure the original filename has the correct extension
            const currentExt = this.originalFilename.split('.').pop()?.toLowerCase();
            if (!currentExt || (currentExt !== 'pdf' && currentExt !== 'doc' && currentExt !== 'docx')) {
              this.originalFilename = `${this.originalFilename}.${fileExtension}`;
              console.log('Added extension to filename:', this.originalFilename);
            } else if (currentExt !== fileExtension) {
              // If the extension doesn't match what we determined, update it
              this.originalFilename = this.originalFilename.replace(/\.[^/.]+$/, '') + '.' + fileExtension;
              console.log('Updated extension in filename:', this.originalFilename);
            }
          }

          console.log('Final determined file extension:', fileExtension);
          console.log('Final filename:', this.originalFilename);

          console.log('Creating file with name:', this.originalFilename);

          // Create a File object from the blob using the original filename
          this.pdfFile = new File([blob], this.originalFilename, { type: blob.type });
          console.log('File object created:', this.pdfFile);

          // Determine file type for preview - this will also set the URL
          this.determineFilePreviewType(this.pdfFile);

          this.isLoading = false;
          this.errorMessage = null; // Clear any error messages
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error getting contract file for viewing:', error);
          this.errorMessage = 'Error loading file: ' + (error.message || 'Unknown error');
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Setup canvas for signature drawing
  private setupCanvas(): void {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      console.warn('Not in browser environment, skipping canvas setup');
      return;
    }

    if (!this.signaturePad) {
      console.warn('Signature pad not available yet');
      return;
    }

    try {
      const canvas = this.signaturePad.nativeElement;
      if (!canvas) {
        console.warn('Canvas element not available');
        return;
      }

      this.ctx = canvas.getContext('2d', { willReadFrequently: true })!;
      if (!this.ctx) {
        console.warn('Could not get 2D context from canvas');
        return;
      }

      const rect = canvas.parentElement?.getBoundingClientRect();
      if (!rect) {
        console.warn('Canvas parent element not available');
        // Use default dimensions
        canvas.width = 400;
        canvas.height = 200;
      } else {
        canvas.width = rect.width || 400;
        canvas.height = 200;
      }

      this.ctx.lineWidth = this.penSize;
      this.ctx.lineCap = 'round';
      this.ctx.lineJoin = 'round';
      this.ctx.strokeStyle = this.penColor;
    } catch (error) {
      console.error('Error setting up canvas:', error);
    }
  }

  private setupResizeObserver(): void {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof ResizeObserver === 'undefined') {
      console.warn('Not in browser environment or ResizeObserver not available, skipping resize observer setup');
      return;
    }

    if (!this.signaturePad) {
      console.warn('Signature pad not available yet for resize observer');
      return;
    }

    try {
      const resizeObserver = new ResizeObserver(() => {
        try {
          this.setupCanvas();
          if (this.signatureDataUrl && this.signaturePad && this.ctx) {
            // Redraw signature if canvas resizes
            const img = new Image();
            img.onload = () => {
              try {
                this.ctx.clearRect(0, 0, this.signaturePad.nativeElement.width, this.signaturePad.nativeElement.height);
                this.ctx.drawImage(img, 0, 0);
              } catch (error) {
                console.error('Error redrawing signature on resize:', error);
              }
            };
            img.onerror = (error) => {
              console.error('Error loading signature image:', error);
            };
            img.src = this.signatureDataUrl;
          }
        } catch (error) {
          console.error('Error in resize observer callback:', error);
        }
      });

      if (this.signaturePad.nativeElement && this.signaturePad.nativeElement.parentElement) {
        resizeObserver.observe(this.signaturePad.nativeElement.parentElement);
      } else {
        console.warn('Cannot attach resize observer - signature pad or parent element not available');
      }
    } catch (error) {
      console.error('Error setting up resize observer:', error);
    }
  }

  // Drawing methods
  startDrawing(event: MouseEvent | TouchEvent): void {
    this.isDrawing = true;
    const canvas = this.signaturePad.nativeElement;
    const rect = canvas.getBoundingClientRect();

    this.undoStack.push(this.ctx.getImageData(0, 0, canvas.width, canvas.height));
    this.redoStack = [];
    this.ctx.lineWidth = this.penSize;
    this.ctx.strokeStyle = this.penColor;

    const coords = this.getEventCoordinates(event, rect);
    this.lastX = coords.x;
    this.lastY = coords.y;

    if (event instanceof TouchEvent && event.touches[0].force) {
      this.ctx.lineWidth = Math.min(event.touches[0].force * 4, 6);
    }
  }

  draw(event: MouseEvent | TouchEvent): void {
    if (!this.isDrawing) return;

    event.preventDefault();
    const rect = this.signaturePad.nativeElement.getBoundingClientRect();
    const coords = this.getEventCoordinates(event, rect);

    if (event instanceof TouchEvent && event.touches[0].force) {
      this.ctx.lineWidth = Math.min(event.touches[0].force * 4, 6);
    } else {
      this.ctx.lineWidth = this.penSize;
    }

    this.ctx.strokeStyle = this.penColor;
    this.ctx.beginPath();
    this.ctx.moveTo(this.lastX, this.lastY);
    this.ctx.lineTo(coords.x, coords.y);
    this.ctx.stroke();

    this.lastX = coords.x;
    this.lastY = coords.y;
  }

  endDrawing(): void {
    this.isDrawing = false;
    // Enregistrez la version string
    const signatureDataUrl = this.signaturePad.nativeElement.toDataURL('image/png', 1.0);
    this.signatureDataUrl = signatureDataUrl;
    this.ctx.lineWidth = this.penSize;
  }

  // Signature type methods
  setSignatureType(type: 'draw' | 'upload'): void {
    this.signatureType = type;
    this.signatureDataUrl = undefined;

    // Clear previous signature data
    if (type === 'draw') {
      // If switching to draw, make sure canvas is ready
      setTimeout(() => {
        if (this.signaturePad) {
          this.setupCanvas();
          this.setupResizeObserver();
        } else {
          console.warn('Signature pad not available when switching to draw mode');
          // Try to initialize the canvas again after a short delay
          setTimeout(() => {
            this.ngZone.run(() => {
              this.setupCanvas();
              this.setupResizeObserver();
              this.cdr.detectChanges();
            });
          }, 100);
        }
      }, 0);
    } else if (type === 'upload') {
      // Reset uploaded signature
      this.uploadedSignatureUrl = null;
      this.signatureDataUrl = undefined;
    }

    this.cdr.detectChanges();
  }

  // Pen color and size methods
  setPenColor(color: string): void {
    this.penColor = color;
    if (this.ctx) {
      this.ctx.strokeStyle = color;
    }
  }

  setPenSize(size: number): void {
    this.penSize = size;
    if (this.ctx) {
      this.ctx.lineWidth = size;
    }
  }

  // Signature image upload method
  onSignatureImageSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];

    // Check if file is an image
    if (!file.type.match('image.*')) {
      this.errorMessage = 'Please select an image file (PNG, JPG, JPEG)';
      this.cdr.detectChanges();
      return;
    }

    // Check file size (max 2MB)
    const maxSignatureSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSignatureSize) {
      this.errorMessage = 'Signature image is too large. Maximum size is 2MB.';
      this.cdr.detectChanges();
      return;
    }

    // Create a FileReader to read the image
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        if (e.target?.result) {
          // Create an image element to get dimensions
          const img = new Image();
          img.onload = () => {
            // Process the image to ensure it's suitable for a signature
            this.processSignatureImage(img);
          };
          img.src = e.target.result as string;

          // Set the uploaded signature URL for preview
          this.uploadedSignatureUrl = this.sanitizer.bypassSecurityTrustResourceUrl(e.target.result as string);
          this.signatureDataUrl = e.target.result as string;
          this.cdr.detectChanges();
        }
      } catch (error) {
        console.error('Error processing signature image:', error);
        this.errorMessage = 'Error processing signature image. Please try again.';
        this.cdr.detectChanges();
      }
    };

    reader.onerror = () => {
      this.errorMessage = 'Error reading the image file. Please try again.';
      this.cdr.detectChanges();
    };

    // Read the image file
    reader.readAsDataURL(file);
  }

  // Process the signature image to ensure it's suitable
  private processSignatureImage(img: HTMLImageElement): void {
    try {
      // Create a canvas to process the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;

      // Set maximum dimensions for the signature
      const maxWidth = 400;
      const maxHeight = 200;

      // Calculate dimensions while maintaining aspect ratio
      let width = img.width;
      let height = img.height;

      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw the image on the canvas
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to data URL with maximum quality
      const processedSignatureDataUrl = canvas.toDataURL('image/png', 1.0);
      this.signatureDataUrl = processedSignatureDataUrl;
      this.cdr.detectChanges();
    } catch (error) {
      console.error('Error processing signature image:', error);
    }
  }

  // Signature position
  setSignaturePosition(position: 'bottom-left' | 'bottom-center' | 'bottom-right'): void {
    this.signaturePosition = position;
    this.cdr.detectChanges();
  }

  private getEventCoordinates(event: MouseEvent | TouchEvent, rect: DOMRect) {
    return {
      x: (event instanceof TouchEvent ? event.touches[0].clientX : event.clientX) - rect.left,
      y: (event instanceof TouchEvent ? event.touches[0].clientY : event.clientY) - rect.top
    };
  }

  clearSignature(): void {
    if (this.signatureType === 'draw') {
      const canvas = this.signaturePad.nativeElement;
      this.ctx.clearRect(0, 0, canvas.width, canvas.height);
      this.signatureDataUrl = undefined;
      this.undoStack = [];
      this.redoStack = [];
    } else if (this.signatureType === 'upload') {
      this.uploadedSignatureUrl = null;
      this.signatureDataUrl = undefined;
    }

    this.cdr.detectChanges();
  }

  // Utility methods for document info
  getFileType(): string {
    if (!this.pdfFile) return '';

    // IMPORTANT: Use ONLY file extension for type detection, not MIME type
    // This ensures we display the correct document type based on the actual file extension
    const fileExtension = this.pdfFile.name.split('.').pop()?.toLowerCase();

    console.log('Getting file type - Extension:', fileExtension);

    // Determine file type based ONLY on extension
    if (fileExtension === 'pdf') {
      return 'Document PDF';
    } else if (fileExtension === 'doc') {
      return 'Document Word (.doc)';
    } else if (fileExtension === 'docx') {
      return 'Document Word (.docx)';
    } else {
      // If we can't determine from extension, check the file name
      if (this.pdfFile.name.toLowerCase().endsWith('.pdf')) {
        return 'Document PDF';
      } else if (this.pdfFile.name.toLowerCase().endsWith('.doc')) {
        return 'Document Word (.doc)';
      } else if (this.pdfFile.name.toLowerCase().endsWith('.docx')) {
        return 'Document Word (.docx)';
      } else {
        // Last resort - check MIME type
        const fileType = this.pdfFile.type;
        if (fileType.includes('pdf')) {
          return 'Document PDF';
        } else if (fileType.includes('word') || fileType.includes('msword') || fileType.includes('officedocument')) {
          return 'Document Word';
        } else {
          return 'Document';
        }
      }
    }
  }

  getCurrentDate(): string {
    const now = new Date();
    return now.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // File handling
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];
    console.log('File selected:', file.name);
    console.log('File type:', file.type);
    console.log('File size:', file.size, 'bytes');

    // Check file size
    if (file.size > this.maxFileSize) {
      this.errorMessage = ERROR_MESSAGES.PDF_TOO_LARGE;
      this.cdr.detectChanges();
      return;
    }

    // Check file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const validExtensions = ['pdf', 'doc', 'docx'];

    if (!validExtensions.includes(fileExtension || '')) {
      this.errorMessage = 'Format de fichier non supporté. Veuillez utiliser PDF ou Word (.pdf, .doc, .docx).';
      this.cdr.detectChanges();
      return;
    }

    // File passed validation
    this.errorMessage = null;

    // Clean up previous preview
    this.clearFilePreview();

    // Determine file type for preview - this will also set this.pdfFile and create the URL
    this.determineFilePreviewType(file);

    this.cdr.detectChanges();
  }

  // Determine the type of file for preview
  private determineFilePreviewType(file: File): void {
    console.log('Determining file type for preview:', file);

    // Get file extension and type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const fileType = file.type;
    console.log('File extension:', fileExtension);
    console.log('File MIME type:', fileType);
    console.log('File size:', file.size);

    // Clear any previous error
    this.errorMessage = null;

    // IMPORTANT: Force the correct file extension if it doesn't match the MIME type
    let correctedFile = file;
    let correctedExtension = fileExtension;

    // If the file has a PDF MIME type but not a PDF extension, fix the filename
    if (fileType.includes('pdf') && fileExtension !== 'pdf') {
      console.log('File has PDF MIME type but incorrect extension, fixing filename');
      const newFileName = file.name.replace(/\.[^/.]+$/, '') + '.pdf';
      correctedFile = new File([file], newFileName, { type: file.type });
      correctedExtension = 'pdf';
    }
    // If the file has a Word MIME type but not a Word extension, fix the filename
    else if ((fileType.includes('word') || fileType.includes('msword') || fileType.includes('officedocument')) &&
             !['doc', 'docx'].includes(fileExtension || '')) {
      console.log('File has Word MIME type but incorrect extension, fixing filename');
      const newFileName = file.name.replace(/\.[^/.]+$/, '') + '.docx';
      correctedFile = new File([file], newFileName, { type: file.type });
      correctedExtension = 'docx';
    }
    // If the file has a generic MIME type, try to determine from the original filename
    else if (fileType === 'application/octet-stream' && this.originalFilename) {
      const origExt = this.originalFilename.split('.').pop()?.toLowerCase();
      if (origExt && ['pdf', 'doc', 'docx'].includes(origExt) && fileExtension !== origExt) {
        console.log('File has generic MIME type, using extension from original filename:', origExt);
        const newFileName = file.name.replace(/\.[^/.]+$/, '') + '.' + origExt;
        correctedFile = new File([file], newFileName, { type: file.type });
        correctedExtension = origExt;
      }
    }

    // Log if we made any corrections
    if (correctedFile !== file) {
      console.log('Corrected filename:', correctedFile.name);
      console.log('Corrected extension:', correctedExtension);
    }

    // Determine file type primarily by extension (most reliable method)
    if (correctedExtension === 'pdf') {
      console.log('Detected PDF file by extension');
      this.filePreviewType = 'pdf';

      // Store the file without changing its MIME type
      this.pdfFile = correctedFile;
      console.log('Storing PDF file with original MIME type:', correctedFile.type);

      // Create a URL for the file that can be used for viewing
      try {
        const fileUrl = URL.createObjectURL(this.pdfFile);
        this.uploadedFileUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileUrl);

        // Store cleanup function
        const oldCleanup = this.cleanupFilePreview;
        this.cleanupFilePreview = () => {
          if (oldCleanup) oldCleanup();
          URL.revokeObjectURL(fileUrl);
        };

        console.log('Created URL for PDF preview:', fileUrl);
      } catch (error) {
        console.error('Error creating URL for PDF preview:', error);
      }

      this.cdr.detectChanges();
    }
    else if (['doc', 'docx'].includes(correctedExtension || '')) {
      console.log('Detected Word file by extension');
      this.filePreviewType = 'word';

      // Store the file with its original MIME type and content
      this.pdfFile = correctedFile;
      console.log('Storing Word file with original MIME type:', correctedFile.type);

      // Create a URL for the file that can be used for viewing
      try {
        const fileUrl = URL.createObjectURL(this.pdfFile);
        this.uploadedFileUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileUrl);

        // Store cleanup function
        const oldCleanup = this.cleanupFilePreview;
        this.cleanupFilePreview = () => {
          if (oldCleanup) oldCleanup();
          URL.revokeObjectURL(fileUrl);
        };

        console.log('Created URL for Word preview:', fileUrl);
      } catch (error) {
        console.error('Error creating URL for Word preview:', error);
      }

      this.cdr.detectChanges();
    }
    else {
      // If we can't determine by extension, try MIME type
      if (fileType.includes('pdf')) {
        console.log('Detected PDF file by MIME type');
        this.filePreviewType = 'pdf';
        this.pdfFile = file;
      }
      else if (fileType.includes('word') || fileType.includes('office') || fileType.includes('document')) {
        console.log('Detected Word file by MIME type');
        this.filePreviewType = 'word';
        this.pdfFile = file;
      }
      else {
        // Default to PDF as fallback
        console.log('Could not determine file type, defaulting to PDF');
        this.filePreviewType = 'pdf';
        this.pdfFile = file;
      }

      this.cdr.detectChanges();
    }
  }

  /**
   * Open the document in a new tab for external viewing
   * Works for both PDF and Word documents
   */
  openExternalViewer(): void {
    if (!this.pdfFile) {
      console.error('No file available to open');
      return;
    }

    try {
      console.log('Opening file in external viewer:', this.pdfFile.name);
      console.log('File type:', this.pdfFile.type);
      console.log('File size:', this.pdfFile.size, 'bytes');

      // Create a new File object with the correct MIME type based on extension
      const fileExtension = this.pdfFile.name.split('.').pop()?.toLowerCase();
      let fileToOpen = this.pdfFile;

      // Force the correct MIME type based on file extension
      if (fileExtension === 'pdf' && !this.pdfFile.type.includes('pdf')) {
        console.log('Forcing PDF MIME type for external viewing');
        fileToOpen = new File([this.pdfFile], this.pdfFile.name, { type: 'application/pdf' });
      }
      else if ((fileExtension === 'doc' || fileExtension === 'docx') &&
               !this.pdfFile.type.includes('word') && !this.pdfFile.type.includes('office')) {
        console.log('Forcing Word MIME type for external viewing');
        fileToOpen = new File(
          [this.pdfFile],
          this.pdfFile.name,
          { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }
        );
      }

      // Create a URL for the file
      const fileUrl = URL.createObjectURL(fileToOpen);
      console.log('Created URL for external viewing:', fileUrl);

      // Open in a new tab
      window.open(fileUrl, '_blank');

      // Clean up the URL after opening
      setTimeout(() => {
        URL.revokeObjectURL(fileUrl);
      }, 1000);
    } catch (error) {
      console.error('Error opening file in external viewer:', error);
      this.errorMessage = 'Error opening file. Please try again.';
      this.cdr.detectChanges();
    }
  }

  /**
   * Reload the PDF preview
   * This is a public method that can be called from the template
   */
  reloadPdfPreview(): void {
    if (!this.pdfFile) {
      console.error('No file to reload');
      return;
    }

    console.log('Reloading file preview');

    // Clear any previous error message
    this.errorMessage = null;

    // Get file extension
    const fileExtension = this.pdfFile.name.split('.').pop()?.toLowerCase();
    console.log('File extension:', fileExtension);
    console.log('File type:', this.pdfFile.type);
    console.log('File size:', this.pdfFile.size);

    // Force the file type based on extension
    let fileToPreview = this.pdfFile;

    if (fileExtension === 'pdf') {
      console.log('Forcing file type to PDF based on extension');
      fileToPreview = new File([this.pdfFile], this.pdfFile.name, { type: 'application/pdf' });
      this.pdfFile = fileToPreview; // Update the stored file
    } else if (fileExtension === 'doc' || fileExtension === 'docx') {
      console.log('Forcing file type to Word document based on extension');
      fileToPreview = new File([this.pdfFile], this.pdfFile.name,
        { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
      this.pdfFile = fileToPreview; // Update the stored file
    }

    try {
      // Create a URL for the file
      const fileUrl = URL.createObjectURL(fileToPreview);
      console.log('Created object URL for preview:', fileUrl);

      // Use direct URL for better compatibility
      this.uploadedFileUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileUrl);
      console.log('Set uploadedFileUrl:', this.uploadedFileUrl);

      // Set file type based on extension
      if (fileExtension === 'pdf') {
        this.filePreviewType = 'pdf';
      } else if (fileExtension === 'doc' || fileExtension === 'docx') {
        this.filePreviewType = 'word';
      }

      // Store cleanup function
      const oldCleanup = this.cleanupFilePreview;
      this.cleanupFilePreview = () => {
        if (oldCleanup) oldCleanup();
        URL.revokeObjectURL(fileUrl);
      };

      // Force change detection
      this.cdr.detectChanges();
    } catch (error) {
      console.error('Error reloading file preview:', error);
      this.errorMessage = 'Error displaying file. Please try again.';
      this.cdr.detectChanges();
    }
  }

  // Clear the file preview
  clearFilePreview(): void {
    if (this.cleanupFilePreview) {
      this.cleanupFilePreview();
      this.cleanupFilePreview = undefined;
    }

    // Clear any pending timeouts
    if (this.pdfViewerCheckTimeout) {
      clearTimeout(this.pdfViewerCheckTimeout);
      this.pdfViewerCheckTimeout = null;
    }

    this.uploadedFileUrl = null;
    this.filePreviewType = null;
    this.showPdfFallback = false;
    this.cdr.detectChanges();
  }

  // Undo/Redo methods
  undo(): void {
    if (this.undoStack.length === 0) return;

    const lastState = this.undoStack.pop();
    if (!lastState) return;

    this.redoStack.push(this.ctx.getImageData(0, 0,
      this.signaturePad.nativeElement.width,
      this.signaturePad.nativeElement.height
    ));

    this.ctx.putImageData(lastState, 0, 0);
    this.signatureDataUrl = this.signaturePad.nativeElement.toDataURL();
  }

  redo(): void {
    if (this.redoStack.length === 0) return;

    const nextState = this.redoStack.pop();
    if (!nextState) return;

    this.undoStack.push(this.ctx.getImageData(0, 0,
      this.signaturePad.nativeElement.width,
      this.signaturePad.nativeElement.height
    ));

    this.ctx.putImageData(nextState, 0, 0);
    this.signatureDataUrl = this.signaturePad.nativeElement.toDataURL();
  }

  // Method to check if signature is valid
  isSignatureValid(): boolean {
    if (this.signatureType === 'draw') {
      if (!this.signaturePad || !this.ctx) return false;

      const imageData = this.ctx.getImageData(
        0, 0,
        this.signaturePad.nativeElement.width,
        this.signaturePad.nativeElement.height
      );
      return imageData.data.some((_, index) => index % 4 === 3 && imageData.data[index] > 0);
    } else if (this.signatureType === 'upload') {
      return !!this.uploadedSignatureUrl && !!this.signatureDataUrl;
    }

    return false;
  }

  // Method to submit the signature
  submitSignature(): void {
    if (!this.isSignatureValid() || !this.pdfFile) {
      this.errorMessage = 'Please provide a valid signature and document.';
      this.cdr.detectChanges();
      return;
    }

    if (this.signForm.invalid) {
      this.validateFormFields();
      this.errorMessage = 'Please fill in all required fields correctly.';
      this.cdr.detectChanges();
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;
    this.successMessage = null;
    this.cdr.detectChanges();

    // Get form values
    const formValues = this.signForm.value;

    // Create form data for submission
    const formData = new FormData();

    // Use the exact field names expected by the backend API
    formData.append('nom_contrat', formValues.fullName);
    formData.append('email_signataire', formValues.email);
    formData.append('signature_position', this.signaturePosition);

    // Add signature data - backend expects a string, not a blob
    if (this.signatureDataUrl) {
      formData.append('signature_data', this.signatureDataUrl);
    }

    // If we have an unsigned contract ID, include it
    if (this.unsignedContractId) {
      formData.append('unsigned_contract_id', this.unsignedContractId.toString());
    }

    // Determine if it's a PDF or Word document based on file extension (most reliable method)
    const fileExtension = this.pdfFile.name.split('.').pop()?.toLowerCase();

    // Log the file extension to help with debugging
    console.log(`File extension: ${fileExtension}`);
    console.log(`File MIME type: ${this.pdfFile.type}`);

    // Check both extension and MIME type for more reliable detection
    const isPdf = fileExtension === 'pdf' || this.pdfFile.type.includes('pdf');
    const isWord = fileExtension === 'doc' || fileExtension === 'docx' ||
                  this.pdfFile.type.includes('word') ||
                  this.pdfFile.type.includes('msword') ||
                  this.pdfFile.type.includes('officedocument');

    // Use the original file without any conversion
    let fileToSubmit = this.pdfFile;

    console.log(`Using original file without conversion: ${fileToSubmit.name} (${fileToSubmit.type})`);

    // Do not modify the file's content or MIME type - use it as is
    // This ensures we preserve the original file format

    console.log(`Document type: ${isPdf ? 'PDF' : isWord ? 'Word' : 'Unknown'}`);
    console.log(`File details: ${fileToSubmit.name} (${fileToSubmit.type}, ${fileToSubmit.size} bytes)`);

    // Now add the file with the correct MIME type to the form data
    // Backend expects 'pdf_file' field name for both PDF and Word documents
    formData.append('pdf_file', fileToSubmit);

    // Use the appropriate signing method based on file type
    // For Word documents, use the Word endpoint, otherwise use the main endpoint
    const signingObservable = isWord
      ? this.contractSigningService.signWordDocument(formData)
      : this.contractSigningService.signPdfContract(formData);

    // Log the form data before sending
    console.log('Form data being sent:');
    formData.forEach((value, key) => {
      if (key === 'pdf_file') {
        const file = value as File;
        console.log(`${key}: ${file.name} (${file.type}, ${file.size} bytes)`);
      } else if (key === 'signature_data') {
        console.log(`${key}: [Signature data string]`);
      } else {
        console.log(`${key}: ${value}`);
      }
    });

    signingObservable.pipe(
      takeUntil(this.destroy$),
      finalize(() => {
        this.isLoading = false;
        this.cdr.detectChanges();
      })
    )
    .subscribe({
      next: (response: any) => {
        console.log('Response received:', response);

        // Handle different response types (HttpEventType)
        // 0: sent, 1: upload progress, 2: download progress, 3: response header received, 4: response complete
        if (response.type === 4) {
          console.log('Signature successful:', response);
          this.successMessage = 'Document signed successfully!';

          // Navigate to dashboard after successful submission
          setTimeout(() => {
            this.router.navigate(['/dashboard']);
          }, 2000);
        } else if (response.type === 1) {
          // Upload progress
          console.log('Upload progress:', response);
        }

        this.cdr.detectChanges();
      },
      error: (error: any) => {
        console.error('Error signing document:', error);

        // Try to extract more detailed error information
        let errorMessage = 'Error signing document. ';

        if (error.error && typeof error.error === 'object') {
          if (error.error.message) {
            errorMessage += error.error.message;
          }

          // Check for validation errors
          if (error.error.errors) {
            errorMessage += ' Validation errors: ';
            for (const field in error.error.errors) {
              errorMessage += `${field}: ${error.error.errors[field].join(', ')}. `;
            }
          }
        } else if (error.message) {
          errorMessage = error.message;
        } else {
          errorMessage += 'Unknown error. Please try again.';
        }

        // For Word documents specifically
        if (this.pdfFile && (
          this.pdfFile.name.toLowerCase().endsWith('.docx') ||
          this.pdfFile.name.toLowerCase().endsWith('.doc')
        )) {
          errorMessage = 'Failed to sign Word document. Please try again.';
        }

        this.errorMessage = errorMessage;

        // Even though there was an error, the document might still be signed in the database
        // Let's navigate to the dashboard after a delay to check
        setTimeout(() => {
          this.router.navigate(['/dashboard']);
        }, 3000);

        this.cdr.detectChanges();
      }
    });
  }

  // We no longer need the dataURLtoBlob method as we're sending the signature data directly

  // Validate form fields and populate fieldErrors
  private validateFormFields(): void {
    this.fieldErrors = {};
    const controls = this.signForm.controls;

    if (controls['fullName'].invalid) {
      this.fieldErrors['fullName'] = controls['fullName'].hasError('required')
        ? ERROR_MESSAGES.REQUIRED
        : ERROR_MESSAGES.INVALID_NAME;
    }

    if (controls['email'].invalid) {
      this.fieldErrors['email'] = controls['email'].hasError('required')
        ? ERROR_MESSAGES.REQUIRED
        : ERROR_MESSAGES.INVALID_EMAIL;
    }

    if (controls['consent'].invalid) {
      this.fieldErrors['consent'] = ERROR_MESSAGES.CONSENT_REQUIRED;
    }

    if (!this.isSignatureValid()) {
      this.fieldErrors['signature'] = ERROR_MESSAGES.SIGNATURE_REQUIRED;
    }
  }
}
