import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { PartnerContractsComponent, Partner, Contract } from './partner-contracts.component';
import { PartnerService } from '../../services/partner.service';

describe('PartnerContractsComponent', () => {
  let component: PartnerContractsComponent;
  let fixture: ComponentFixture<PartnerContractsComponent>;
  let partnerServiceSpy: jasmine.SpyObj<PartnerService>;

  const mockPartners: Partner[] = [
    { id: 1, name: 'Microsoft', logo: 'fab fa-microsoft', description: 'Microsoft Corporation' },
    { id: 2, name: 'Cloud', logo: 'fas fa-cloud', description: 'Cloud Services' }
  ];

  const mockContracts: Contract[] = [
    { id: 1, partnerId: 1, name: 'Service Agreement', description: 'Microsoft Service Agreement', filename: 'ms-agreement.docx', type: 'word' },
    { id: 2, partnerId: 1, name: 'License Agreement', description: 'Microsoft License Agreement', filename: 'ms-license.pdf', type: 'pdf' }
  ];

  beforeEach(async () => {
    const partnerSpy = jasmine.createSpyObj('PartnerService', ['getPartners', 'getPartnerContracts']);

    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        PartnerContractsComponent
      ],
      providers: [
        { provide: PartnerService, useValue: partnerSpy }
      ]
    }).compileComponents();

    partnerServiceSpy = TestBed.inject(PartnerService) as jasmine.SpyObj<PartnerService>;
    partnerServiceSpy.getPartners.and.returnValue(of({ success: true, partners: mockPartners }));
    partnerServiceSpy.getPartnerContracts.and.returnValue(of({ success: true, contracts: mockContracts }));
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PartnerContractsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load partners on init', () => {
    expect(partnerServiceSpy.getPartners).toHaveBeenCalled();
    expect(component.partners).toEqual(mockPartners);
  });

  it('should load contracts when a partner is selected', () => {
    component.onPartnerSelected(1);
    expect(partnerServiceSpy.getPartnerContracts).toHaveBeenCalledWith(1);
    expect(component.contracts).toEqual(mockContracts);
  });

  it('should get the selected partner name', () => {
    component.selectedPartnerId = 1;
    component.partners = mockPartners;
    expect(component.getSelectedPartnerName()).toBe('Microsoft');
  });
});
