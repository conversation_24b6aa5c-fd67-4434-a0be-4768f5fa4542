<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Validation\Rules\Password;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * Accept terms and conditions
     */
    public function acceptTerms(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'message' => 'Unauthenticated'
                ], 401);
            }
            
            $user->terms_accepted = true;
            $user->terms_accepted_at = Carbon::now();
            $user->save();
            
            return response()->json([
                'message' => 'Terms and conditions accepted successfully',
                'user' => $user
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to accept terms and conditions',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Register a new user
     */
    public function register(Request $request)
    {
        try {
            $request->validate([
                'firstname' => 'required|string|max:255',
                'lastname' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => ['required', 'confirmed', Password::defaults()],
                'terms' => 'required|boolean',
            ]);

            $userData = [
                'firstname' => $request->firstname,
                'lastname' => $request->lastname,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ];
            
            // If terms were accepted, record this
            if ($request->terms) {
                $userData['terms_accepted'] = true;
                $userData['terms_accepted_at'] = Carbon::now();
            }

            $user = User::create($userData);
            
            // Create a token for the user
            $token = $user->createToken('auth-token')->plainTextToken;

            return response()->json([
                'message' => 'Registration successful',
                'user' => $user,
                'token' => $token
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Registration failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle forgot password request
     */
    public function forgotPassword(Request $request)
    {
        try {
            // Log the request for debugging
            \Illuminate\Support\Facades\Log::info('Forgot password request received', ['email' => $request->email]);

            // Validate email format
            $request->validate([
                'email' => 'required|email|max:255'
            ]);

            // Find user by email
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                \Illuminate\Support\Facades\Log::warning('Email not found', ['email' => $request->email]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Email address not found in our system.'
                ], 404);
            }

            // Check if the user's account is active
            if (!$user->is_active) {
                \Illuminate\Support\Facades\Log::warning('Inactive account', ['email' => $request->email]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'This account is not active. Please contact support.'
                ], 403);
            }

            // Generate a unique token
            $token = Str::random(64);

            try {
                // Store token in database (skip this for now to simplify debugging)
                // We'll just log it instead
                \Illuminate\Support\Facades\Log::info('Would store token in database', [
                    'email' => $request->email,
                    'token' => $token
                ]);

                // Create a reset URL
                $resetUrl = url("/reset-password?token=$token&email=" . urlencode($request->email));

                // Log the reset URL
                \Illuminate\Support\Facades\Log::info('Password reset URL generated', ['url' => $resetUrl]);

                // For development, always return success with the reset URL
                return response()->json([
                    'status' => 'email_sent',
                    'message' => 'Password reset instructions have been sent to your email.',
                    'debug_url' => $resetUrl
                ]);

            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error generating reset URL', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                throw $e;
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Illuminate\Support\Facades\Log::warning('Validation failed', ['errors' => $e->errors()]);

            return response()->json([
                'status' => 'error',
                'message' => 'Invalid email format.',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            // Log detailed error information
            \Illuminate\Support\Facades\Log::error('Password reset request failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // For development, include detailed error information in the response
            return response()->json([
                'status' => 'error',
                'message' => 'An unexpected error occurred. Please try again later.',
                'error_details' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ], 500);
        }
    }


    public function resetPassword(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
                'token' => 'required|string',
                'password' => 'required|min:8|confirmed'
            ]);

            // Find the token record
            $record = DB::table('password_reset_tokens')
                        ->where('email', $request->email)
                        ->first();

            if (!$record) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No password reset request found for this email.'
                ], 400);
            }

            if (!Hash::check($request->token, $record->token)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid reset token.'
                ], 400);
            }

            if (Carbon::parse($record->created_at)->addMinutes(60)->isPast()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Reset token has expired. Please request a new password reset link.'
                ], 400);
            }

            // Find the user
            $user = User::where('email', $request->email)->first();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User not found.'
                ], 404);
            }

            // Update the user's password
            $user->password = Hash::make($request->password);
            $user->save();

            // Delete the token from the password_reset_tokens table
            DB::table('password_reset_tokens')->where('email', $request->email)->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Password has been reset successfully.'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Password reset failed: ' . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An unexpected error occurred. Please try again later.',
                'debug_info' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }



    public function updateSettings(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'message' => 'User not authenticated'
                ], 401);
            }

            $validated = $request->validate([
                'firstname' => 'required|string|max:255',
                'lastname' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $user->id,
                'phone' => 'nullable|string|max:20',
            ]);

            $user->update($validated);

            // Return user data in a consistent format
            $userData = [
                'id' => $user->id,
                'firstname' => $user->firstname,
                'lastname' => $user->lastname,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
                'avatar_url' => $user->avatar_url,
                'is_active' => $user->is_active,
                'is_admin' => $user->is_admin
            ];

            return response()->json([
                'message' => 'Profile updated successfully',
                'user' => $userData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the authenticated user's profile
     */
    public function getUser(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Ensure we're explicitly loading these fields
            $userData = [
                'id' => $user->id,
                'firstname' => $user->firstname,
                'lastname' => $user->lastname,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
                'avatar_url' => $user->avatar_url,
                'is_active' => $user->is_active,
                'is_admin' => $user->is_admin
            ];

            \Illuminate\Support\Facades\Log::info('User data being returned:', $userData);

            return response()->json([
                'user' => $userData
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error fetching user profile: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error fetching user profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'message' => 'User not authenticated'
                ], 401);
            }

            $validated = $request->validate([
                'firstname' => 'required|string|max:255',
                'lastname' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $user->id,
                'phone' => 'nullable|string|max:20'
            ]);

            $user->update($validated);

            // Return user data in a consistent format
            $userData = [
                'id' => $user->id,
                'firstname' => $user->firstname,
                'lastname' => $user->lastname,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
                'avatar_url' => $user->avatar_url,
                'is_active' => $user->is_active,
                'is_admin' => $user->is_admin
            ];

            return response()->json([
                'message' => 'Profile updated successfully',
                'user' => $userData
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error updating profile: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error updating profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * User login
     */
    public function login(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|string|email',
                'password' => 'required|string',
            ]);

            $user = User::where('email', $request->email)->first();

            if (!$user || !Hash::check($request->password, $user->password)) {
                return response()->json([
                    'message' => 'Invalid credentials'
                ], 401);
            }

            $token = $user->createToken('auth_token')->plainTextToken;

            // Create a user data object with explicit fields
            $userData = [
                'id' => $user->id,
                'firstname' => $user->firstname,
                'lastname' => $user->lastname,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
                'avatar_url' => $user->avatar_url,
                'is_active' => $user->is_active,
                'is_admin' => $user->is_admin
            ];

            return response()->json([
                'message' => 'Login successful',
                'user' => $userData,
                'token' => $token
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Login failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload profile image
     */
    public function uploadProfileImage(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'message' => 'User not authenticated'
                ], 401);
            }

            $request->validate([
                'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            if ($request->hasFile('avatar')) {
                $avatarsPath = public_path('uploads/avatars');

                // Create avatars directory if it doesn't exist
                if (!file_exists($avatarsPath)) {
                    mkdir($avatarsPath, 0755, true);
                }

                // Delete old avatar if it exists
                if ($user->avatar && file_exists($avatarsPath . '/' . $user->avatar)) {
                    unlink($avatarsPath . '/' . $user->avatar);
                }

                // Generate unique filename
                $avatarName = $user->id . '_' . time() . '.' . $request->avatar->extension();

                // Move the file
                $request->avatar->move($avatarsPath, $avatarName);

                // Update user record
                $user->avatar = $avatarName;
                $user->save();

                // Return updated user data
                $userData = [
                    'id' => $user->id,
                    'firstname' => $user->firstname,
                    'lastname' => $user->lastname,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'avatar' => $user->avatar,
                    'avatar_url' => $user->avatar_url,
                    'is_active' => $user->is_active,
                    'is_admin' => $user->is_admin
                ];

                return response()->json([
                    'message' => 'Profile image uploaded successfully',
                    'user' => $userData
                ]);
            }

            return response()->json([
                'message' => 'No image file uploaded'
            ], 400);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Profile image upload error: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to upload profile image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getAvatar($filename)
    {
        $path = public_path('uploads/avatars/' . $filename);

        if (!file_exists($path)) {
            return response()->json([
                'message' => 'Image not found'
            ], 404);
        }

        return response()->file($path);
    }

    /**
     * Get Google OAuth URL
     */
    public function getGoogleAuthUrl()
    {
        // Use the OAuthService instead
        $oauthService = app(\App\Services\OAuthService::class);
        return $oauthService->getGoogleAuthUrl();
    }

    /**
     * Handle Google OAuth callback
     */
    public function handleGoogleCallback(Request $request)
    {
        try {
            // Use the OAuthService instead
            $oauthService = app(\App\Services\OAuthService::class);
            $result = $oauthService->handleGoogleCallback($request->input('code'));

            $googleUser = $result['user'] ?? null;

            if (!$googleUser || !isset($googleUser['email'])) {
                throw new \Exception('Failed to retrieve valid user info from Google.');
            }

            // Split the full name into first and last name
            $name = $googleUser['name'] ?? '';
            $nameParts = explode(' ', $name);
            $firstname = $googleUser['given_name'] ?? $nameParts[0] ?? 'User';
            $lastname = $googleUser['family_name'] ?? (count($nameParts) > 1 ? end($nameParts) : '');

            // Find or create user
            $user = User::updateOrCreate(
                ['email' => $googleUser['email']],
                [
                    'firstname' => $firstname,
                    'lastname' => $lastname,
                    'google_id' => $googleUser['sub'] ?? null,
                    'avatar' => $googleUser['picture'] ?? null,
                    'password' => Hash::make(Str::random(24)) // Set a random password for Google users
                ]
            );

            $token = $user->createToken('auth-token')->plainTextToken;

            return response()->json([
                'user' => $user,
                'token' => $token
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to authenticate with Google',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 422);
        }
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'current_password' => ['required', 'string'],
                'password' => ['required', 'string', 'min:8', 'confirmed', Password::defaults()],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();

            if (!Hash::check($request->current_password, $user->password)) {
                return response()->json([
                    'message' => 'Current password is incorrect'
                ], 401);
            }

            $user->password = Hash::make($request->password);
            $user->save();

            return response()->json([
                'message' => 'Password updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating password',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
