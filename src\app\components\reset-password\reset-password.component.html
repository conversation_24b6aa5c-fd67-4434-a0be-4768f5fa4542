<div class="component-container">
  <div class="reset-password-container">
    <div class="reset-password-left">
      <div class="logo-section">
        <img src="logo.png" alt="ContratPro Logo" class="logo" />
        <a routerLink="/" class="back-link">Back to website</a>
      </div>
      <div class="hero-content">
        <h2>Create New<br>Password</h2>
        <p class="hero-subtitle">Your new password must be different from previously used passwords.</p>
        <div class="hero-dots">
          <span class="dot active"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>
    </div>

    <div class="reset-password-right">
      <div class="reset-password-form-container">
        <div *ngIf="!resetComplete">
          <h1>Reset Password</h1>
          <p class="signin-link">Remember your password? <a routerLink="/signin">Sign in</a></p>

          <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="reset-password-form">
            <!-- Error Message -->
            <div *ngIf="errorMessage" class="message error">
              <i class="bi bi-exclamation-triangle-fill"></i>
              {{ errorMessage }}
            </div>

            <div class="form-field">
              <label for="password">New Password</label>
              <div class="password-input">
                <input
                  [type]="showPassword ? 'text' : 'password'"
                  id="password"
                  formControlName="password"
                  placeholder="Enter new password"
                  [class.is-invalid]="password?.invalid && password?.touched"
                />
                <button
                  type="button"
                  class="toggle-password"
                  (click)="togglePasswordVisibility()"
                >
                  <i class="bi" [ngClass]="showPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
                </button>
              </div>
              <div *ngIf="password?.invalid && password?.touched" class="error-message">
                <span *ngIf="password?.errors?.['required']">Password is required</span>
                <span *ngIf="password?.errors?.['minlength']">Password must be at least 8 characters</span>
              </div>
            </div>

            <div class="form-field">
              <label for="passwordConfirmation">Confirm Password</label>
              <div class="password-input">
                <input
                  [type]="showConfirmPassword ? 'text' : 'password'"
                  id="passwordConfirmation"
                  formControlName="passwordConfirmation"
                  placeholder="Confirm new password"
                  [class.is-invalid]="passwordConfirmation?.invalid && passwordConfirmation?.touched || passwordMismatch"
                />
                <button
                  type="button"
                  class="toggle-password"
                  (click)="toggleConfirmPasswordVisibility()"
                >
                  <i class="bi" [ngClass]="showConfirmPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
                </button>
              </div>
              <div *ngIf="(passwordConfirmation?.invalid && passwordConfirmation?.touched) || passwordMismatch" class="error-message">
                <span *ngIf="passwordConfirmation?.errors?.['required']">Confirm password is required</span>
                <span *ngIf="passwordMismatch">Passwords do not match</span>
              </div>
            </div>

            <div class="password-strength" *ngIf="password?.value">
              <div class="strength-label">
                <span>Password Strength:</span>
                <span [ngClass]="getPasswordStrengthClass()">{{ getPasswordStrength() }}</span>
              </div>
              <div class="strength-meter">
                <div class="strength-bar" [ngClass]="getPasswordStrengthClass()"></div>
              </div>
              <div class="password-requirements">
                <div class="requirement" [class.met]="hasMinLength()">
                  <i class="bi" [ngClass]="hasMinLength() ? 'bi-check-circle-fill' : 'bi-circle'"></i>
                  <span>At least 8 characters</span>
                </div>
                <div class="requirement" [class.met]="hasUppercase()">
                  <i class="bi" [ngClass]="hasUppercase() ? 'bi-check-circle-fill' : 'bi-circle'"></i>
                  <span>At least 1 uppercase letter</span>
                </div>
                <div class="requirement" [class.met]="hasLowercase()">
                  <i class="bi" [ngClass]="hasLowercase() ? 'bi-check-circle-fill' : 'bi-circle'"></i>
                  <span>At least 1 lowercase letter</span>
                </div>
                <div class="requirement" [class.met]="hasNumber()">
                  <i class="bi" [ngClass]="hasNumber() ? 'bi-check-circle-fill' : 'bi-circle'"></i>
                  <span>At least 1 number</span>
                </div>
              </div>
            </div>

            <button
              type="submit"
              class="submit-button"
              [disabled]="loading || resetPasswordForm.invalid || passwordMismatch"
            >
              <span *ngIf="!loading">Reset Password</span>
              <span *ngIf="loading">
                <i class="bi bi-arrow-repeat spinning"></i>
                Processing...
              </span>
            </button>
          </form>
        </div>

        <!-- Success View -->
        <div *ngIf="resetComplete" class="success-container">
          <div class="success-icon">
            <i class="bi bi-check-circle-fill"></i>
          </div>
          <h2>Password Reset Successful</h2>
          <p class="success-message">
            {{ successMessage }}
          </p>
          <p class="instruction-text">
            Your password has been successfully reset. You can now sign in with your new password.
          </p>
          <div class="action-buttons">
            <a routerLink="/signin" class="primary-button">Sign In</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
