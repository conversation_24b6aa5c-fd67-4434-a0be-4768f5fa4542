<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\SignedContract;
use App\Models\UnsignedContract;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AdminController extends Controller
{
    /**
     * Get dashboard statistics
     */
    public function getDashboardStats(Request $request)
    {
        // Get date range filter
        $dateRange = $request->input('date_range', 'last12Months');
        $startDate = $this->getStartDateFromRange($dateRange);

        // Get user statistics
        $totalUsers = User::count();
        $adminUsers = User::where('is_admin', true)->count();
        $regularUsers = $totalUsers - $adminUsers;

        // Get contract statistics
        $totalContracts = SignedContract::count() + UnsignedContract::count();
        $signedContracts = SignedContract::count();
        $unsignedContracts = UnsignedContract::count();
        $pendingContracts = UnsignedContract::where('status', 'pending')->count();

        // Get monthly user growth data
        $userGrowthData = $this->getUserGrowthData($startDate);

        // Get monthly contract data
        $signedContractsPerMonth = $this->getContractsPerMonth($startDate, SignedContract::class);
        $unsignedContractsPerMonth = $this->getContractsPerMonth($startDate, UnsignedContract::class);

        // Get recent users
        $recentUsers = User::orderBy('created_at', 'desc')
            ->take(10)
            ->get(['id', 'firstname', 'lastname', 'email', 'is_admin', 'created_at']);

        // Get active users
        $activeUsers = User::orderBy('updated_at', 'desc')
            ->take(10)
            ->get(['id', 'firstname', 'lastname', 'email', 'updated_at as last_activity']);

        // Get recent contracts
        $recentContracts = $this->getRecentContracts();

        return response()->json([
            'total_users' => $totalUsers,
            'admin_users' => $adminUsers,
            'regular_users' => $regularUsers,
            'total_contracts' => $totalContracts,
            'signed_contracts' => $signedContracts,
            'unsigned_contracts' => $unsignedContracts,
            'pending_contracts' => $pendingContracts,
            'users_per_month' => $userGrowthData,
            'signed_contracts_per_month' => $signedContractsPerMonth,
            'unsigned_contracts_per_month' => $unsignedContractsPerMonth,
            'recent_users' => $recentUsers,
            'active_users' => $activeUsers,
            'recent_contracts' => $recentContracts
        ]);
    }

    /**
     * Get all users
     */
    public function getUsers()
    {
        $users = User::orderBy('created_at', 'desc')->get();
        return response()->json($users);
    }

    /**
     * Get a specific user
     */
    public function getUser($id)
    {
        $user = User::findOrFail($id);
        return response()->json($user);
    }

    /**
     * Create a new user
     */
    public function createUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'is_admin' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::create([
            'firstname' => $request->firstname,
            'lastname' => $request->lastname,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_admin' => $request->is_admin ?? false,
            'is_active' => true
        ]);

        return response()->json($user, 201);
    }

    /**
     * Update an existing user
     */
    public function updateUser(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'firstname' => 'string|max:255',
            'lastname' => 'string|max:255',
            'email' => 'string|email|max:255|unique:users,email,' . $id,
            'password' => 'nullable|string|min:8',
            'is_admin' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Update user fields
        if ($request->has('firstname')) {
            $user->firstname = $request->firstname;
        }

        if ($request->has('lastname')) {
            $user->lastname = $request->lastname;
        }

        if ($request->has('email')) {
            $user->email = $request->email;
        }

        if ($request->has('password') && $request->password) {
            $user->password = Hash::make($request->password);
        }

        if ($request->has('is_admin')) {
            $user->is_admin = $request->is_admin;
        }

        $user->save();

        return response()->json($user);
    }

    /**
     * Delete a user
     */
    public function deleteUser($id)
    {
        $user = User::findOrFail($id);
        $user->delete();

        return response()->json(['message' => 'User deleted successfully']);
    }

    /**
     * Helper method to get start date from range
     */
    private function getStartDateFromRange($range)
    {
        $now = Carbon::now();

        switch ($range) {
            case 'last30Days':
                return $now->copy()->subDays(30);
            case 'last3Months':
                return $now->copy()->subMonths(3);
            case 'last6Months':
                return $now->copy()->subMonths(6);
            case 'last12Months':
                return $now->copy()->subMonths(12);
            case 'allTime':
                return Carbon::createFromTimestamp(0);
            default:
                return $now->copy()->subMonths(12);
        }
    }

    /**
     * Helper method to get user growth data
     */
    private function getUserGrowthData($startDate)
    {
        return DB::table('users')
            ->select(DB::raw('MONTH(created_at) as month, COUNT(*) as count'))
            ->where('created_at', '>=', $startDate)
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy('month')
            ->get();
    }

    /**
     * Helper method to get contracts per month
     */
    private function getContractsPerMonth($startDate, $contractClass)
    {
        return DB::table((new $contractClass)->getTable())
            ->select(DB::raw('MONTH(created_at) as month, COUNT(*) as count'))
            ->where('created_at', '>=', $startDate)
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy('month')
            ->get();
    }

    /**
     * Helper method to get recent contracts
     */
    private function getRecentContracts()
    {
        $signedContracts = SignedContract::with('user')
            ->select('id', 'nom_contrat as name', 'user_id', 'created_at')
            ->selectRaw("'signed' as type")
            ->selectRaw("'completed' as status")
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($contract) {
                $contract->user = $contract->user ?
                    $contract->user->firstname . ' ' . $contract->user->lastname : 'Unknown';
                return $contract;
            });

        $unsignedContracts = UnsignedContract::with('user')
            ->select('id', 'nom_contrat as name', 'user_id', 'status', 'created_at')
            ->selectRaw("'unsigned' as type")
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($contract) {
                $contract->user = $contract->user ?
                    $contract->user->firstname . ' ' . $contract->user->lastname : 'Unknown';
                return $contract;
            });

        return $signedContracts->concat($unsignedContracts)
            ->sortByDesc('created_at')
            ->take(10)
            ->values();
    }
}
