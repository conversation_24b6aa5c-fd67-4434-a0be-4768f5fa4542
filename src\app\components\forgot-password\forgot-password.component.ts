import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink]
})
export class ForgotPasswordComponent implements OnInit {
  forgotPasswordForm!: FormGroup;
  loading = false;
  errorMessage = '';
  successMessage = '';
  emailSent = false;
  private userEmail = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  initializeForm(): void {
    this.forgotPasswordForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  onSubmit(): void {
    if (this.forgotPasswordForm.valid && !this.loading) {
      this.loading = true;
      this.errorMessage = '';
      this.successMessage = '';

      const email = this.forgotPasswordForm.value.email;
      this.userEmail = email;

      this.authService.forgotPassword(email).subscribe({
        next: (response) => {
          // Don't set successMessage for initial submission to avoid duplicate messages
          // this.successMessage = response.message || 'Password reset link sent successfully!';
          this.emailSent = true;
          this.loading = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to send reset link. Please try again.';
          this.loading = false;
        }
      });
    }
  }

  resendEmail(): void {
    if (!this.loading && this.userEmail) {
      this.loading = true;
      this.errorMessage = '';
      this.successMessage = '';

      console.log('Resending password reset email to:', this.userEmail);

      this.authService.forgotPassword(this.userEmail).subscribe({
        next: (response) => {
          console.log('Resend password reset response:', response);
          this.successMessage = response.message || 'Password reset link resent successfully!';
          this.loading = false;
        },
        error: (error) => {
          console.error('Resend password reset error:', error);
          this.errorMessage = error.message || 'Failed to resend reset link. Please try again.';
          this.loading = false;
        }
      });
    } else if (!this.userEmail) {
      this.errorMessage = 'Email address is missing. Please try again.';
    }
  }

  get email() {
    return this.forgotPasswordForm.get('email');
  }
}

