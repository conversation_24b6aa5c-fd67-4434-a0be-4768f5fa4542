<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SignedContract;
use App\Models\UnsignedContract;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class UserDashboardController extends Controller
{
    /**
     * Get dashboard statistics for the authenticated user
     */
    public function getDashboardStats(Request $request)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            Log::warning('Unauthenticated user trying to access dashboard stats');
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to access dashboard'
            ], 401);
        }

        $userId = Auth::id();
        Log::info('Fetching dashboard stats for user', ['user_id' => $userId]);

        // Get date range filter
        $dateRange = $request->input('date_range', 'last12Months');
        $startDate = $this->getStartDateFromRange($dateRange);

        // Get user information
        $user = User::find($userId);

        // Get contract statistics for this user
        $signedContracts = SignedContract::where('user_id', $userId)->get();
        $unsignedContracts = UnsignedContract::where('user_id', $userId)->get();
        
        $totalContracts = $signedContracts->count() + $unsignedContracts->count();
        $pendingContracts = UnsignedContract::where('user_id', $userId)
            ->where('status', 'pending')
            ->count();

        // Get monthly contract data for this user
        $signedContractsPerMonth = $this->getContractsPerMonth($startDate, SignedContract::class, $userId);
        $unsignedContractsPerMonth = $this->getContractsPerMonth($startDate, UnsignedContract::class, $userId);

        // Get recent contracts for this user
        $recentContracts = $this->getRecentContracts($userId);

        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->firstname . ' ' . $user->lastname,
                'email' => $user->email,
                'avatar_url' => $user->avatar_url,
                'is_admin' => $user->is_admin
            ],
            'total_contracts' => $totalContracts,
            'signed_contracts' => $signedContracts->count(),
            'unsigned_contracts' => $unsignedContracts->count(),
            'pending_contracts' => $pendingContracts,
            'signed_contracts_per_month' => $signedContractsPerMonth,
            'unsigned_contracts_per_month' => $unsignedContractsPerMonth,
            'recent_contracts' => $recentContracts
        ]);
    }

    /**
     * Get start date from range string
     */
    private function getStartDateFromRange($range)
    {
        $now = now();
        
        switch ($range) {
            case 'last30Days':
                return $now->subDays(30);
            case 'last90Days':
                return $now->subDays(90);
            case 'last6Months':
                return $now->subMonths(6);
            case 'lastYear':
                return $now->subYear();
            case 'last12Months':
            default:
                return $now->subMonths(12);
        }
    }

    /**
     * Get contracts per month for a specific user
     */
    private function getContractsPerMonth($startDate, $model, $userId)
    {
        $endDate = now();
        
        return $model::where('user_id', $userId)
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate)
            ->selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->groupBy('month')
            ->orderBy('month')
            ->get();
    }

    /**
     * Get recent contracts for a specific user
     */
    private function getRecentContracts($userId)
    {
        // Get recent signed contracts
        $signedContracts = SignedContract::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($contract) {
                return [
                    'id' => $contract->id,
                    'name' => $contract->nom_contrat,
                    'type' => 'signed',
                    'created_at' => $contract->created_at,
                    'email_signataire' => $contract->email_signataire
                ];
            });

        // Get recent unsigned contracts
        $unsignedContracts = UnsignedContract::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($contract) {
                return [
                    'id' => $contract->id,
                    'name' => $contract->nom_contrat,
                    'type' => 'unsigned',
                    'status' => $contract->status,
                    'created_at' => $contract->created_at
                ];
            });

        // Combine and sort by created_at
        return $signedContracts->concat($unsignedContracts)
            ->sortByDesc('created_at')
            ->take(10)
            ->values()
            ->all();
    }
}
