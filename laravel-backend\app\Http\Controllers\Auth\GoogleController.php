<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\OAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class GoogleController extends Controller
{
    protected $oauthService;

    public function __construct(OAuthService $oauthService)
    {
        $this->oauthService = $oauthService;
    }

    /**
     * Redirect user to Google OAuth screen.
     */
    public function redirectToGoogle(Request $request)
    {
        try {
            Log::info('Initiating Google OAuth redirect', [
                'origin' => $request->header('Origin'),
                'is_ajax' => $request->ajax(),
                'wants_json' => $request->wantsJson(),
                'accept' => $request->header('Accept')
            ]);

            $authUrl = $this->oauthService->getGoogleAuthUrl();

            $url = json_decode($authUrl->getContent(), true)['url'] ?? null;

            if (!$url) {
                throw new \Exception('Failed to generate Google OAuth URL.');
            }

            // Add state parameter to identify Google callbacks
            $url .= '&state=google';

            Log::info('Redirecting to Google OAuth', ['url' => $url]);

            // Always redirect directly to Google
            return redirect()->away($url);

        } catch (\Exception $e) {
            Log::error('Google OAuth redirect error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Failed to initiate Google authentication.', 500, $e);
        }
    }

    /**
     * Handle Google callback and authenticate user.
     */
    public function handleGoogleCallback(Request $request)
    {
        try {
            Log::info('Handling Google callback', [
                'full_url' => $request->fullUrl(),
                'query' => $request->all()
            ]);

            $code = $request->input('code');

            if (!$code) {
                throw new \Exception('Missing authorization code.');
            }

            Log::info('Received authorization code', ['length' => strlen($code)]);

            $result = $this->oauthService->handleGoogleCallback($code);

            $googleUser = $result['user'] ?? null;
            $tokens = $result['tokens'] ?? null;

            if (!$googleUser || !isset($googleUser['email'])) {
                throw new \Exception('Failed to retrieve valid user info from Google.');
            }

            $firstName = $googleUser['given_name'] ?? explode(' ', $googleUser['name'])[0] ?? 'User';
            $lastName = $googleUser['family_name'] ?? (explode(' ', $googleUser['name'])[1] ?? '');

            try {
                // First, check if the user exists
                $user = User::where('email', $googleUser['email'])->first();

                if ($user) {
                    // Update existing user
                    $user->firstname = $firstName;
                    $user->lastname = $lastName;
                    $user->avatar = $googleUser['picture'] ?? $user->avatar;

                    // Only update google_id if the column exists
                    if (Schema::hasColumn('users', 'google_id')) {
                        $user->google_id = $googleUser['sub'];
                    }

                    $user->save();
                } else {
                    // Create new user with minimal fields
                    $userData = [
                        'firstname' => $firstName,
                        'lastname' => $lastName,
                        'email' => $googleUser['email'],
                        'password' => Hash::make(Str::random(24)),
                        'avatar' => $googleUser['picture'] ?? null
                    ];

                    // Only add google_id if the column exists
                    if (Schema::hasColumn('users', 'google_id')) {
                        $userData['google_id'] = $googleUser['sub'];
                    }

                    $user = User::create($userData);
                }
            } catch (\Exception $e) {
                Log::error('Error creating/updating user', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // Fallback: create a basic user without the problematic fields
                $user = User::updateOrCreate(
                    ['email' => $googleUser['email']],
                    [
                        'firstname' => $firstName,
                        'lastname' => $lastName,
                        'password' => Hash::make(Str::random(24))
                    ]
                );
            }

            Log::info('User synced with database', ['user_id' => $user->id]);

            $token = $user->createToken('google-auth')->plainTextToken;

            // Prepare user data for frontend
            $userData = [
                'id' => $user->id,
                'name' => $user->firstname . ' ' . $user->lastname,
                'firstname' => $user->firstname,
                'lastname' => $user->lastname,
                'email' => $user->email,
                'avatar' => $user->avatar,
                'avatar_url' => $user->avatar_url
            ];

            $encodedUser = rtrim(strtr(base64_encode(json_encode($userData)), '+/', '-_'), '=');
            $frontendUrl = config('app.frontend_url', 'http://localhost:4200');
            $redirectUrl = "{$frontendUrl}/auth/callback?token={$token}&user={$encodedUser}";

            Log::info('Redirecting to frontend', ['url' => $redirectUrl]);

            // Always redirect to the frontend, regardless of whether it's an API request or not
            return redirect($redirectUrl);

        } catch (\Exception $e) {
            Log::error('Google OAuth failed', ['message' => $e->getMessage()]);

            $frontendUrl = config('app.frontend_url', 'http://localhost:4200');
            $errorUrl = "{$frontendUrl}/auth/callback?error=" . urlencode($e->getMessage());

            // Always redirect to the frontend with the error
            return redirect($errorUrl);
        }
    }

    /**
     * Return a JSON error response with proper CORS headers.
     */
    private function errorResponse(string $message, int $status = 400, ?\Exception $e = null)
    {
        Log::error('OAuth error', [
            'message' => $message,
            'exception' => $e?->getMessage()
        ]);

        return response()->json([
            'error' => 'OAuth Error',
            'message' => $message,
            'details' => config('app.debug') && $e ? $e->getMessage() : null
        ], $status)->header('Access-Control-Allow-Origin', request()->header('Origin') ?: '*')
                  ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                  ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept')
                  ->header('Access-Control-Allow-Credentials', 'true');
    }
}
