import { HttpHeaders } from '@angular/common/http';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule]
})
export class SettingsComponent implements OnInit, OnDestroy {
  securityForm: FormGroup;
  loading = false;
  successMessage = '';
  errorMessage: string | null = null;
  showCurrentPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;
  private authStateSubscription: Subscription | null = null;
  passwordStrength = 0;
  currentUser: any = null;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService
  ) {
    this.securityForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]]
    }, { validator: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    console.log('Settings component initialized');

    // Subscribe to auth state changes
    this.authStateSubscription = this.authService.authState$.subscribe(isLoggedIn => {
      console.log('Auth state changed:', isLoggedIn);
    });
  }

  ngOnDestroy(): void {
    if (this.authStateSubscription) {
      this.authStateSubscription.unsubscribe();
    }
  }

  clearMessages(): void {
    this.successMessage = '';
    this.errorMessage = null;
  }

  private passwordMatchValidator(g: FormGroup) {
    return g.get('newPassword')?.value === g.get('confirmPassword')?.value
      ? null
      : { mismatch: true };
  }

  togglePasswordVisibility(field: 'current' | 'new' | 'confirm'): void {
    switch (field) {
      case 'current':
        this.showCurrentPassword = !this.showCurrentPassword;
        break;
      case 'new':
        this.showNewPassword = !this.showNewPassword;
        break;
      case 'confirm':
        this.showConfirmPassword = !this.showConfirmPassword;
        break;
    }
  }

  checkPasswordStrength(event: Event): void {
    const password = (event.target as HTMLInputElement).value;
    let strength = 0;

    // Check for length
    if (password.length >= 8) strength += 1;
    if (password.length >= 12) strength += 1;

    // Check for character types
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    this.passwordStrength = strength;
  }

  getPasswordStrengthClass(): string {
    switch (this.passwordStrength) {
      case 0:
      case 1:
        return 'weak';
      case 2:
      case 3:
        return 'medium';
      case 4:
      case 5:
        return 'strong';
      default:
        return 'weak';
    }
  }

  getPasswordStrength(): string {
    switch (this.passwordStrength) {
      case 0:
      case 1:
        return 'weak';
      case 2:
      case 3:
        return 'medium';
      case 4:
      case 5:
        return 'strong';
      default:
        return 'weak';
    }
  }

  getPasswordStrengthIcon(): string {
    switch (this.passwordStrength) {
      case 0:
      case 1:
        return 'sentiment_very_dissatisfied';
      case 2:
      case 3:
        return 'sentiment_satisfied';
      case 4:
      case 5:
        return 'sentiment_very_satisfied';
      default:
        return 'sentiment_very_dissatisfied';
    }
  }

  hasMinLength(): boolean {
    const password = this.securityForm.get('newPassword')?.value || '';
    return password.length >= 8;
  }

  hasUpperCase(): boolean {
    const password = this.securityForm.get('newPassword')?.value || '';
    return /[A-Z]/.test(password);
  }

  hasLowerCase(): boolean {
    const password = this.securityForm.get('newPassword')?.value || '';
    return /[a-z]/.test(password);
  }

  hasNumbers(): boolean {
    const password = this.securityForm.get('newPassword')?.value || '';
    return /[0-9]/.test(password);
  }

  hasSpecialChars(): boolean {
    const password = this.securityForm.get('newPassword')?.value || '';
    return /[^A-Za-z0-9]/.test(password);
  }

  async updatePassword(): Promise<void> {
    if (this.securityForm.valid) {
      this.loading = true;
      this.clearMessages();

      const currentPassword = this.securityForm.get('currentPassword')?.value;
      const newPassword = this.securityForm.get('newPassword')?.value;

      console.log('Attempting to update password...');

      this.authService.updatePassword(currentPassword, newPassword).subscribe({
        next: (response) => {
          console.log('Password update successful:', response);
          this.successMessage = 'Password updated successfully';
          this.securityForm.reset();
        },
        error: (error) => {
          console.error('Error updating password:', error);

          // Extract the error message directly from the Error object
          if (error instanceof Error) {
            this.errorMessage = error.message;
          } else if (typeof error === 'string') {
            this.errorMessage = error;
          } else {
            this.errorMessage = 'Failed to update password. Please try again later.';
          }

          // Focus on the current password field if it's incorrect
          if (this.errorMessage.includes('Current password is incorrect')) {
            setTimeout(() => {
              const currentPasswordInput = document.getElementById('currentPassword');
              if (currentPasswordInput) {
                currentPasswordInput.focus();
              }
            }, 100);
          }
        },
        complete: () => {
          this.loading = false;
        }
      });
    }
  }


}

