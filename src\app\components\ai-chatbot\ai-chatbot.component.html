<div class="ai-chatbot-container">
  <!-- Header -->
  <div class="chat-header">
    <div class="header-content">
      <div class="header-info">
        <i class="material-icons">smart_toy</i>
        <div class="header-text">
          <h3>AI Contract Assistant</h3>
          <p class="status">{{ isTyping ? 'AI is typing...' : 'Online' }}</p>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn-icon" (click)="clearChat()" title="Clear chat">
          <i class="material-icons">refresh</i>
        </button>
      </div>
    </div>
  </div>

  <!-- Contract Selection -->
  <div class="contract-selection" *ngIf="!isLoading">
    <div *ngIf="availableContracts.length > 0">
      <label for="contractSelect">Select Contract for Analysis:</label>
      <select
        id="contractSelect"
        [(ngModel)]="selectedContractId"
        (change)="selectContract(selectedContractId!)"
        class="contract-select">
        <option value="">Choose a contract...</option>
        <option *ngFor="let contract of availableContracts" [value]="contract.id">
          {{ contract.displayName }}
        </option>
      </select>
      <div class="selected-contract" *ngIf="selectedContractId">
        <i class="material-icons">description</i>
        <span>{{ getSelectedContractName() }}</span>
      </div>
    </div>

    <div *ngIf="availableContracts.length === 0" class="no-contracts-message">
      <i class="material-icons">info</i>
      <p>No contracts available for analysis. Upload some contracts first to get started with AI analysis.</p>
      <a routerLink="/file-upload" class="upload-link">
        <i class="material-icons">cloud_upload</i>
        Upload Documents
      </a>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="quick-actions" *ngIf="selectedContractId">
    <h4>Quick Analysis</h4>
    <div class="action-buttons">
      <button
        *ngFor="let action of quickActions"
        class="action-btn"
        (click)="handleQuickAction(action.action)"
        [disabled]="isTyping">
        <i class="material-icons">{{ action.icon }}</i>
        <span>{{ action.label }}</span>
      </button>
    </div>
  </div>

  <!-- Messages Container -->
  <div class="messages-container" #messagesContainer>
    <div class="messages-list">
      <div
        *ngFor="let message of messages; trackBy: trackByMessageId"
        class="message"
        [ngClass]="'message-' + message.role">

        <div class="message-avatar">
          <i class="material-icons" *ngIf="message.role === 'assistant'">smart_toy</i>
          <i class="material-icons" *ngIf="message.role === 'user'">person</i>
        </div>

        <div class="message-content">
          <div class="message-header">
            <span class="message-sender">
              {{ message.role === 'assistant' ? 'AI Assistant' : 'You' }}
            </span>
            <span class="message-time">{{ getMessageTime(message.timestamp) }}</span>
          </div>

          <div class="message-text" [innerHTML]="formatMessageContent(message.content)"></div>

          <div class="message-meta" *ngIf="message.contractId">
            <i class="material-icons">description</i>
            <span>Contract #{{ message.contractId }}</span>
            <span *ngIf="message.analysisType" class="analysis-type">
              • {{ message.analysisType | titlecase }}
            </span>
          </div>
        </div>
      </div>

      <!-- Typing Indicator -->
      <div class="message message-assistant" *ngIf="isTyping">
        <div class="message-avatar">
          <i class="material-icons">smart_toy</i>
        </div>
        <div class="message-content">
          <div class="typing-indicator">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span class="typing-text">AI is analyzing...</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Input Area -->
  <div class="input-area">
    <div class="input-container">
      <textarea
        #messageInput
        [(ngModel)]="currentMessage"
        (keydown)="onKeyPress($event)"
        placeholder="Ask me about contract analysis, risks, compliance, or improvements..."
        class="message-input"
        rows="1"
        [disabled]="isTyping"></textarea>

      <button
        class="send-button"
        (click)="sendMessage()"
        [disabled]="!currentMessage.trim() || isTyping">
        <i class="material-icons">send</i>
      </button>
    </div>

    <div class="input-hints" *ngIf="!selectedContractId">
      <p>💡 Select a contract above to get specific analysis and recommendations</p>
    </div>

    <div class="input-hints" *ngIf="selectedContractId">
      <p>💡 Try asking: "What are the main risks?" or "Check compliance with GDPR"</p>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="loading-spinner">
      <i class="material-icons spinning">refresh</i>
      <p>Loading contracts...</p>
    </div>
  </div>
</div>
