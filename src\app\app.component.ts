import { Component, OnInit, On<PERSON><PERSON>roy, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule, isPlatformBrowser, DOCUMENT } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { NavbarComponent } from './components/navbar/navbar.component';
import { FooterComponent } from "./components/footer/footer.component";
import { SidebarComponent } from './components/sidebar/sidebar.component';

import { ThemeService } from './services/theme.service';

import { Subscription } from 'rxjs';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    NavbarComponent,
    FooterComponent,
    SidebarComponent
  ],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'my-angular-app';
  sidebarExpanded = true;
  isDarkMode = false;
  private themeSubscription: Subscription | null = null;

  constructor(
    private themeService: ThemeService,
    @Inject(PLATFORM_ID) private platformId: Object,
    @Inject(DOCUMENT) private document: Document
  ) {}

  // In app.component.ts
  ngOnInit() {
    // Subscribe to theme changes
    this.themeSubscription = this.themeService.isDarkMode$.subscribe(
      isDark => {
        this.isDarkMode = isDark;
        // Only manipulate DOM in browser environment
        if (isPlatformBrowser(this.platformId)) {
          this.document.body.classList.toggle('dark-mode', isDark);
        }
      }
    );
  }

  ngOnDestroy() {
    if (this.themeSubscription) {
      this.themeSubscription.unsubscribe();
    }
  }

  onSidebarToggle(expanded: boolean) {
    this.sidebarExpanded = expanded;
  }
}
