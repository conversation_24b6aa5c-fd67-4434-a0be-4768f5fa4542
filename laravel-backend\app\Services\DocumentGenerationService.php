<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PhpOffice\PhpWord\TemplateProcessor;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

class DocumentGenerationService
{
    /**
     * Generate a new document from a template with placeholders replaced by values
     *
     * @param string $originalFilePath Path to the original template file
     * @param array $replacements Key-value pairs where key is the placeholder name and value is the replacement
     * @param string|null $documentName Optional name for the document
     * @return string|null Path to the generated file or null on failure
     */
    public function generateDocument(string $originalFilePath, array $replacements, ?string $documentName = null): ?string
    {
        Log::info('Generating document from template: ' . $originalFilePath);
        Log::debug('Replacements count: ' . count($replacements));
        Log::debug('Document name: ' . ($documentName ?? 'Not provided'));

        // Validate the original file path
        if (empty($originalFilePath)) {
            Log::error('Empty file path provided for document generation');
            return null;
        }

        // Validate the file extension
        $extension = strtolower(pathinfo($originalFilePath, PATHINFO_EXTENSION));
        if (empty($extension)) {
            Log::error('File has no extension: ' . $originalFilePath);
            return null;
        }

        // Validate replacements
        if (!is_array($replacements)) {
            Log::error('Replacements is not an array');
            return null;
        }

        // Log the first few replacements for debugging
        $replacementsPreview = array_slice($replacements, 0, 5, true);
        Log::debug('Replacements preview: ' . json_encode($replacementsPreview));

        try {
            switch ($extension) {
                case 'docx':
                case 'doc':
                    return $this->generateDocxDocument($originalFilePath, $replacements, $documentName);
                case 'pdf':
                    return $this->generatePdfDocument($originalFilePath, $replacements, $documentName);
                default:
                    Log::error('Unsupported file type for document generation: ' . $extension);
                    return null;
            }
        } catch (\Exception $e) {
            Log::error('Error generating document: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Generate a new DOCX document from a template
     *
     * @param string $originalFilePath Path to the original template file
     * @param array $replacements Key-value pairs where key is the placeholder name and value is the replacement
     * @param string|null $documentName Optional name for the document
     * @return string|null Path to the generated file or null on failure
     */
    private function generateDocxDocument(string $originalFilePath, array $replacements, ?string $documentName = null): ?string
    {
        Log::info('Generating DOCX document using PhpWord TemplateProcessor');

        try {
            // Ensure the generated directory exists
            if (!Storage::disk('public')->exists('generated')) {
                Storage::disk('public')->makeDirectory('generated');
            }

            // Create a unique filename for the generated document
            if ($documentName) {
                // Use the provided document name, sanitize it and add a timestamp
                $sanitizedName = Str::slug($documentName);
                $outputFilename = $sanitizedName . '_' . time() . '.docx';
            } else {
                // Use a generic name with timestamp and random string
                $outputFilename = 'generated_' . time() . '_' . Str::random(8) . '.docx';
            }
            $outputPath = 'generated/' . $outputFilename;

            // Check if the original file exists
            if (!Storage::disk('public')->exists($originalFilePath)) {
                Log::error('Original file not found: ' . $originalFilePath);

                // Try to create a valid template as a fallback
                try {
                    // Create a new PhpWord instance
                    $phpWord = new \PhpOffice\PhpWord\PhpWord();

                    // Add a section
                    $section = $phpWord->addSection();

                    // Add text with placeholders
                    $section->addText('This is a sample document for testing.');
                    $section->addText('');
                    $section->addText('It contains placeholders like ${name} and ${date}.');

                    // Save the document
                    $tempFile = tempnam(sys_get_temp_dir(), 'docx_');
                    $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
                    $objWriter->save($tempFile);

                    // Then move to storage
                    Storage::disk('public')->put($originalFilePath, file_get_contents($tempFile));

                    // Clean up
                    unlink($tempFile);

                    Log::info('Created fallback template file at: ' . $originalFilePath);
                } catch (\Exception $e) {
                    Log::error('Failed to create fallback template: ' . $e->getMessage());
                    return null;
                }
            }

            // Check if the file is readable
            $originalFilePath = Storage::disk('public')->path($originalFilePath);
            if (!is_readable($originalFilePath)) {
                Log::error('Original file is not readable: ' . $originalFilePath);
                return null;
            }

            // Check if the file size is valid
            $fileSize = filesize($originalFilePath);
            if ($fileSize === false || $fileSize === 0) {
                Log::error('Original file is empty or invalid: ' . $originalFilePath);
                return null;
            }

            Log::info('Original file is valid and readable: ' . $originalFilePath . ' (Size: ' . $fileSize . ' bytes)');

            // Create a temporary copy of the original file to work with
            $tempFile = tempnam(sys_get_temp_dir(), 'docx_');
            if (!copy($originalFilePath, $tempFile)) {
                Log::error('Failed to copy original file to temporary location');
                return null;
            }

            // Save the original document for debugging
            $debugDir = storage_path('app/public/debug');
            if (!is_dir($debugDir)) {
                mkdir($debugDir, 0755, true);
            }

            // Use PhpWord's TemplateProcessor to replace placeholders
            try {
                $templateProcessor = new TemplateProcessor($tempFile);

                // Get all variables in the template
                $variables = $templateProcessor->getVariables();
                Log::info('Variables found in template: ' . implode(', ', $variables));

                // Replace each placeholder with its value
                foreach ($replacements as $placeholder => $value) {
                    // Try to set the value directly
                    try {
                        $templateProcessor->setValue($placeholder, $value);
                        Log::info("Replaced placeholder: {$placeholder} with value: {$value}");
                    } catch (\Exception $e) {
                        Log::warning("Error setting value for {$placeholder}: " . $e->getMessage());
                    }

                    // Also try with variations of the placeholder name
                    $variations = [
                        strtolower($placeholder),
                        strtoupper($placeholder),
                        ucfirst(strtolower($placeholder))
                    ];

                    foreach ($variations as $variation) {
                        if ($variation !== $placeholder) {
                            try {
                                $templateProcessor->setValue($variation, $value);
                                Log::info("Replaced variation placeholder: {$variation} with value: {$value}");
                            } catch (\Exception $e) {
                                // Ignore errors for variations
                            }
                        }
                    }
                }

                // Save the generated document
                $outputTempFile = tempnam(sys_get_temp_dir(), 'docx_output_');
                $templateProcessor->saveAs($outputTempFile);

                // Move the file to the public storage
                Storage::disk('public')->put($outputPath, file_get_contents($outputTempFile));

                // Clean up temporary files
                unlink($tempFile);
                unlink($outputTempFile);

                Log::info('DOCX document generated successfully: ' . $outputPath);
                return $outputPath;
            } catch (\Exception $e) {
                Log::error('Error using TemplateProcessor: ' . $e->getMessage());
                Log::error('Stack trace: ' . $e->getTraceAsString());

                // Fall back to the manual XML approach
                return $this->generateDocxDocumentFallback($originalFilePath, $replacements, $documentName);
            }
        } catch (\Exception $e) {
            Log::error('Error generating DOCX document: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Fallback method for generating DOCX document by manually manipulating XML
     *
     * @param string $originalFilePath Path to the original template file
     * @param array $replacements Key-value pairs where key is the placeholder name and value is the replacement
     * @param string|null $documentName Optional name for the document
     * @return string|null Path to the generated file or null on failure
     */
    private function generateDocxDocumentFallback(string $originalFilePath, array $replacements, ?string $documentName = null): ?string
    {
        Log::info('Using fallback method for DOCX generation');

        try {
            // Ensure the generated directory exists
            if (!Storage::disk('public')->exists('generated')) {
                Storage::disk('public')->makeDirectory('generated');
            }

            // Create a unique filename for the generated document
            if ($documentName) {
                // Use the provided document name, sanitize it and add a timestamp
                $sanitizedName = Str::slug($documentName);
                $outputFilename = $sanitizedName . '_fallback_' . time() . '.docx';
            } else {
                // Use a generic name with timestamp and random string
                $outputFilename = 'generated_' . time() . '_' . Str::random(8) . '.docx';
            }
            $outputPath = 'generated/' . $outputFilename;

            // Get the original file content
            $originalContent = Storage::disk('public')->get($originalFilePath);

            // Create a temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'docx_');
            file_put_contents($tempFile, $originalContent);

            // Create a ZIP archive object
            $zip = new \ZipArchive();
            if ($zip->open($tempFile) === true) {
                // Get the content of document.xml
                $documentXml = $zip->getFromName('word/document.xml');

                if ($documentXml) {
                    Log::info('Successfully extracted document.xml from DOCX file');

                    // Save the original document.xml for debugging
                    $debugDir = storage_path('app/public/debug');
                    if (!is_dir($debugDir)) {
                        mkdir($debugDir, 0755, true);
                    }

                    $originalXmlPath = $debugDir . '/original_document_' . time() . '.xml';
                    file_put_contents($originalXmlPath, $documentXml);

                    // Replace each placeholder with its value
                    foreach ($replacements as $placeholder => $value) {
                        // Escape special XML characters in the value
                        $safeValue = htmlspecialchars($value, ENT_XML1 | ENT_QUOTES, 'UTF-8');

                        // Replace the placeholder in the XML content
                        $documentXml = str_replace('${'.$placeholder.'}', $safeValue, $documentXml);

                        // Also try with the value as a suffix (e.g., ${placeholder}value)
                        $pattern = '/\$\{' . preg_quote($placeholder, '/') . '\}[a-zA-Z0-9]+/';
                        $documentXml = preg_replace($pattern, $safeValue, $documentXml);

                        Log::info("Replaced placeholder: \${$placeholder} with value: {$safeValue}");
                    }

                    // Save the modified document.xml for debugging
                    $modifiedXmlPath = $debugDir . '/modified_document_' . time() . '.xml';
                    file_put_contents($modifiedXmlPath, $documentXml);

                    // Update the document.xml in the ZIP archive
                    $zip->addFromString('word/document.xml', $documentXml);
                    $zip->close();

                    // Read the modified file
                    $modifiedContent = file_get_contents($tempFile);

                    // Save the modified file to the output path
                    Storage::disk('public')->put($outputPath, $modifiedContent);

                    // Clean up the temporary file
                    unlink($tempFile);

                    Log::info('DOCX document with replaced placeholders saved to: ' . $outputPath);
                    return $outputPath;
                } else {
                    Log::error('Failed to extract document.xml from DOCX file');
                    $zip->close();
                    unlink($tempFile);

                    // Fallback to just copying the file
                    Storage::disk('public')->put($outputPath, $originalContent);
                    return $outputPath;
                }
            } else {
                Log::error('Failed to open DOCX file as ZIP archive');
                unlink($tempFile);

                // Fallback to just copying the file
                Storage::disk('public')->put($outputPath, $originalContent);
                return $outputPath;
            }
        } catch (\Exception $e) {
            Log::error('Error in fallback DOCX generation: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Generate a new PDF document
     *
     * @param string $originalFilePath Path to the original template file
     * @param array $replacements Key-value pairs where key is the placeholder name and value is the replacement
     * @param string|null $documentName Optional name for the document
     * @return string|null Path to the generated file or null on failure
     */
    private function generatePdfDocument(string $originalFilePath, array $replacements, ?string $documentName = null): ?string
    {
        Log::info('Generating PDF document');

        try {
            // Ensure the generated directory exists
            if (!Storage::disk('public')->exists('generated')) {
                Storage::disk('public')->makeDirectory('generated');
            }

            // Create a unique filename for the generated document
            if ($documentName) {
                // Use the provided document name, sanitize it and add a timestamp
                $sanitizedName = Str::slug($documentName);
                $outputFilename = $sanitizedName . '_' . time() . '.pdf';
            } else {
                // Use a generic name with timestamp and random string
                $outputFilename = 'generated_' . time() . '_' . Str::random(8) . '.pdf';
            }
            $outputPath = 'generated/' . $outputFilename;

            // Check if the original file exists
            if (!Storage::disk('public')->exists($originalFilePath)) {
                Log::error('Original file not found: ' . $originalFilePath);
                return null;
            }

            // Get the original file content
            $originalContent = Storage::disk('public')->get($originalFilePath);

            // For PDF files, we can't easily modify the content directly
            // We'll create a simple text file with the replacements for now

            // Create a text file with the replacements
            $textContent = "Original PDF: {$originalFilePath}\n\n";
            $textContent .= "Replacements applied:\n";

            foreach ($replacements as $placeholder => $value) {
                $textContent .= "- {$placeholder}: {$value}\n";
            }

            $textContent .= "\nNote: PDF content modification requires additional libraries. This is a placeholder.";

            // Create a text file with the same name but .txt extension
            $textFilename = pathinfo($outputFilename, PATHINFO_FILENAME) . '.txt';
            $textPath = 'generated/' . $textFilename;

            // Save both the original PDF and the text file with replacements
            Storage::disk('public')->put($outputPath, $originalContent);
            Storage::disk('public')->put($textPath, $textContent);

            Log::info('PDF document copied to output path: ' . $outputPath);
            Log::info('Text file with replacements saved to: ' . $textPath);

            // For now, we'll return the PDF path, but in a real implementation
            // you would use a PDF library to replace placeholders in the PDF
            return $outputPath;
        } catch (\Exception $e) {
            Log::error('Error generating PDF document: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }
}
