<div class="profile-page">
  <!-- Loading state -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p class="loading-text">Loading your profile...</p>
    </div>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="alert alert-danger">
    <div class="alert-icon">
      <i class="material-icons">error_outline</i>
    </div>
    <div class="alert-content">
      <h4>Something went wrong</h4>
      <p>{{ error }}</p>
      <div *ngIf="error.includes('not logged in')" class="error-actions">
        <button (click)="manuallySetAuthData()" class="btn-secondary">
          <i class="material-icons">refresh</i> Restore Session
        </button>
        <button (click)="redirectToSignIn()" class="btn-primary">
          <i class="material-icons">login</i> Sign In
        </button>
      </div>
    </div>
  </div>

  <!-- Success message -->
  <div *ngIf="message" class="alert alert-success">
    <div class="alert-icon">
      <i class="material-icons">check_circle</i>
    </div>
    <div class="alert-content">
      <p>{{ message }}</p>
    </div>
  </div>

  <!-- Profile content -->
  <div *ngIf="currentUser && !isLoading" class="profile-container">
    <!-- Profile Header Card -->
    <div class="profile-header-card">
      <div class="profile-cover">
        <div class="cover-gradient"></div>
      </div>

      <div class="profile-info">
        <div class="avatar-section">
          <div class="avatar-container">
            <div class="avatar-wrapper">
              <img
                [src]="getAvatarUrl()"
                [alt]="getDisplayName()"
                class="profile-avatar"
                (error)="onImageError($event)"
                (load)="onImageLoad($event)"
              >
              <div class="avatar-upload">
                <label for="avatar-input" class="btn-upload">
                  <i class="material-icons">photo_camera</i>
                </label>
                <input
                  type="file"
                  id="avatar-input"
                  (change)="onFileSelected($event)"
                  accept="image/*"
                  style="display: none;"
                >
              </div>
            </div>
          </div>

          <div class="user-details">
            <h1 class="user-name">{{ getDisplayName() }}</h1>
            <p class="user-email">{{ currentUser.email }}</p>

          </div>
        </div>

        <div class="profile-info-cards">
          <div class="info-card">
            <div class="info-icon">
              <i class="material-icons">verified</i>
            </div>
            <div class="info-content">
              <div class="info-label">Account</div>
              <div class="info-value">Verified</div>
            </div>
          </div>
          <div class="info-card" *ngIf="currentUser.phone">
            <div class="info-icon">
              <i class="material-icons">phone</i>
            </div>
            <div class="info-content">
              <div class="info-label">Phone</div>
              <div class="info-value">{{ currentUser.phone }}</div>
            </div>
          </div>
          <div class="info-card">
            <div class="info-icon">
              <i class="material-icons">{{ currentUser.is_admin ? 'admin_panel_settings' : 'person' }}</i>
            </div>
            <div class="info-content">
              <div class="info-label">Role</div>
              <div class="info-value">{{ currentUser.is_admin ? 'Administrator' : 'User' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="content-grid">
      <!-- Profile Form Card -->
      <div class="form-card">
        <div class="card-header">
          <div class="header-content">
            <h2>
              <i class="material-icons">edit</i>
              Profile Information
            </h2>
            <p>Update your personal information and account settings</p>
          </div>
        </div>

        <div class="card-body">
          <form [formGroup]="profileForm" (ngSubmit)="onSubmit()" class="profile-form">
            <div class="form-grid">
              <div class="form-group">
                <label for="firstname" class="form-label">
                  <i class="material-icons">person</i>
                  First Name
                </label>
                <input
                  type="text"
                  id="firstname"
                  formControlName="firstname"
                  class="form-input"
                  [class.error]="profileForm.get('firstname')?.invalid && profileForm.get('firstname')?.touched"
                  placeholder="Enter your first name"
                >
                <div class="error-message" *ngIf="profileForm.get('firstname')?.invalid && profileForm.get('firstname')?.touched">
                  <i class="material-icons">error</i>
                  First name is required
                </div>
              </div>

              <div class="form-group">
                <label for="lastname" class="form-label">
                  <i class="material-icons">person</i>
                  Last Name
                </label>
                <input
                  type="text"
                  id="lastname"
                  formControlName="lastname"
                  class="form-input"
                  [class.error]="profileForm.get('lastname')?.invalid && profileForm.get('lastname')?.touched"
                  placeholder="Enter your last name"
                >
                <div class="error-message" *ngIf="profileForm.get('lastname')?.invalid && profileForm.get('lastname')?.touched">
                  <i class="material-icons">error</i>
                  Last name is required
                </div>
              </div>

              <div class="form-group full-width">
                <label for="email" class="form-label">
                  <i class="material-icons">email</i>
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  formControlName="email"
                  class="form-input"
                  [class.error]="profileForm.get('email')?.invalid && profileForm.get('email')?.touched"
                  placeholder="Enter your email address"
                >
                <div class="error-message" *ngIf="profileForm.get('email')?.invalid && profileForm.get('email')?.touched">
                  <i class="material-icons">error</i>
                  Please enter a valid email address
                </div>
              </div>

              <div class="form-group full-width">
                <label for="phone" class="form-label">
                  <i class="material-icons">phone</i>
                  Phone Number
                  <span class="optional">(Optional)</span>
                </label>
                <input
                  type="tel"
                  id="phone"
                  formControlName="phone"
                  class="form-input"
                  placeholder="Enter your phone number"
                >
                <div class="form-hint">
                  <i class="material-icons">info</i>
                  Your phone number will be used for important communications only
                </div>
              </div>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn-primary" [disabled]="!profileForm.valid || isLoading">
                <i class="material-icons">{{ isLoading ? 'hourglass_empty' : 'save' }}</i>
                {{ isLoading ? 'Saving...' : 'Save Changes' }}
              </button>
              <button type="button" class="btn-secondary" (click)="resetForm()">
                <i class="material-icons">refresh</i>
                Reset
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Quick Actions Card -->
      <div class="actions-card">
        <div class="card-header">
          <h3>
            <i class="material-icons">flash_on</i>
            Quick Actions
          </h3>
        </div>
        <div class="card-body">
          <div class="action-list">
            <button class="action-item" (click)="navigateToDocuments()">
              <div class="action-icon">
                <i class="material-icons">description</i>
              </div>
              <div class="action-content">
                <h4>My Documents</h4>
                <p>View and manage your contracts</p>
              </div>
              <i class="material-icons arrow">arrow_forward_ios</i>
            </button>

            <button class="action-item" (click)="navigateToSecurity()">
              <div class="action-icon">
                <i class="material-icons">security</i>
              </div>
              <div class="action-content">
                <h4>Security Settings</h4>
                <p>Manage your account security</p>
              </div>
              <i class="material-icons arrow">arrow_forward_ios</i>
            </button>

            <button *ngIf="currentUser.is_admin" class="action-item" (click)="navigateToNotifications()">
              <div class="action-icon">
                <i class="material-icons">notifications</i>
              </div>
              <div class="action-content">
                <h4>Notifications</h4>
                <p>Configure your preferences</p>
              </div>
              <i class="material-icons arrow">arrow_forward_ios</i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

