/* Terms and Conditions Styles - Theme Adaptive */
:host {
  display: block;
  width: 100%;
  background: var(--background);
  color: var(--text);
  transition: background-color 0.3s ease, color 0.3s ease;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px;
  color: var(--text);
}

/* Section Header */
.section-header {
  margin-bottom: 3rem;
}

.award-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.award-badge i {
  font-size: 1.2rem;
}

.highlight {
  color: var(--primary);
  position: relative;
}

/* Utility Classes */
.mb-4 {
  margin-bottom: 1.5rem;
}

/* Page Header */
h1 {
  font-size: 2.8rem;
  color: var(--text);
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

h1::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(78, 205, 196, 0.3);
  z-index: -1;
  transform: skewX(-15deg);
}

.text-muted {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: 30px;
}

/* Cards */
.card {
  background-color: var(--surface);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-md);
  margin-bottom: 1.5rem;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-left: 3px solid var(--primary);
}

.card-body {
  padding: 30px;
}

/* Section Headers */
.card h2 {
  color: var(--primary);
  font-size: 1.5rem;
  margin-bottom: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.card h2 i {
  margin-right: 12px;
  font-size: 1.5rem;
  color: var(--primary);
}

.card h2::before {
  content: '';
  width: 6px;
  height: 24px;
  background: linear-gradient(to bottom, var(--primary), var(--primary-dark));
  margin-right: 15px;
  border-radius: 3px;
  display: none; /* Hide the ::before since we're using icons */
}

/* Content Styling */
.card p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 15px;
}

.card strong {
  color: var(--text);
  font-weight: 600;
}

.card ul {
  margin: 20px 0;
  padding-left: 20px;
}

.card li {
  color: var(--text-secondary);
  margin-bottom: 10px;
  line-height: 1.5;
  position: relative;
  padding-left: 10px;
}

.card li::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 10px;
  width: 8px;
  height: 8px;
  background-color: var(--primary);
  border-radius: 50%;
}

/* Links */
.card a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
  position: relative;
}

.card a:hover {
  color: var(--primary-light);
}

.card a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--primary);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.card a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Buttons */
.btn-primary {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  text-decoration: none;
}

.btn-primary i {
  margin-right: 8px;
  font-size: 1.2rem;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 4px 8px rgba(78, 205, 196, 0.2);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
  padding: 12px 30px;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-outline i {
  margin-right: 8px;
  font-size: 1.2rem;
}

.btn-outline:hover {
  background: rgba(78, 205, 196, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.3);
}

.btn-outline:active {
  transform: translateY(0);
  box-shadow: 0 4px 8px rgba(78, 205, 196, 0.15);
}

/* Animation for page load */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.card {
  animation: fadeIn 0.5s ease-out forwards;
  opacity: 0;
}

.card:nth-child(2) { animation-delay: 0.1s; }
.card:nth-child(3) { animation-delay: 0.2s; }
.card:nth-child(4) { animation-delay: 0.3s; }
.card:nth-child(5) { animation-delay: 0.4s; }
.card:nth-child(6) { animation-delay: 0.5s; }
.card:nth-child(7) { animation-delay: 0.6s; }
.card:nth-child(8) { animation-delay: 0.7s; }
.card:nth-child(9) { animation-delay: 0.8s; }
.card:nth-child(10) { animation-delay: 0.9s; }

/* CTA Buttons */
.cta-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 3rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 20px;
  }

  h1 {
    font-size: 2.2rem;
  }

  .card-body {
    padding: 20px;
  }

  .card h2 {
    font-size: 1.3rem;
  }

  .card p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.8rem;
  }

  .card-body {
    padding: 15px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary,
  .btn-outline {
    width: 100%;
    max-width: 300px;
    padding: 12px 20px;
    justify-content: center;
  }

  .award-badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }
}