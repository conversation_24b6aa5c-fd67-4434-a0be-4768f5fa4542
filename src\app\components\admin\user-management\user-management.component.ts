import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AdminService } from '../../../services/admin.service';

@Component({
  selector: 'app-user-management',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule, ReactiveFormsModule]
})
export class UserManagementComponent implements OnInit {
  users: any[] = [];
  filteredUsers: any[] = [];
  isLoading = false;
  searchTerm = '';
  showUserForm = false;
  isEditMode = false;
  selectedUserId: number | null = null;
  successMessage: string | null = null;
  errorMessage: string | null = null;

  userForm: FormGroup;

  constructor(
    private adminService: AdminService,
    private fb: FormBuilder
  ) {
    this.userForm = this.fb.group({
      firstname: ['', [Validators.required, Validators.minLength(2)]],
      lastname: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.minLength(8)]],
      is_admin: [false]
    });
  }

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers(): void {
    this.isLoading = true;

    this.adminService.getUsers().subscribe({
      next: (data) => {
        this.users = data;
        this.filteredUsers = [...this.users];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.isLoading = false;
      }
    });
  }

  searchUsers(): void {
    if (!this.searchTerm.trim()) {
      this.filteredUsers = [...this.users];
      return;
    }

    const term = this.searchTerm.toLowerCase().trim();
    this.filteredUsers = this.users.filter(user =>
      user.firstname.toLowerCase().includes(term) ||
      user.lastname.toLowerCase().includes(term) ||
      user.email.toLowerCase().includes(term)
    );
  }

  toggleUserForm(): void {
    this.showUserForm = !this.showUserForm;

    if (!this.showUserForm) {
      // Reset form when closing
      this.resetForm();
    }
  }

  resetForm(): void {
    this.userForm.reset({
      firstname: '',
      lastname: '',
      email: '',
      password: '',
      is_admin: false
    });
    this.isEditMode = false;
    this.selectedUserId = null;
  }

  editUser(user: any): void {
    this.isEditMode = true;
    this.selectedUserId = user.id;
    this.showUserForm = true;

    // When editing, password is optional
    this.userForm.get('password')?.setValidators([]);
    this.userForm.get('password')?.updateValueAndValidity();

    this.userForm.patchValue({
      firstname: user.firstname,
      lastname: user.lastname,
      email: user.email,
      is_admin: user.is_admin
    });
  }

  onSubmit(): void {
    if (this.userForm.invalid) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;
    this.successMessage = null;
    const userData = this.userForm.value;

    // If password is empty and we're in edit mode, remove it from the payload
    if (this.isEditMode && !userData.password) {
      delete userData.password;
    }

    if (this.isEditMode && this.selectedUserId) {
      // Update existing user
      this.adminService.updateUser(this.selectedUserId, userData).subscribe({
        next: (response) => {
          // Update user in the list
          const index = this.users.findIndex(u => u.id === this.selectedUserId);
          if (index !== -1) {
            this.users[index] = { ...this.users[index], ...userData };
          }

          this.successMessage = 'User updated successfully';
          setTimeout(() => this.successMessage = null, 3000);

          this.searchUsers(); // Refresh filtered list
          this.resetForm();
          this.showUserForm = false;
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Error updating user';
          console.error('Error updating user:', error);
          this.isLoading = false;
        }
      });
    } else {
      // Create new user
      this.adminService.createUser(userData).subscribe({
        next: (response) => {
          this.users.unshift(response); // Add to beginning of array

          this.successMessage = 'User created successfully';
          setTimeout(() => this.successMessage = null, 3000);

          this.searchUsers(); // Refresh filtered list
          this.resetForm();
          this.showUserForm = false;
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Error creating user';
          console.error('Error creating user:', error);
          this.isLoading = false;
        }
      });
    }
  }

  deleteUser(userId: number): void {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      this.isLoading = true;
      this.errorMessage = null;
      this.successMessage = null;

      this.adminService.deleteUser(userId).subscribe({
        next: () => {
          // Remove user from arrays
          this.users = this.users.filter(user => user.id !== userId);
          this.filteredUsers = this.filteredUsers.filter(user => user.id !== userId);
          this.successMessage = 'User deleted successfully';
          setTimeout(() => this.successMessage = null, 3000);
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Error deleting user';
          console.error('Error deleting user:', error);
          this.isLoading = false;
        }
      });
    }
  }
}
