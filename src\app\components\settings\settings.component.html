<div class="security-container">
  <!-- Background Elements -->
  <div class="bg-element bg-circle-1"></div>
  <div class="bg-element bg-circle-2"></div>
  <div class="bg-element bg-pattern"></div>

  <div class="security-card">
    <!-- Header with Icon -->
    <div class="security-header">
      <div class="header-icon-container">
        <div class="header-icon-bg"></div>
        <div class="header-icon">
          <i class="material-icons">shield</i>
        </div>
      </div>
      <div class="header-text">
        <h1>Security Center</h1>
        <p>Enhance your account protection</p>
      </div>
    </div>

    <!-- Alert Messages -->
    <div *ngIf="successMessage" class="alert alert-success">
      <div class="alert-icon-container">
        <i class="material-icons">check_circle</i>
      </div>
      <div class="alert-content">
        <span class="alert-title">Success!</span>
        <span class="alert-message">{{ successMessage }}</span>
      </div>
      <button class="alert-close" (click)="clearMessages()">
        <i class="material-icons">close</i>
      </button>
    </div>

    <div *ngIf="errorMessage" class="alert alert-error">
      <div class="alert-icon-container">
        <i class="material-icons">error</i>
      </div>
      <div class="alert-content">
        <span class="alert-title">Error</span>
        <span class="alert-message">{{ errorMessage }}</span>
      </div>
      <button class="alert-close" (click)="clearMessages()">
        <i class="material-icons">close</i>
      </button>
    </div>

    <!-- Security Status Card -->
    <div class="status-card">
      <div class="status-icon">
        <i class="material-icons">security</i>
      </div>
      <div class="status-content">
        <h3>Security Status</h3>
        <div class="status-meter">
          <div class="status-bar" style="width: 70%;"></div>
        </div>
        <p>Your account security is good, but could be improved.</p>
      </div>
      <div class="status-action">
        <span class="status-badge">70%</span>
      </div>
    </div>

    <!-- Security Form -->
    <form [formGroup]="securityForm" (ngSubmit)="updatePassword()" class="security-form">
      <div class="form-section">
        <div class="section-header">
          <i class="material-icons section-icon">password</i>
          <h2>Password Management</h2>
        </div>

        <!-- Current Password -->
        <div class="form-group">
          <label for="currentPassword">Current Password</label>
          <div class="input-container">
            <i class="material-icons input-icon">lock</i>
            <input
              id="currentPassword"
              [type]="showCurrentPassword ? 'text' : 'password'"
              formControlName="currentPassword"
              class="form-control"
              placeholder="Enter your current password"
              [class.error]="securityForm.get('currentPassword')?.invalid && securityForm.get('currentPassword')?.touched">
            <button
              type="button"
              class="toggle-button"
              (click)="togglePasswordVisibility('current')">
              <i class="material-icons">{{ showCurrentPassword ? 'visibility_off' : 'visibility' }}</i>
            </button>
          </div>
          <div class="error-message" *ngIf="securityForm.get('currentPassword')?.invalid && securityForm.get('currentPassword')?.touched">
            <i class="material-icons error-icon">error_outline</i>
            Current password is required
          </div>
        </div>

        <!-- New Password -->
        <div class="form-group">
          <label for="newPassword">New Password</label>
          <div class="input-container">
            <i class="material-icons input-icon">vpn_key</i>
            <input
              id="newPassword"
              [type]="showNewPassword ? 'text' : 'password'"
              formControlName="newPassword"
              class="form-control"
              placeholder="Create a new password"
              (input)="checkPasswordStrength($event)"
              [class.error]="securityForm.get('newPassword')?.invalid && securityForm.get('newPassword')?.touched">
            <button
              type="button"
              class="toggle-button"
              (click)="togglePasswordVisibility('new')">
              <i class="material-icons">{{ showNewPassword ? 'visibility_off' : 'visibility' }}</i>
            </button>
          </div>

          <!-- Password Strength Indicator -->
          <div class="password-strength" *ngIf="securityForm.get('newPassword')?.value">
            <div class="strength-meter">
              <div [class]="'strength-bar ' + getPasswordStrengthClass()"></div>
            </div>
            <div class="strength-text">
              <i class="material-icons" [ngClass]="getPasswordStrengthClass()">
                {{ getPasswordStrengthIcon() }}
              </i>
              <span>Password Strength: {{ getPasswordStrength() | titlecase }}</span>
            </div>
          </div>

          <div class="password-tips" *ngIf="securityForm.get('newPassword')?.value">
            <div class="tip-title">Password Requirements:</div>
            <ul class="tip-list">
              <li [class.active]="hasMinLength()">
                <i class="material-icons tip-icon">{{ hasMinLength() ? 'check_circle' : 'radio_button_unchecked' }}</i>
                <span>At least 8 characters</span>
              </li>
              <li [class.active]="hasUpperCase()">
                <i class="material-icons tip-icon">{{ hasUpperCase() ? 'check_circle' : 'radio_button_unchecked' }}</i>
                <span>Include uppercase letters</span>
              </li>
              <li [class.active]="hasLowerCase()">
                <i class="material-icons tip-icon">{{ hasLowerCase() ? 'check_circle' : 'radio_button_unchecked' }}</i>
                <span>Include lowercase letters</span>
              </li>
              <li [class.active]="hasNumbers()">
                <i class="material-icons tip-icon">{{ hasNumbers() ? 'check_circle' : 'radio_button_unchecked' }}</i>
                <span>Include numbers</span>
              </li>
              <li [class.active]="hasSpecialChars()">
                <i class="material-icons tip-icon">{{ hasSpecialChars() ? 'check_circle' : 'radio_button_unchecked' }}</i>
                <span>Include special characters</span>
              </li>
            </ul>
          </div>

          <div class="error-message" *ngIf="securityForm.get('newPassword')?.invalid && securityForm.get('newPassword')?.touched">
            <i class="material-icons error-icon">error_outline</i>
            Password must be at least 8 characters long
          </div>
        </div>

        <!-- Confirm Password -->
        <div class="form-group">
          <label for="confirmPassword">Confirm New Password</label>
          <div class="input-container">
            <i class="material-icons input-icon">done_all</i>
            <input
              id="confirmPassword"
              [type]="showConfirmPassword ? 'text' : 'password'"
              formControlName="confirmPassword"
              class="form-control"
              placeholder="Confirm your new password"
              [class.error]="securityForm.get('confirmPassword')?.invalid && securityForm.get('confirmPassword')?.touched">
            <button
              type="button"
              class="toggle-button"
              (click)="togglePasswordVisibility('confirm')">
              <i class="material-icons">{{ showConfirmPassword ? 'visibility_off' : 'visibility' }}</i>
            </button>
          </div>
          <div class="error-message" *ngIf="securityForm.hasError('mismatch')">
            <i class="material-icons error-icon">error_outline</i>
            Passwords do not match
          </div>
        </div>
      </div>

      <!-- Security Recommendations -->
      <div class="form-section">
        <div class="section-header">
          <i class="material-icons section-icon">verified_user</i>
          <h2>Security Recommendations</h2>
        </div>

        <div class="recommendations-list">
          <div class="recommendation-item completed">
            <div class="recommendation-status">
              <i class="material-icons status-icon">check_circle</i>
            </div>
            <div class="recommendation-content">
              <h3>Strong Password</h3>
              <p>Your password meets our security requirements</p>
              <div class="recommendation-progress">
                <div class="progress-bar" style="width: 100%"></div>
              </div>
            </div>
          </div>

          <div class="recommendation-item">
            <div class="recommendation-status">
              <i class="material-icons status-icon">radio_button_unchecked</i>
            </div>
            <div class="recommendation-content">
              <h3>Enable Email Notifications</h3>
              <p>Get notified about important account activities</p>
              <div class="recommendation-progress">
                <div class="progress-bar" style="width: 0%"></div>
              </div>
              <div class="recommendation-action">
                <label class="toggle-switch">
                  <input type="checkbox">
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>

          <div class="recommendation-item">
            <div class="recommendation-status">
              <i class="material-icons status-icon">radio_button_unchecked</i>
            </div>
            <div class="recommendation-content">
              <h3>Regular Password Updates</h3>
              <p>Change your password every 90 days for better security</p>
              <div class="recommendation-progress">
                <div class="progress-bar" style="width: 0%"></div>
              </div>
            </div>
          </div>

          <div class="recommendation-item in-progress">
            <div class="recommendation-status">
              <i class="material-icons status-icon">timelapse</i>
            </div>
            <div class="recommendation-content">
              <h3>Verify Your Email</h3>
              <p>Confirm your email address to secure your account</p>
              <div class="recommendation-progress">
                <div class="progress-bar" style="width: 50%"></div>
              </div>
              <div class="recommendation-action">
                <button class="btn-text">
                  <i class="material-icons">email</i>
                  <span>Resend Verification</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Log -->
      <div class="form-section">
        <div class="section-header">
          <i class="material-icons section-icon">history</i>
          <h2>Recent Activity</h2>
        </div>

        <div class="activity-log">
          <div class="activity-item">
            <div class="activity-icon">
              <i class="material-icons">login</i>
            </div>
            <div class="activity-content">
              <div class="activity-header">
                <h3>Login</h3>
                <span class="activity-time">Today, 10:45 AM</span>
              </div>
              <p>Successful login from Chrome on Windows</p>
              <span class="activity-location">
                <i class="material-icons">location_on</i>
                New York, USA
              </span>
            </div>
          </div>

          <div class="activity-item">
            <div class="activity-icon">
              <i class="material-icons">password</i>
            </div>
            <div class="activity-content">
              <div class="activity-header">
                <h3>Password Changed</h3>
                <span class="activity-time">Yesterday, 3:20 PM</span>
              </div>
              <p>Your account password was successfully updated</p>
              <span class="activity-location">
                <i class="material-icons">location_on</i>
                New York, USA
              </span>
            </div>
          </div>

          <div class="activity-item">
            <div class="activity-icon">
              <i class="material-icons">login</i>
            </div>
            <div class="activity-content">
              <div class="activity-header">
                <h3>Login</h3>
                <span class="activity-time">May 15, 2023, 9:30 AM</span>
              </div>
              <p>Successful login from Safari on macOS</p>
              <span class="activity-location">
                <i class="material-icons">location_on</i>
                Boston, USA
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button
          type="submit"
          class="btn-primary"
          [disabled]="!securityForm.valid || loading">
          <div class="btn-content">
            <i class="material-icons btn-icon" *ngIf="loading">sync</i>
            <i class="material-icons btn-icon" *ngIf="!loading">save</i>
            <span>Update Password</span>
          </div>
          <div class="btn-shine"></div>
        </button>
      </div>
    </form>
  </div>
</div>

