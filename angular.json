{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"my-angular-app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/my-angular-app", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "allowedCommonJsDependencies": ["pako"], "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "src/assets/", "output": "/assets/"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "3MB"}, {"type": "anyComponentStyle", "maximumWarning": "15kB", "maximumError": "20kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "my-angular-app:build:production"}, "development": {"buildTarget": "my-angular-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/@popperjs/core/dist/umd/popper.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js"]}}}}}, "cli": {"analytics": "b5a0bd89-86d3-4712-b494-d96e986189b3"}}