/* User Management Styles */
.user-management-section {
  padding: var(--spacing-xl);
  background-color: var(--background);
  min-height: calc(100vh - 60px);
}

.user-management-content {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border);
  text-align: center;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.section-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text);
  margin: 0 0 var(--spacing-xs) 0;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: var(--radius-sm);
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: var(--spacing-md) 0 0;
}

.user-management-container {
  position: relative;
  background-color: var(--surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  border: 1px solid var(--border);
  transition: all var(--transition-normal);
}

.user-management-container.loading {
  min-height: 400px;
}

/* Message Styles */
.success-message {
  background-color: rgba(var(--success), 0.1);
  color: var(--success);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  border-left: 4px solid var(--success);
  display: flex;
  align-items: center;
}

.success-message::before {
  content: '✓';
  font-size: 1.2rem;
  margin-right: var(--spacing-md);
  font-weight: bold;
}

.error-message {
  background-color: rgba(var(--error-rgb), 0.1);
  color: var(--error);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  border-left: 4px solid var(--error);
  display: flex;
  align-items: center;
}

.error-message::before {
  content: '!';
  font-size: 1.2rem;
  margin-right: var(--spacing-md);
  font-weight: bold;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--elevation1-rgb), 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: var(--radius-lg);
  backdrop-filter: blur(4px);
}

.spinner {
  border: 4px solid rgba(var(--primary-rgb), 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Action Bar */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  background-color: var(--elevation1);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 40px;
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  font-size: 0.9rem;
  color: var(--text);
  background-color: var(--elevation2);
  transition: all var(--transition-fast);
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
  outline: none;
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.btn-add {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.btn-add i {
  margin-right: var(--spacing-sm);
}

.btn-add:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* User Form */
.user-form-container {
  background-color: var(--elevation1);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  border: 1px solid var(--border);
  position: relative;
}

.user-form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-gradient);
  z-index: 1;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: rgba(var(--primary-rgb), 0.05);
  border-bottom: 1px solid var(--border);
}

.form-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text);
  display: flex;
  align-items: center;
}

.form-header h2::before {
  content: '';
  display: inline-block;
  width: 10px;
  height: 10px;
  background: var(--primary);
  border-radius: 50%;
  margin-right: var(--spacing-sm);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.btn-close:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

.user-form {
  padding: var(--spacing-lg);
}

.form-row {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.form-group {
  margin-bottom: var(--spacing-lg);
  flex: 1;
}

.form-row .form-group {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  font-size: 0.95rem;
  color: var(--text);
  background-color: var(--elevation2);
  transition: all var(--transition-fast);
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
  outline: none;
}

.form-control.is-invalid {
  border-color: var(--error);
  box-shadow: 0 0 0 2px rgba(var(--error-rgb), 0.2);
}

.invalid-feedback {
  color: var(--error);
  font-size: 0.8rem;
  margin-top: var(--spacing-xs);
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.form-check-input {
  margin-right: var(--spacing-sm);
  width: 18px;
  height: 18px;
  accent-color: var(--primary);
}

.form-check-label {
  font-weight: 500;
  color: var(--text);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.btn-cancel {
  background-color: var(--elevation2);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.btn-cancel:hover {
  background-color: var(--surface-hover);
  color: var(--text);
}

.btn-submit {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.btn-submit:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-submit:disabled {
  background-color: rgba(var(--primary-rgb), 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Users Table */
.users-table-container {
  background-color: var(--elevation1);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  border: 1px solid var(--border);
  position: relative;
}

.users-table-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-gradient);
  z-index: 1;
}

.users-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  color: var(--text);
}

.users-table th, .users-table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.users-table th {
  background-color: rgba(var(--primary-rgb), 0.05);
  font-weight: 600;
  color: var(--text);
  white-space: nowrap;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.users-table tr:hover td {
  background-color: rgba(var(--primary-rgb), 0.03);
}

.users-table tr:last-child td {
  border-bottom: none;
}

.no-data {
  text-align: center;
  color: var(--text-secondary);
  padding: var(--spacing-lg);
  font-style: italic;
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all var(--transition-fast);
}

.badge-admin {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
}

.badge-user {
  background-color: rgba(var(--secondary-rgb), 0.1);
  color: var(--secondary);
  border: 1px solid rgba(var(--secondary-rgb), 0.2);
}

/* Action Buttons */
.actions-cell {
  white-space: nowrap;
}

.btn-edit, .btn-delete {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-right: var(--spacing-xs);
}

.btn-edit {
  color: var(--info);
}

.btn-edit:hover {
  background-color: rgba(var(--info), 0.1);
  transform: translateY(-2px);
}

.btn-delete {
  color: var(--error);
}

.btn-delete:hover {
  background-color: rgba(var(--error-rgb), 0.1);
  transform: translateY(-2px);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .user-management-section {
    padding: var(--spacing-md);
  }

  .user-management-container {
    padding: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .section-header {
    padding-top: 60px;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
    margin-bottom: var(--spacing-md);
  }

  .btn-add {
    width: 100%;
    justify-content: center;
  }

  .users-table {
    font-size: 0.9rem;
  }

  .users-table th, .users-table td {
    padding: var(--spacing-sm);
  }
}
