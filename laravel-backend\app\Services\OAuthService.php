<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\JsonResponse;

class OAuthService
{
    protected $client;

    /**
     * Initialize the OAuth service with a Guzzle HTTP client
     */
    public function __construct()
    {
        $this->client = new Client([
            'verify' => false,
            'timeout' => 30,
            'debug' => false, // Set debug to false to avoid STDIO FILE* error
        ]);
    }

    /**
     * Generate a Google OAuth authorization URL
     *
     * @return JsonResponse The URL for Google authentication
     */
    public function getGoogleAuthUrl(): JsonResponse
    {
        try {
            Log::info('Generating Google auth URL with config', [
                'client_id' => config('services.google.client_id'),
                'redirect' => config('services.google.redirect'),
                'frontend_url' => config('app.frontend_url')
            ]);

            // Use the redirect URI from config
            $redirectUri = config('services.google.redirect');
            Log::info('Using redirect URI from config', ['uri' => $redirectUri]);

            // Log all relevant configuration
            Log::info('Google OAuth Configuration', [
                'client_id' => config('services.google.client_id'),
                'redirect_uri' => $redirectUri,
                'env_redirect_uri' => env('GOOGLE_REDIRECT_URI'),
                'config_redirect_uri' => config('services.google.redirect')
            ]);

            // Manually build the Google OAuth URL
            $clientId = config('services.google.client_id');
            $scopes = ['email', 'profile', 'openid'];
            $scopeString = implode(' ', $scopes);

            $params = [
                'client_id' => $clientId,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => $scopeString,
                'access_type' => 'offline',
                'prompt' => 'select_account consent',
                'include_granted_scopes' => 'true'
            ];

            $url = 'https://accounts.google.com/o/oauth2/v2/auth?' . http_build_query($params);

            Log::info('Generated Google auth URL', ['url' => $url]);

            $response = response()->json(['url' => $url]);

            // Add CORS headers
            $origin = request()->header('Origin');
            if ($origin) {
                $response->header('Access-Control-Allow-Origin', $origin);
                $response->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
                $response->header('Access-Control-Allow-Headers', 'Content-Type, X-Auth-Token, Origin, Authorization, X-Requested-With');
                $response->header('Access-Control-Allow-Credentials', 'true');
            }

            return $response;
        } catch (\Exception $e) {
            Log::error('Failed to generate Google auth URL: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            $response = response()->json(['error' => 'Failed to generate Google auth URL.'], 500);

            // Add CORS headers
            $origin = request()->header('Origin');
            if ($origin) {
                $response->header('Access-Control-Allow-Origin', $origin);
                $response->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
                $response->header('Access-Control-Allow-Headers', 'Content-Type, X-Auth-Token, Origin, Authorization, X-Requested-With');
                $response->header('Access-Control-Allow-Credentials', 'true');
            }

            return $response;
        }
    }

    /**
     * Handle the Google OAuth callback
     *
     * @param string $code The authorization code from Google
     * @return array The tokens and user data
     * @throws \Exception If authentication fails
     */
    public function handleGoogleCallback(string $code): array
    {
        try {
            Log::info('Processing Google OAuth callback', ['code_length' => strlen($code)]);

            // Add debug logging for the redirect URI
            Log::info('Google OAuth redirect URI', [
                'redirect_uri' => config('services.google.redirect')
            ]);

            // Use the redirect URI from config
            $redirectUri = config('services.google.redirect');
            Log::info('Using redirect URI from config for token exchange', ['uri' => $redirectUri]);

            // Prepare token exchange parameters
            $tokenParams = [
                'code' => $code,
                'client_id' => config('services.google.client_id'),
                'client_secret' => config('services.google.client_secret'),
                'redirect_uri' => $redirectUri,
                'grant_type' => 'authorization_code'
            ];

            Log::info('Token exchange parameters', ['params' => $tokenParams]);

            // Exchange the authorization code for tokens
            $response = $this->client->post('https://oauth2.googleapis.com/token', [
                'form_params' => $tokenParams,
                'headers' => [
                    'Accept' => 'application/json'
                ]
            ]);

            $responseBody = $response->getBody()->getContents();
            Log::info('Token exchange response', ['response' => $responseBody]);

            $tokens = json_decode($responseBody, true);
            Log::info('Token exchange successful', [
                'has_access_token' => isset($tokens['access_token']),
                'has_refresh_token' => isset($tokens['refresh_token']),
                'token_type' => $tokens['token_type'] ?? 'not set'
            ]);

            $userResponse = $this->client->get('https://www.googleapis.com/oauth2/v3/userinfo', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $tokens['access_token'],
                    'Accept' => 'application/json'
                ]
            ]);

            $userData = json_decode($userResponse->getBody()->getContents(), true);
            Log::info('Retrieved user data from Google', [
                'sub' => $userData['sub'] ?? 'not set',
                'email' => $userData['email'] ?? 'not set'
            ]);

            return [
                'tokens' => $tokens,
                'user' => $userData
            ];

        } catch (ClientException $e) {
            Log::error('Google OAuth error', [
                'message' => $e->getMessage(),
                'response' => $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request' => $e->getRequest() ? (string)$e->getRequest()->getUri() : null,
                'request_body' => $e->getRequest() ? (string)$e->getRequest()->getBody() : null
            ]);
            throw new \Exception('Failed to authenticate with Google: ' . $this->formatOAuthError($e));
        } catch (\Exception $e) {
            Log::error('Google OAuth general error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception('Google authentication error: ' . $e->getMessage());
        }
    }

    /**
     * Format OAuth error messages into user-friendly messages
     *
     * @param ClientException $e The exception from the OAuth provider
     * @return string A user-friendly error message
     */
    protected function formatOAuthError(ClientException $e): string
    {
        try {
            $response = $e->getResponse();
            if (!$response) {
                return $e->getMessage();
            }

            $errorResponse = json_decode($response->getBody()->getContents(), true);
            $errorType = $errorResponse['error'] ?? 'unknown_error';
            $errorDesc = $errorResponse['error_description'] ?? 'No description provided';

            switch ($errorType) {
                case 'invalid_grant':
                    return "Authentication session expired or invalid. Please sign in again.";
                case 'invalid_client':
                    return "Application authentication failed. Please contact support.";
                case 'invalid_request':
                    return "Invalid authentication request. Please try again.";
                case 'access_denied':
                    return "Access was denied to your Google account. Please try again and grant the required permissions.";
                default:
                    return "$errorType: $errorDesc";
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }
}
