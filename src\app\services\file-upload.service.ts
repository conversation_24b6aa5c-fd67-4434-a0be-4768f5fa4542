// src/app/services/file-upload.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpEvent, HttpRequest, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';
import {
  FileUploadResponse,
  FileListResponse,
  DocumentGenerationResponse
} from '../models/file-upload.model';

@Injectable({
  providedIn: 'root'
})
export class FileUploadService {
  private apiUrl = environment.apiUrl + '/api'; // Laravel API URL
  private storageUrl = environment.storageUrl;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Get auth headers for API requests
   * @returns HttpHeaders with authentication token if available
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken();
    let headers = new HttpHeaders();

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.warn('No auth token available for file upload request');
    }

    return headers;
  }

  uploadFile(file: File): Observable<HttpEvent<FileUploadResponse>> {
    const formData = new FormData();

    // Make sure to use the correct field name expected by the backend
    // Use the third parameter to explicitly set the filename
    formData.append('document', file, file.name);

    // Log detailed information about the file and request
    console.log('Sending file upload request with:', {
      url: `${this.apiUrl}/upload`,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      fileExtension: file.name.split('.').pop()?.toLowerCase()
    });

    // Get auth headers
    const authHeaders = this.getAuthHeaders();

    // Create a request with proper headers and configuration
    const req = new HttpRequest(
      'POST',
      `${this.apiUrl}/upload`,
      formData,
      {
        reportProgress: true,
        responseType: 'json',
        headers: authHeaders
        // Let the browser set the appropriate Content-Type header for multipart/form-data
      }
    );

    return this.http.request<FileUploadResponse>(req);
  }



  getFiles(): Observable<FileListResponse> {
    return this.http.get<FileListResponse>(`${this.apiUrl}/files`, {
      headers: this.getAuthHeaders()
    });
  }

  deleteFile(filename: string): Observable<FileUploadResponse> {
    console.log(`Deleting file at: ${this.apiUrl}/files/delete/${filename}`);
    return this.http.delete<FileUploadResponse>(`${this.apiUrl}/files/delete/${filename}`, {
      headers: this.getAuthHeaders()
    });
  }

  deleteAllFiles(): Observable<FileUploadResponse> {
    console.log(`Deleting all files at: ${this.apiUrl}/files/deleteAll`);
    return this.http.delete<FileUploadResponse>(`${this.apiUrl}/files/deleteAll`, {
      headers: this.getAuthHeaders()
    });
  }

  getFileUrl(path: string): string {
    const url = `${this.storageUrl}/${path}`;
    console.log(`Generated file URL: ${url}`);
    return url;
  }



  /**
   * Generate a document with data replacements
   * @param filenameOrPath The filename for uploads or complete file_path for partner contracts
   * @param replacements Any data replacements to apply
   * @param isPartnerContract Whether this is a partner contract (true) or an uploaded file (false)
   * @returns Observable of DocumentGenerationResponse
   */
  generateDocument(filenameOrPath: string, replacements: any[] = [], isPartnerContract: boolean = false): Observable<DocumentGenerationResponse> {
    // Convert array of replacements to object format expected by the backend
    const replacementsObject: {[key: string]: string} = {};
    if (Array.isArray(replacements)) {
      replacements.forEach(item => {
        if (item && item.key && item.value !== undefined) {
          replacementsObject[item.key] = item.value;
        }
      });
    }

    // Create a document name based on the original filename
    const documentName = `Generated_${filenameOrPath.split('/').pop()?.split('.')[0] || 'Document'}_${new Date().toISOString().slice(0, 10)}`;

    // Simple approach like in c1 folder
    let filename = '';
    let file_path = '';

    // Handle both partner contracts and user uploads
    if (isPartnerContract) {
      // For partner contracts, ensure the path starts with 'contracts/'
      if (!filenameOrPath.startsWith('contracts/')) {
        file_path = 'contracts/' + filenameOrPath.split('/').pop();
      } else {
        file_path = filenameOrPath;
      }
      filename = file_path.split('/').pop() || '';
      console.log('Generating document from partner contract with path:', file_path);
    } else {
      // For regular uploads, use filename and add uploads/ prefix
      filename = filenameOrPath.includes('/') ? filenameOrPath.split('/').pop() || '' : filenameOrPath;
      file_path = 'uploads/' + filename;
      console.log('Generating document from uploaded file:', filename);
    }

    // Create a simple request body
    const finalRequest = {
      filename: filename,
      file_path: file_path,
      document_name: documentName,
      replacements: replacementsObject,
      is_partner_contract: isPartnerContract // Add this flag to help the backend identify partner contracts
    };

    console.log('Document generation request:', JSON.stringify(finalRequest));

    return this.http.post<DocumentGenerationResponse>(`${this.apiUrl}/documents/generate`, finalRequest, {
      headers: this.getAuthHeaders().set('Content-Type', 'application/json')
    });
  }

  downloadDocument(filename: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/documents/download/${filename}`, {
      responseType: 'blob',
      headers: this.getAuthHeaders()
    });
  }

  /**
   * Get extracted data from an uploaded file
   * @param filename The name of the uploaded file
   * @param isPartnerContract Whether this is a partner contract (true) or an uploaded file (false)
   * @returns Observable with extracted data
   */
  getExtractedData(filename: string, isPartnerContract: boolean = false): Observable<any> {
    console.log('Getting extracted data for file:', filename, isPartnerContract ? '(partner contract)' : '(user upload)');

    // Use the standard file extraction endpoint (now supports both uploads and contracts)
    return this.http.get(`${this.apiUrl}/files/extracted-data/${filename}`, {
      headers: this.getAuthHeaders()
    });
  }

  /**
   * Get extracted data from a contract by contract ID
   * @param contractId The ID of the contract
   * @returns Observable with extracted data
   */
  getContractExtractedData(contractId: number): Observable<any> {
    console.log('Getting extracted data for contract ID:', contractId);

    return this.http.get(`${this.apiUrl}/contracts/${contractId}/extracted-data`, {
      headers: this.getAuthHeaders()
    });
  }
}
