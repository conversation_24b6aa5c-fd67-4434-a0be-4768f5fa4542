/* Main container */
.partner-contracts-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Header styles */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text);
}

.section-header .highlight {
  color: #4ECDC4;
  position: relative;
}

.section-header .highlight::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #4ECDC4, transparent);
  border-radius: 3px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Partners grid */
.partners-grid-container {
  margin-bottom: 3rem;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
}

.partner-card {
  background-color: var(--elevation1);
  border-radius: 12px;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.partner-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #4ECDC4, transparent);
  transform: translateY(4px);
  transition: transform 0.3s ease;
}

.partner-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.partner-card:hover::after {
  transform: translateY(0);
}

.partner-card.selected {
  border-color: #4ECDC4;
  background-color: rgba(78, 205, 196, 0.05);
  box-shadow: 0 8px 16px rgba(78, 205, 196, 0.15);
}

.partner-card.selected::after {
  transform: translateY(0);
  background: #4ECDC4;
}

.partner-logo {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  border-radius: 50%;
  background-color: var(--elevation2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.partner-logo i {
  font-size: 2.5rem;
  color: #4ECDC4;
}

.partner-logo img {
  max-width: 60px;
  max-height: 60px;
  object-fit: contain;
}

.partner-info {
  width: 100%;
}

.partner-name {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text);
}

.partner-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* Contracts section */
.contracts-container {
  background-color: var(--elevation1);
  border-radius: 12px;
  padding: 2rem;
  margin-top: 2rem;
  border: 1px solid var(--border);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.contracts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.contracts-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text);
}

.contracts-title i {
  color: #4ECDC4;
}

.btn-upload {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-upload:hover {
  background-color: #3dbdb5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(78, 205, 196, 0.3);
}

.btn-upload i {
  font-size: 1.1rem;
}

/* Contracts grid */
.contracts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.contract-card {
  background-color: var(--elevation2);
  border-radius: 10px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border);
}

.contract-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.contract-card.selected {
  border-color: #4ECDC4;
  background-color: rgba(78, 205, 196, 0.05);
}

.contract-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  flex-shrink: 0;
}

.contract-icon i {
  font-size: 2rem;
}

.contract-icon i.fa-file-pdf {
  color: #e74c3c;
}

.contract-icon i.fa-file-word {
  color: #3498db;
}

.contract-info {
  flex: 1;
}

.contract-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--text);
}

.contract-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0 0 0.75rem;
  line-height: 1.4;
}

.contract-type {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.contract-type.pdf {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.contract-type.word {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

/* Loading and empty states */
.loading-partners,
.no-partners,
.loading-contracts,
.no-contracts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: var(--text-secondary);
}

.loading-partners i,
.no-partners i,
.loading-contracts i,
.no-contracts i {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  opacity: 0.7;
  color: #4ECDC4;
}

.btn-upload-suggestion {
  margin-top: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: transparent;
  color: #4ECDC4;
  border: 1px solid #4ECDC4;
  border-radius: 8px;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-upload-suggestion:hover {
  background-color: rgba(78, 205, 196, 0.1);
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .partners-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
  
  .contracts-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .section-header h2 {
    font-size: 2rem;
  }
  
  .partners-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
  }
  
  .partner-card {
    padding: 1.5rem;
  }
  
  .partner-logo {
    width: 60px;
    height: 60px;
  }
  
  .contracts-container {
    padding: 1.5rem;
  }
}

@media (max-width: 576px) {
  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .contracts-grid {
    grid-template-columns: 1fr;
  }
  
  .partner-name {
    font-size: 1.2rem;
  }
  
  .partner-logo {
    width: 50px;
    height: 50px;
    margin-bottom: 1rem;
  }
  
  .partner-logo i {
    font-size: 2rem;
  }
}
