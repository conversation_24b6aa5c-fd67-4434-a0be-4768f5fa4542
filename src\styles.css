@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap');

/* Global Styles */
:root {
  /* Primary Brand Colors */
  --primary: #4ECDC4; /* Teal/Turquoise */
  --primary-light: #6CDED6;
  --primary-dark: #3DACA4;
  --primary-rgb: 78, 205, 196;
  --primary-gradient: linear-gradient(135deg, #4ECDC4, #3DACA4);

  /* Secondary Colors */
  --secondary: #6c757d;
  --secondary-light: #868e96;
  --secondary-dark: #495057;
  --secondary-rgb: 108, 117, 125;

  /* Neutral Colors */
  --dark-gray: #1a1a1a; /* Dark gray */
  --dark-gray-light: #2a2a2a;
  --dark-gray-lighter: #3a3a3a;
  --light-gray: #f5f5f5;
  --light-gray-dark: #e0e0e0;

  /* Background & Surface Colors */
  --background: #ffffff;
  --surface: #ffffff;
  --surface-hover: #f5f5f5;
  --surfaceHover-rgb: 230, 230, 230;

  /* Text Colors - Improved for better contrast */
  --text: #1a1a1a;
  --text-secondary: #555555; /* Darker for better contrast */
  --textSecondary: #555555;
  --text-muted: #6b6b6b; /* Darker for better contrast */

  /* Border Colors */
  --border: #e5e7eb;
  --border-rgb: 0, 0, 0;

  /* Status Colors - Enhanced for better contrast */
  --error: #d32f2f; /* Darker red for better contrast */
  --error-rgb: 211, 47, 47;
  --error-light: #fee2e2;
  --success: #2e7d32; /* Darker green for better contrast */
  --success-light: #d1fae5;
  --warning: #ed6c02; /* Darker orange for better contrast */
  --warning-light: #fef3c7;
  --info: #0277bd; /* Darker blue for better contrast */
  --info-light: #e0f2fe;

  /* Elevation Colors */
  --elevation1: #ffffff;
  --elevation1-rgb: 255, 255, 255;
  --elevation2: #f5f5f5;
  --elevation2-rgb: 240, 240, 240;
  --elevation3: #eeeeee;
  --elevation3-rgb: 245, 245, 245;

  /* Overlay & Shadow */
  --overlay: rgba(0, 0, 0, 0.1);
  --overlay-rgb: 100, 100, 100;
  --shadow: rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 10px rgba(78, 205, 196, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Dark Theme Colors */
.dark-mode {
  /* Primary & Secondary Colors remain the same */
  --primary: #4ECDC4;
  --primary-light: #6CDED6;
  --primary-dark: #3DACA4;
  --primary-rgb: 78, 205, 196;
  --primary-gradient: linear-gradient(135deg, #4ECDC4, #3DACA4);

  /* Background & Surface Colors */
  --background: #1a1a1a;
  --surface: #1a1a1a;
  --surface-hover: #2c2c2c;
  --surfaceHover-rgb: 50, 50, 50;

  /* Text Colors - Improved for better contrast in dark mode */
  --text: #ffffff;
  --text-secondary: #cccccc; /* Lighter for better contrast in dark mode */
  --textSecondary: #cccccc;
  --text-muted: #a0a0a0; /* Lighter for better contrast in dark mode */

  /* Border Colors */
  --border: #2e2e2e;
  --border-rgb: 255, 255, 255;

  /* Status Colors - Brighter in dark mode for better visibility */
  --error: #f87171;
  --error-rgb: 248, 113, 113;
  --error-light: rgba(248, 113, 113, 0.2);
  --success: #34d399;
  --success-light: rgba(52, 211, 153, 0.2);
  --warning: #fbbf24;
  --warning-light: rgba(251, 191, 36, 0.2);
  --info: #60a5fa;
  --info-light: rgba(96, 165, 250, 0.2);

  /* Elevation Colors */
  --elevation1: #1a1a1a;
  --elevation1-rgb: 26, 26, 26;
  --elevation2: #252525;
  --elevation2-rgb: 35, 35, 35;
  --elevation3: #2c2c2c;
  --elevation3-rgb: 25, 25, 25;

  /* Overlay & Shadow */
  --overlay: rgba(255, 255, 255, 0.1);
  --overlay-rgb: 150, 150, 150;
  --shadow: rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.25);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Apply theme colors */
body {
  background-color: var(--background);
  color: var(--text);
  transition: background-color var(--transition-normal),
              color var(--transition-normal),
              border-color var(--transition-normal),
              box-shadow var(--transition-normal);
}

/* Common styles */
.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary);
  color: white;
}

.text-primary {
  color: var(--primary) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.border {
  border-color: var(--border) !important;
}

/* Card styles */
.card {
  background-color: var(--surface);
  border-color: var(--border);
}

.card:hover {
  background-color: var(--surface-hover);
}

/* Input styles */
input, select, textarea {
  background-color: var(--surface);
  color: var(--text);
  border-color: var(--border);
}

input:focus, select:focus, textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 102, 255, 0.25);
}

/* Navbar styles */
.navbar {
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
}

/* Sidebar styles */
.sidebar {
  background-color: var(--surface);
  border-right: 1px solid var(--border);
}

/* Footer styles */
.footer {
  background-color: var(--surface);
  border-top: 1px solid var(--border);
}

/* Material Icons */
.material-icons {
  color: var(--text);
}

html, body { height: 100%; }
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 14px;
}

/* Responsive font sizing */
@media screen and (min-width: 1200px) {
  body {
    font-size: 14px;
  }
}

@media screen and (max-width: 1199px) {
  body {
    font-size: 13px;
  }
}

@media screen and (max-width: 992px) {
  body {
    font-size: 12px;
  }
}

@media screen and (max-width: 768px) {
  body {
    font-size: 11px;
  }
}

/* Snackbar Styles */
.custom-snackbar {
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.snackbar-success {
  background-color: var(--success) !important;
  color: white !important;
}

.snackbar-error {
  background-color: var(--error) !important;
  color: white !important;
}

.mat-simple-snackbar-action {
  color: white !important;
}

/* Global styles */
body {
  background-color: var(--background);
  color: var(--text);
  transition: all 0.2s ease;
}

/* Container sizing */
.container, .container-fluid {
  max-width: 1400px;
  margin: 0 auto;
}

@media (min-width: 1600px) {
  .container, .container-fluid {
    max-width: 1400px;
  }
}

@media (max-width: 1599px) {
  .container, .container-fluid {
    max-width: 1200px;
  }
}

@media (max-width: 1200px) {
  .container, .container-fluid {
    max-width: 992px;
  }
}

/* Navbar styles */
.navbar {
  background-color: var(--elevation1) !important;
  border-bottom: 1px solid var(--border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

/* Card styles */
.card {
  background-color: var(--elevation1);
  border: 1px solid var(--border);
  border-radius: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px var(--shadow);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px var(--shadow);
}

/* Input styles */
.form-control {
  background-color: var(--elevation2);
  border: 1px solid var(--border);
  color: var(--text);
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
}

.form-control:focus {
  background-color: var(--elevation3);
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 102, 255, 0.2);
}

.form-control::placeholder {
  color: var(--textSecondary);
}

/* Dropdown styles */
.dropdown-menu {
  background-color: var(--elevation2);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: 0 4px 6px var(--shadow);
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.dropdown-item {
  color: var(--text);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: var(--surfaceHover);
  color: var(--text);
}

/* Button styles */
.btn {
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: var(--primary);
  border: none;
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary);
  filter: brightness(110%);
}

.btn-outline-primary {
  border: 1px solid var(--border);
  color: var(--text);
}

.btn-outline-primary:hover {
  background-color: var(--surfaceHover);
  border-color: var(--primary);
  color: var(--primary);
}

/* Table styles */
.table {
  color: var(--text);
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

.table th {
  background-color: var(--elevation1);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

.table td, .table th {
  padding: 1rem;
  border-bottom: 1px solid var(--border);
}

.table tbody tr:hover {
  background-color: var(--surfaceHover);
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--overlay);
  border-radius: 10px;
  border: 3px solid var(--background);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--textSecondary);
}

/* Sidebar styles */
.sidebar {
  background-color: var(--elevation1);
  border-right: 1px solid var(--border);
  transition: all 0.2s ease;
}

.sidebar .nav-link {
  color: var(--text);
  border-radius: 8px;
  margin: 0.25rem 0.5rem;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
  background-color: var(--surfaceHover);
}

.sidebar .nav-link.active {
  background-color: var(--primary);
  color: white;
}

/* Modal styles */
.modal-content {
  background-color: var(--elevation1);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: 0 8px 16px var(--shadow);
}

.modal-header {
  border-bottom: 1px solid var(--border);
  padding: 1.25rem 1.5rem;
}

.modal-footer {
  border-top: 1px solid var(--border);
  padding: 1.25rem 1.5rem;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background-color: var(--primary);
  color: white;
}

/* Status indicators */
.status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(5, 150, 105, 0.1);
  color: var(--success);
}

.status-pending {
  background-color: rgba(217, 119, 6, 0.1);
  color: var(--warning);
}

.status-inactive {
  background-color: rgba(220, 38, 38, 0.1);
  color: var(--error);
}

/* Component Container */
.component-container {
  width: 100%;
  background-color: var(--elevation1);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow);
  overflow: hidden;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  padding: 15px;
}

.component-container:hover {
  box-shadow: 0 8px 24px var(--shadow);
  transform: translateY(-2px);
}

.dark-mode .component-container {
  background-color: var(--elevation1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Utility classes */
.shadow-sm {
  box-shadow: 0 1px 2px var(--shadow);
}

.shadow {
  box-shadow: 0 1px 3px var(--shadow);
}

.shadow-lg {
  box-shadow: 0 4px 6px var(--shadow);
}

.rounded-lg {
  border-radius: 12px;
}

.border {
  border: 1px solid var(--border);
}

/* Transitions */
* {
  transition: background-color var(--transition-fast),
              color var(--transition-fast),
              border-color var(--transition-fast),
              box-shadow var(--transition-fast),
              transform var(--transition-fast),
              opacity var(--transition-fast);
}

/* Theme Transition - Applied during theme changes for smooth transitions */
.theme-transition {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease;
}

.theme-transition * {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease !important;
}

/* High Contrast Mode - Enhanced for better accessibility */
.high-contrast-mode {
  /* Primary colors with higher contrast */
  --primary: #00BFB3;
  --primary-light: #4ECDC4;
  --primary-dark: #00A99E;
  --primary-rgb: 0, 191, 179;
  --primary-gradient: linear-gradient(135deg, #00BFB3, #00A99E);

  /* Maximum contrast for text */
  --text: #000000;
  --text-secondary: #333333;
  --textSecondary: #333333;
  --text-muted: #444444;

  /* Clean background */
  --background: #ffffff;
  --surface: #ffffff;
  --surface-hover: #f0f0f0;
  --surfaceHover-rgb: 240, 240, 240;

  /* Strong borders for better visibility */
  --border: #000000;
  --border-rgb: 0, 0, 0;

  /* High contrast status colors */
  --error: #d32f2f;
  --error-rgb: 211, 47, 47;
  --error-light: #ffcdd2;
  --success: #2e7d32;
  --success-light: #c8e6c9;
  --warning: #ed6c02;
  --warning-light: #ffe0b2;
  --info: #0277bd;
  --info-light: #b3e5fc;

  /* Stronger shadows for better definition */
  --shadow: rgba(0, 0, 0, 0.2);
  --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4);
}

/* Dark High Contrast Mode */
.dark-mode.high-contrast-mode {
  /* Bright primary colors for dark mode */
  --primary: #00E6D8;
  --primary-light: #4EEEE5;
  --primary-dark: #00C5B8;
  --primary-rgb: 0, 230, 216;
  --primary-gradient: linear-gradient(135deg, #00E6D8, #00C5B8);

  /* Maximum contrast for text in dark mode */
  --text: #ffffff;
  --text-secondary: #f0f0f0;
  --textSecondary: #f0f0f0;
  --text-muted: #e0e0e0;

  /* Pure black background */
  --background: #000000;
  --surface: #000000;
  --surface-hover: #1a1a1a;
  --surfaceHover-rgb: 26, 26, 26;

  /* Strong white borders for better visibility */
  --border: #ffffff;
  --border-rgb: 255, 255, 255;

  /* Bright status colors for dark mode */
  --error: #ff5252;
  --error-rgb: 255, 82, 82;
  --error-light: rgba(255, 82, 82, 0.2);
  --success: #69f0ae;
  --success-light: rgba(105, 240, 174, 0.2);
  --warning: #ffab40;
  --warning-light: rgba(255, 171, 64, 0.2);
  --info: #40c4ff;
  --info-light: rgba(64, 196, 255, 0.2);
}

/* Zoom reset */
html {
  zoom: 0.9;
}

@media (max-width: 1200px) {
  html {
    zoom: 0.85;
  }
}

@media (max-width: 992px) {
  html {
    zoom: 0.8;
  }
}
