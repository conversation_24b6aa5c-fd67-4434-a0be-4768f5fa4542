:host {
  --signature-border-color: rgba(var(--primary-rgb, 78, 205, 196), 0.5);
  --signature-bg: rgba(var(--primary-rgb, 78, 205, 196), 0.05);
  --signature-border-width: 2px;
  --component-radius-sm: var(--radius-sm, 0.25rem);
  --component-radius-md: var(--radius-md, 0.5rem);
  --component-radius-lg: var(--radius-lg, 1rem);
}

/* Header Styles */
.security-section {
  padding: 2rem 1rem;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--background);
  min-height: 100vh;
}

.feature-content {
  width: 100%;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-top: 80px;
}

.section-header h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  width: 100%;
  white-space: nowrap;
  overflow: visible;
  color: var(--text);
  font-weight: bold;
  text-align: center;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.highlight {
  color: var(--primary);
  background: linear-gradient(90deg, var(--primary), var(--primary-light, #6CDED6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  display: inline-block;
}

/* Animation classes */
.animate__fadeInDown {
  animation: fadeInDown 1s ease forwards;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.security-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  color: var(--text);
  background-color: var(--elevation1);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

@media (min-width: 992px) {
  .security-container {
    grid-template-columns: 1fr 1fr;
  }
}

/* Contract Preview Section */
.contract-preview {
  padding: 25px;
  background-color: var(--elevation1);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.contract-preview:hover {
  box-shadow: var(--shadow-md);
}

.contract-preview h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--text);
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 10px;
}

.contract-preview h2 i {
  color: var(--primary);
}

.contract-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.file-preview-container {
  width: 100%;
  height: 500px;
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--elevation2);
  position: relative;
  box-shadow: var(--shadow-md);
  margin-bottom: 20px;
}

.pdf-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--elevation2);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.pdf-iframe-viewer {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--elevation2);
  display: block;
  border-radius: 8px;
  box-shadow: var(--shadow-md);
}

.pdf-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: var(--elevation2);
  color: var(--text);
  text-align: center;
  padding: 20px;
}

.error-icon {
  font-size: 4rem;
  color: var(--error);
  margin-bottom: 15px;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.doc-viewer, .pdf-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--elevation2);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.doc-viewer-content, .document-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--elevation2);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
}

.document-preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  max-width: 80%;
  height: 100%;
  color: var(--text);
}

.document-preview-content i.fas {
  font-size: 4rem;
  margin-bottom: 15px;
  color: var(--info);
}

.document-preview-content i.fa-file-word {
  color: var(--info);
}

.document-preview-content h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.document-preview-content p {
  margin-bottom: 15px;
  word-break: break-word;
  max-width: 100%;
}

.document-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.pdf-icon {
  font-size: 4rem;
  color: var(--error);
  margin-bottom: 15px;
}

.doc-icon {
  font-size: 4rem;
  color: var(--info);
  margin-bottom: 15px;
}

.no-file-selected, .loading-state, .error-state {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--elevation2);
  color: var(--text-secondary);
  padding: 20px;
}

.loading-state {
  background-color: var(--elevation2);
}

.error-state {
  background-color: var(--elevation2);
  color: var(--error);
  text-align: center;
}

.error-state i {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--error);
}

.error-state p {
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.upload-icon {
  font-size: 4rem;
  margin-bottom: 15px;
  color: var(--primary);
  animation: pulse 2s infinite;
}

/* Spinner for loading state */
.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(var(--primary-rgb, 78, 205, 196), 0.3);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

/* Upload Container */
.upload-container {
  margin-top: 20px;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: var(--elevation2);
  border: 2px dashed var(--primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 10px;
}

.upload-label:hover {
  background-color: var(--surface-hover);
  border-color: var(--primary-light, #6CDED6);
}

.upload-label .icon {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 10px;
}

.label-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text);
}

.upload-label small {
  color: var(--text-secondary);
  margin-top: 5px;
}

.file-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  padding: 8px 15px;
  background-color: rgba(var(--primary-rgb, 78, 205, 196), 0.2);
  border-radius: var(--radius-sm);
  color: var(--text);
}

.file-status i {
  color: var(--success);
}

.upload-input {
  display: none;
}

/* Document Info */
.document-info {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--elevation2);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.document-info h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.info-value {
  color: var(--text);
  font-weight: 500;
}

/* Document Actions */
.document-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: space-between;
  flex-wrap: wrap;
}

@media (max-width: 576px) {
  .document-actions {
    flex-direction: column;
  }
}

/* Signature Position Selector */
.signature-position-selector {
  margin-top: 20px;
}

.signature-position-selector h4 {
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: var(--text-white);
  display: flex;
  align-items: center;
  gap: 10px;
}

.position-options {
  display: flex;
  gap: 15px;
  justify-content: space-between;
}

.position-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 10px;
  border-radius: var(--radius-md);
}

.position-option:hover {
  background-color: rgba(78, 205, 196, 0.1);
}

.position-option.active {
  background-color: rgba(78, 205, 196, 0.2);
}

.position-preview {
  width: 80px;
  height: 50px;
  background-color: var(--elevation2);
  border-radius: var(--radius-sm);
  position: relative;
}

.position-preview::after {
  content: "";
  position: absolute;
  width: 30px;
  height: 15px;
  background-color: var(--primary);
  border-radius: 2px;
}

.position-preview.bottom-left::after {
  bottom: 5px;
  left: 5px;
}

.position-preview.bottom-center::after {
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
}

.position-preview.bottom-right::after {
  bottom: 5px;
  right: 5px;
}

/* Signing Section */
.signing-section {
  padding: 25px;
  background-color: var(--background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.signing-section:hover {
  box-shadow: var(--shadow-md);
}

.signing-section h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--text);
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 10px;
}

.signing-section h2 i {
  color: var(--primary);
}

.signing-section h3 {
  font-size: 1.4rem;
  color: var(--primary);
  margin-top: 25px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.signer-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Styling */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text);
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group label i {
  color: var(--primary);
}

.modern-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid var(--border);
  background-color: var(--elevation2);
  border-radius: var(--radius-md);
  color: var(--text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.modern-input:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 78, 205, 196), 0.2);
}

.modern-input::placeholder {
  color: var(--text-placeholder);
}

.error-message {
  display: block;
  color: var(--error);
  margin-top: 5px;
  font-size: 0.9rem;
}

/* Signature Area */
.signature-area {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--elevation2);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

/* Signature Type Selector */
.signature-type-selector {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.signature-type-btn {
  flex: 1;
  padding: 12px 15px;
  background-color: var(--elevation2);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  color: var(--text);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.signature-type-btn:hover {
  background-color: var(--surface-hover);
}

.signature-type-btn.active {
  background-color: var(--primary);
  color: white;
}

.signature-type-btn i {
  font-size: 1.1rem;
}

/* Draw Signature */
.signature-draw-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.signature-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.pen-color-options, .pen-size-options {
  display: flex;
  align-items: center;
  gap: 10px;
}

.option-label {
  color: var(--text-light-gray);
  font-size: 0.9rem;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid var(--border);
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: white;
  box-shadow: 0 0 0 2px var(--primary);
}

.size-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--elevation2);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  font-weight: 600;
  border: 2px solid var(--border);
}

.size-option:hover {
  background: var(--surface-hover);
  color: var(--text);
}

.size-option.active {
  background: var(--primary);
  color: white;
}

.signature-canvas-container {
  width: 100%;
  height: 200px;
  border: var(--signature-border-width) solid var(--signature-border-color);
  border-radius: var(--radius-md);
  background-color: var(--signature-bg);
  cursor: crosshair;
  transition: all 0.3s ease;
}

.signature-canvas-container:hover {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 78, 205, 196), 0.2);
}

/* Type Signature */
.signature-type-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.signature-font-options {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.font-option {
  padding: 10px 15px;
  background-color: var(--elevation2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.font-option:hover {
  background-color: var(--surface-hover);
}

.font-option.active {
  background-color: var(--primary);
}

.font-option span {
  font-size: 1.2rem;
  color: var(--text);
}

.typed-signature-input {
  width: 100%;
  padding: 15px;
  background-color: transparent;
  border: none;
  border-bottom: 2px solid var(--signature-border-color);
  color: var(--text);
  font-size: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.typed-signature-input:focus {
  outline: none;
  border-color: var(--primary);
}

.typed-signature-input::placeholder {
  color: var(--text-placeholder);
  opacity: 0.7;
}

.signature-helper-text {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 5px;
}

/* Upload Signature Styles */
.signature-upload-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-signature-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background-color: var(--elevation2);
  border-radius: var(--radius-md);
  min-height: 200px;
  position: relative;
}

.upload-instructions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 150px;
  width: 100%;
}

.upload-instructions i {
  font-size: 2.5rem;
  color: var(--primary);
  margin-bottom: 15px;
}

.upload-instructions p {
  font-size: 1.1rem;
  color: var(--text);
  margin-bottom: 5px;
}

.upload-instructions small {
  color: var(--text-secondary);
}

.signature-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  background-color: var(--signature-bg);
  border-radius: var(--radius-md);
}

.signature-image {
  max-width: 100%;
  max-height: 150px;
  object-fit: contain;
}

.signature-file-input {
  display: none;
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover:not(:disabled) {
  background-color: rgba(var(--primary-rgb, 78, 205, 196), 0.1);
}

/* Signature Controls */
.signature-controls {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  justify-content: center;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--text-on-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--elevation2);
  color: var(--text);
  border: 2px solid var(--border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--surface-hover);
}

/* Consent Checkbox */
.consent-check {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-top: 20px;
  cursor: pointer;
}

.consent-check input {
  margin-top: 3px;
  accent-color: var(--primary);
  width: 16px;
  height: 16px;
}

.consent-check span {
  color: var(--text);
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.action-buttons .btn {
  padding: 12px 25px;
  font-size: 1.1rem;
}

/* Spinner */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(var(--primary-rgb, 78, 205, 196), 0.3);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Message Display */
.status-messages {
  margin-top: 20px;
}

.alert {
  padding: 15px;
  border-radius: var(--radius-md);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.error {
  background-color: var(--error-bg);
  color: var(--error);
}

.success {
  background-color: var(--success-bg);
  color: var(--success);
}

.field-errors {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.field-errors .error {
  font-size: 0.9rem;
  padding: 8px 15px;
}

/* Utility classes */
.mt-2 {
  margin-top: 10px;
}

/* Responsive Styling */
@media (max-width: 992px) {
  .security-container {
    grid-template-columns: 1fr;
  }

  .file-preview-container,
  .pdf-viewer,
  .doc-viewer,
  .no-file-selected {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .section-header h2 {
    font-size: 2rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .position-options {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .signature-controls {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .file-preview-container,
  .pdf-viewer,
  .doc-viewer,
  .no-file-selected {
    min-height: 300px;
  }

  .signature-options {
    flex-direction: column;
    align-items: flex-start;
  }

  .signature-type-selector {
    flex-direction: column;
  }

  .doc-icon,
  .upload-icon {
    font-size: 3rem;
  }
}
