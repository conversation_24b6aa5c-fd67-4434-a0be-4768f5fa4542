<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UnsignedContract;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;

class UnsignedContractController extends Controller
{
    /**
     * Display a listing of unsigned contracts.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            // Check if user is authenticated
            if (!Auth::check()) {
                Log::warning('Unauthenticated user trying to access unsigned contracts');
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to view contracts'
                ], 401);
            }

            $userId = Auth::id();
            Log::info('Fetching unsigned contracts for user', ['user_id' => $userId]);

            // Only show contracts for the authenticated user
            $contracts = UnsignedContract::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            Log::info('Unsigned contracts fetched successfully', [
                'user_id' => $userId,
                'count' => $contracts->count()
            ]);

            return response()->json($contracts);
        } catch (\Exception $e) {
            Log::error('Error fetching unsigned contracts', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Error fetching unsigned contracts: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified unsigned contract.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            // Check if user is authenticated
            if (!Auth::check()) {
                Log::warning('Unauthenticated user trying to access unsigned contract details', ['contract_id' => $id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to view contract details'
                ], 401);
            }

            $userId = Auth::id();
            Log::info('Fetching unsigned contract details for user', ['user_id' => $userId, 'contract_id' => $id]);

            // Only allow access to the authenticated user's contracts
            $contract = UnsignedContract::where('user_id', $userId)
                ->where('id', $id)
                ->first();

            if ($contract) {
                Log::info('Found unsigned contract for user', ['user_id' => $userId, 'contract_id' => $id]);
                return response()->json($contract);
            } else {
                Log::warning('Contract not found for user or unauthorized access', [
                    'user_id' => $userId,
                    'contract_id' => $id
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Unsigned contract not found or not authorized'
                ], 404);
            }
        } catch (\Exception $e) {
            Log::error('Error fetching unsigned contract details', [
                'contract_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error fetching unsigned contract: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * View an unsigned contract file without downloading it.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function view($id)
    {
        try {
            // Check if user is authenticated
            if (!Auth::check()) {
                Log::warning('Unauthenticated user trying to view unsigned contract', ['contract_id' => $id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to view contract'
                ], 401);
            }

            $userId = Auth::id();
            Log::info('Viewing unsigned contract for user', ['user_id' => $userId, 'contract_id' => $id]);

            // Only allow access to the authenticated user's contracts
            $contract = UnsignedContract::where('user_id', $userId)
                ->where('id', $id)
                ->first();

            if (!$contract) {
                Log::warning('Contract not found for user or unauthorized access', [
                    'user_id' => $userId,
                    'contract_id' => $id
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Unsigned contract not found or not authorized'
                ], 404);
            }

            Log::info('Found unsigned contract for viewing', [
                'user_id' => $userId,
                'contract_id' => $id
            ]);

            if (!$contract->file_path || !Storage::disk('public')->exists($contract->file_path)) {
                Log::error('Unsigned contract file not found', [
                    'contract_id' => $id,
                    'file_path' => $contract->file_path
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Unsigned contract file not found'
                ], 404);
            }

            $fullPath = Storage::disk('public')->path($contract->file_path);
            $fileName = basename($contract->file_path);

            // Determine the MIME type from the file or use the stored file_type
            $mimeType = $contract->file_type ?? File::mimeType($fullPath);

            // Map file_type to proper MIME type if needed
            if ($mimeType === 'word') {
                $mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            } else if ($mimeType === 'pdf') {
                $mimeType = 'application/pdf';
            }

            Log::info('Serving unsigned contract file for viewing', [
                'contract_id' => $id,
                'file_path' => $fullPath,
                'mime_type' => $mimeType,
                'file_exists' => file_exists($fullPath),
                'file_size' => file_exists($fullPath) ? filesize($fullPath) : 'N/A'
            ]);

            // Check if file exists and is readable
            if (!file_exists($fullPath) || !is_readable($fullPath)) {
                Log::error('File exists but is not readable', [
                    'path' => $fullPath,
                    'exists' => file_exists($fullPath),
                    'readable' => is_readable($fullPath),
                    'permissions' => file_exists($fullPath) ? substr(sprintf('%o', fileperms($fullPath)), -4) : 'N/A'
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'File exists but is not readable'
                ], 500);
            }

            // Return file content without forcing download
            return response()->file($fullPath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . $fileName . '"',
            ]);
        } catch (\Exception $e) {
            Log::error('Error viewing unsigned contract', [
                'contract_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error viewing unsigned contract: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download an unsigned contract file.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function download($id)
    {
        try {
            // Check if user is authenticated
            if (!Auth::check()) {
                Log::warning('Unauthenticated user trying to download unsigned contract', ['contract_id' => $id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to download contract'
                ], 401);
            }

            $userId = Auth::id();
            Log::info('Downloading unsigned contract for user', ['user_id' => $userId, 'contract_id' => $id]);

            // Only allow access to the authenticated user's contracts
            $contract = UnsignedContract::where('user_id', $userId)
                ->where('id', $id)
                ->first();

            if (!$contract) {
                Log::warning('Contract not found for user or unauthorized access', [
                    'user_id' => $userId,
                    'contract_id' => $id
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Unsigned contract not found or not authorized'
                ], 404);
            }

            Log::info('Found unsigned contract for download', [
                'user_id' => $userId,
                'contract_id' => $id
            ]);

            if (!$contract->file_path || !Storage::disk('public')->exists($contract->file_path)) {
                Log::error('Unsigned contract file not found', [
                    'contract_id' => $id,
                    'file_path' => $contract->file_path
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Unsigned contract file not found'
                ], 404);
            }

            $fullPath = Storage::disk('public')->path($contract->file_path);
            $fileName = basename($contract->file_path);

            // Determine the MIME type from the file or use the stored file_type
            $mimeType = $contract->file_type ?? File::mimeType($fullPath);

            // Map file_type to proper MIME type if needed
            if ($mimeType === 'word') {
                $mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            } else if ($mimeType === 'pdf') {
                $mimeType = 'application/pdf';
            }

            Log::info('Serving unsigned contract file', [
                'contract_id' => $id,
                'file_path' => $fullPath,
                'mime_type' => $mimeType,
                'file_exists' => file_exists($fullPath),
                'file_size' => file_exists($fullPath) ? filesize($fullPath) : 'N/A'
            ]);

            // Check if file exists and is readable
            if (!file_exists($fullPath) || !is_readable($fullPath)) {
                Log::error('File exists but is not readable', [
                    'path' => $fullPath,
                    'exists' => file_exists($fullPath),
                    'readable' => is_readable($fullPath),
                    'permissions' => file_exists($fullPath) ? substr(sprintf('%o', fileperms($fullPath)), -4) : 'N/A'
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'File exists but is not readable'
                ], 500);
            }

            return Response::download($fullPath, $fileName, [
                'Content-Type' => $mimeType,
            ]);
        } catch (\Exception $e) {
            Log::error('Error downloading unsigned contract', [
                'contract_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error downloading unsigned contract: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an unsigned contract.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            Log::info('Deleting unsigned contract', ['contract_id' => $id]);

            $contract = UnsignedContract::where('user_id', Auth::id())
                ->findOrFail($id);

            // Delete the file if it exists
            if ($contract->file_path && Storage::disk('public')->exists($contract->file_path)) {
                Storage::disk('public')->delete($contract->file_path);
                Log::info('Deleted unsigned contract file', ['file_path' => $contract->file_path]);
            }

            $contract->delete();
            Log::info('Unsigned contract deleted successfully', ['contract_id' => $id]);

            return response()->json([
                'success' => true,
                'message' => 'Unsigned contract deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting unsigned contract', [
                'contract_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error deleting unsigned contract: ' . $e->getMessage()
            ], 500);
        }
    }
}
