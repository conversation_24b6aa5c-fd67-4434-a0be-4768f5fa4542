/* Navbar Styles - Improved */

/* Navbar Styles */

.navbar {
  background: rgba(var(--elevation1-rgb, 255, 255, 255), 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.08);
  padding: 0;
  box-shadow: 0 4px 20px -5px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  right: 0;
  left: 0;
  z-index: 98;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark-mode .navbar {
  background: rgba(var(--elevation2-rgb, 30, 30, 30), 0.95);
  box-shadow: 0 4px 20px -5px rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Navbar Container */
.navbar-container {
  width: 100%;
  max-width: 1320px;
  padding: 0 1rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-brand {
  display: flex;
  align-items: center;
}

.logo {
  height: 32px;
  width: auto;
  transition: filter 0.3s ease;
}

.logo.dark {
  filter: brightness(0) invert(1);
}

.search-form {
  flex: 0 1 500px;
  margin: 0 2rem;
  position: relative;
}

.input-group {
  display: flex;
  align-items: center;
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.5);
  border: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.05);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 46px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.input-group:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 0, 120, 255), 0.15), 0 4px 15px rgba(0, 0, 0, 0.05);
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.8);
  transform: translateY(-2px);
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0 0.75rem;
  color: var(--text-secondary);
  background: transparent;
  border: none;
  font-size: 1.25rem;
}

.form-control {
  flex: 1;
  padding: 0 0.75rem;
  border: none;
  background: transparent;
  color: var(--text);
  font-size: 0.95rem;
  font-weight: 500;
  height: 100%;
}

.form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
  font-weight: 400;
}

.form-control:focus {
  outline: none;
}

.search-results {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  right: 0;
  background: var(--elevation1);
  border-radius: 12px;
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.2);
  padding: 1rem;
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-results.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}


.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 46px;
  padding: 0;
  border: none;
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.5);
  border-radius: 16px;
  color: var(--text);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.btn-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
  border-radius: inherit;
}

.btn-icon:hover {
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.8);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px -5px rgba(var(--primary-rgb, 0, 120, 255), 0.2);
  color: var(--primary);
}

.btn-icon:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px -5px rgba(var(--primary-rgb, 0, 120, 255), 0.15);
}

.btn-icon i {
  font-size: 1.4rem;
  position: relative;
  z-index: 1;
}



.btn {
  padding: 0 1.5rem;
  height: 46px;
  border-radius: 16px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
}

.btn::after {
  content: '';
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn:hover::after {
  opacity: 1;
}

.btn-light {
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.5);
  color: var(--text);
  border: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.btn-light:hover {
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.8);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.1);
}

.btn-light:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px -5px rgba(0, 0, 0, 0.08);
}

.btn-outline-primary {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.btn-outline-primary:hover {
  background: rgba(var(--primary-rgb, 0, 120, 255), 0.1);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px -5px rgba(var(--primary-rgb, 0, 120, 255), 0.2);
}

.btn-outline-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px -5px rgba(var(--primary-rgb, 0, 120, 255), 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  box-shadow: 0 5px 15px -5px rgba(var(--primary-rgb, 0, 120, 255), 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px -5px rgba(var(--primary-rgb, 0, 120, 255), 0.4);
}

.btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px -5px rgba(var(--primary-rgb, 0, 120, 255), 0.3);
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 22px;
  height: 22px;
  padding: 0 6px;
  background: var(--error);
  color: white;
  border-radius: 11px;
  font-size: 0.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px -2px rgba(var(--error-rgb, 255, 0, 0), 0.3);
  border: 2px solid var(--elevation1);
  z-index: 2;
  transform: scale(1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 10px -2px rgba(var(--error-rgb, 255, 0, 0), 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 15px -2px rgba(var(--error-rgb, 255, 0, 0), 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 10px -2px rgba(var(--error-rgb, 255, 0, 0), 0.3);
  }
}

.profile-menu {
  position: relative;
}

.avatar {
  width: 38px;
  height: 38px;
  border-radius: 12px;
  object-fit: cover;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.avatar:hover {
  border-color: var(--primary);
  transform: scale(1.05);
}

.profile-button {
  padding: 3px;
  border: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.05);
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.5);
  border-radius: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.profile-button:hover {
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.8);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px -5px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .search-form {
    display: none;
  }

  .btn {
    padding: 0.5rem;
  }


}

/* Search Form */
.search-form {
  max-width: 400px;
  width: 100%;
  align-items: center;
}

/* Navbar Right */
.navbar-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Buttons */
.btn-icon {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: 8px;
  color: var(--textSecondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: var(--surfaceHover);
  color: var(--primary);
}

/* Badge */
.badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--error);
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Avatar */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.avatar-large {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  object-fit: cover;
}

/* Dropdowns */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 15px);
  right: 0;
  min-width: 280px;
  background: rgba(var(--elevation1-rgb, 255, 255, 255), 0.98);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 20px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12), 0 10px 30px rgba(0, 0, 0, 0.08);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px) scale(0.95);
  transform-origin: top right;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 1000;
  border: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.05);
  overflow: hidden;
  padding: 0.5rem;
}

.dropdown-menu::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 20px;
  width: 16px;
  height: 16px;
  background: rgba(var(--elevation1-rgb, 255, 255, 255), 0.98);
  transform: rotate(45deg);
  border-top: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.05);
  border-left: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.05);
  z-index: -1;
  box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.02);
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.dropdown-header {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.05);
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.5);
}

.dropdown-body {
  padding: 0.75rem;
  max-height: 350px;
  overflow-y: auto;
}

.dropdown-footer {
  padding: 1rem;
  border-top: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.05);
  text-align: center;
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.5);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.9rem 1rem;
  color: var(--text);
  text-decoration: none;
  border-radius: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0.25rem;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 0 2px 2px 0;
}

.dropdown-item:hover {
  background: rgba(var(--surfaceHover-rgb, 230, 230, 230), 0.5);
  color: var(--primary);
  transform: translateX(4px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
}

.dropdown-item:hover::before {
  opacity: 1;
}

.dropdown-item i {
  color: var(--textSecondary);
  font-size: 1.4rem;
  transition: all 0.3s ease;
}

.dropdown-item:hover i {
  color: var(--primary);
  transform: scale(1.1) rotate(-5deg);
}

.dropdown-divider {
  height: 1px;
  margin: 0.5rem 0;
  background: rgba(var(--border-rgb, 0, 0, 0), 0.05);
  border: none;
}



/* Auth Buttons */
.auth-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 10px;
  height: 100%;
}

.auth-buttons .btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.auth-buttons .btn i {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.auth-buttons .btn:hover i {
  transform: translateY(-2px);
}

.auth-buttons .btn-outline-primary {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
  padding: 0 18px;
  height: 40px;
  border-radius: 12px;
}

.auth-buttons .btn-outline-primary:hover {
  background: rgba(var(--primary-rgb), 0.08);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px -5px rgba(var(--primary-rgb), 0.25);
}

.auth-buttons .btn-outline-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px -5px rgba(var(--primary-rgb), 0.2);
}

.auth-buttons .btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  padding: 0 20px;
  height: 40px;
  border-radius: 12px;
  box-shadow: 0 6px 15px -5px rgba(var(--primary-rgb), 0.35);
}

.auth-buttons .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px -5px rgba(var(--primary-rgb), 0.5);
}

.auth-buttons .btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 12px -5px rgba(var(--primary-rgb), 0.4);
}

.auth-buttons .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.auth-buttons .btn:hover::before {
  transform: translateX(100%);
}

/* Language Selector */
.language-dropdown {
  position: relative;
}

.language-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 0 10px;
  width: auto !important;
}

.language-code {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.language-flag {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.language-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.language-item .language-flag {
  width: 20px;
  height: 20px;
}

.language-item:hover .language-flag {
  transform: scale(1.1);
}

.language-item:hover {
  background-color: rgba(var(--primary-rgb, 119, 30, 118), 0.1);
}

/* Responsive */
@media (max-width: 991.98px) {
  .navbar {
    left: 0;
    width: 100%;
  }

  .navbar.sidebar-expanded {
    left: 280px;
    width: calc(100% - 280px);
  }

  .navbar-container {
    max-width: 100%;
  }

  .search-form {
    max-width: none;
    margin: 0 1rem;
  }

  .navbar-right {
    gap: 0.5rem;
  }

  .btn-icon {
    width: 42px;
    height: 42px;
  }
}

@media (max-width: 767.98px) {
  .navbar {
    padding: 0;
  }

  .navbar-container {
    padding: 0 0.5rem;
  }

  .search-form {
    display: none;
  }

  .navbar-brand {
    margin-right: auto;
  }

  .btn-text {
    display: none;
  }

  .btn {
    padding: 0;
    width: 42px;
  }

  .dropdown-menu {
    min-width: 280px;
    right: -10px;
  }

  .dropdown-menu::before {
    right: 15px;
  }

  .auth-buttons {
    gap: 8px;
  }

  .auth-buttons .btn {
    font-size: 0;
    height: 36px;
    width: 36px;
    padding: 0;
    justify-content: center;
  }

  .auth-buttons .btn i {
    font-size: 18px;
    margin: 0;
  }

  .auth-buttons .btn-outline-primary,
  .auth-buttons .btn-primary {
    padding: 0;
  }
}

/* Dark Mode Enhancements */
.dark-mode .dropdown-menu {
  background: rgba(var(--elevation1-rgb, 30, 30, 30), 0.95);
}

.dark-mode .dropdown-menu::before {
  background: rgba(var(--elevation1-rgb, 30, 30, 30), 0.95);
}



.dark-mode .btn-icon:hover {
  background: rgba(var(--elevation2-rgb, 50, 50, 50), 0.8);
}

.dark-mode .input-group:focus-within {
  background: rgba(var(--elevation2-rgb, 50, 50, 50), 0.8);
}

.dark-mode .auth-buttons .btn-outline-primary {
  border-color: var(--primary);
  color: var(--primary);
}

.dark-mode .auth-buttons .btn-outline-primary:hover {
  background: rgba(var(--primary-rgb), 0.15);
  box-shadow: 0 6px 15px -5px rgba(var(--primary-rgb), 0.4);
}

.dark-mode .auth-buttons .btn-primary {
  box-shadow: 0 6px 15px -5px rgba(var(--primary-rgb), 0.5);
}

.dark-mode .auth-buttons .btn-primary:hover {
  box-shadow: 0 8px 20px -5px rgba(var(--primary-rgb), 0.7);
}
