<div class="partner-management-container">
  <div class="page-header">
    <h1>Partner Management</h1>
    <p>Create and manage your organization's partners and their contracts</p>
  </div>

  <div class="dashboard-layout">
    <!-- Left column: Partner Form -->
    <div class="form-section">
      <div class="card">
        <div class="card-header">
          <div class="header-icon">
            <mat-icon class="icon-primary">add_circle</mat-icon>
          </div>
          <h2>Add New Partner</h2>
        </div>

        <div class="card-body">
          <form [formGroup]="partnerForm" (ngSubmit)="onSubmit()">
            <div class="form-group">
              <input type="text" formControlName="name" placeholder="Partner Name*" class="form-input" required>
            </div>

            <div class="form-group">
              <input type="text" formControlName="logo" placeholder="Logo URL*" class="form-input" required>
            </div>

            <div class="form-group">
              <textarea formControlName="description" placeholder="Description" rows="3" class="form-input textarea"></textarea>
            </div>

            <div class="form-actions">
              <button type="submit" [disabled]="partnerForm.invalid || loading" class="add-partner-btn">
                Add Partner
              </button>
              <button type="button" (click)="debugInfo()" class="add-partner-btn" style="margin-left: 10px; background-color: #666;">
                Debug Info
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Right column: Partners List -->
    <div class="list-section">
      <div class="card">
        <div class="card-header">
          <div class="header-icon">
            <mat-icon class="icon-primary">business</mat-icon>
          </div>
          <h2>Partners List</h2>
        </div>

        <div class="card-body">
          <div class="loading-container" *ngIf="loading">
            <mat-spinner diameter="30" color="accent"></mat-spinner>
          </div>

          <div class="table-container" *ngIf="!loading">
            <table class="partners-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Description</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let partner of partners">
                  <td>{{partner.id}}</td>
                  <td class="name-cell">
                    <div class="partner-info">
                      <img [src]="partner.logo" alt="Partner logo" class="partner-logo" *ngIf="partner.logo">
                      <span>{{partner.name}}</span>
                    </div>
                  </td>
                  <td class="description-cell">
                    <span class="description-text">{{partner.description || 'No description provided'}}</span>
                  </td>
                  <td class="action-cell">
                    <button class="action-icon" (click)="editPartner(partner)" title="Edit Partner">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button class="action-icon" (click)="viewPartnerContracts(partner.id)" title="View Partner Contracts">
                      <mat-icon>description</mat-icon>
                    </button>
                    <button class="action-icon" (click)="deletePartner(partner.id)" title="Delete Partner">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>

            <div class="no-data-message" *ngIf="partners.length === 0">
              <mat-icon>business_off</mat-icon>
              <p>No partners found</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


