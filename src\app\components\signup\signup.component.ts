import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, ValidationErrors, ValidatorFn, AbstractControl } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule, HttpHeaders } from '@angular/common/http';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { AuthService } from '../../services/auth.service';

@Component({
  standalone: true,
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.css'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    HttpClientModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule
  ]
})
export class SignupComponent {
  signupForm: FormGroup;
  showPassword = false;
  message: string = '';
  isLoading = false;
  googleAuthUrl: string = '';
  facebookAuthUrl: string = '';


  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private http: HttpClient
  ) {
    this.signupForm = this.fb.group(
      {
        firstname: ['', Validators.required],
        lastname: ['', Validators.required],
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required, Validators.minLength(8)]],
        password_confirmation: ['', Validators.required],
        terms: [false, Validators.requiredTrue]
      },
      { validators: this.passwordMatchValidator() }
    );

    // Initialize auth URLs
    const baseUrl = this.authService.getApiUrl();
    this.googleAuthUrl = `${baseUrl}/auth/google`;
    this.facebookAuthUrl = `${baseUrl}/auth/facebook`;

    console.log('Google Auth URL:', this.googleAuthUrl);
    console.log('Facebook Auth URL:', this.facebookAuthUrl);
  }

  passwordMatchValidator(): ValidatorFn {
    return (group: AbstractControl): ValidationErrors | null => {
      const password = group.get('password')?.value;
      const confirmPassword = group.get('password_confirmation')?.value;
      return password === confirmPassword ? null : { mismatch: true };
    };
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  onSubmit() {
    if (this.signupForm.valid) {
      this.isLoading = true;
      
      // The terms acceptance is already in the form data and will be sent to the backend
      this.authService.register(this.signupForm.value).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.message = 'Registration successful!.';
          
          // If we get a token back, the user is automatically logged in
          if (response.token) {
            console.log('User registered and logged in successfully:', response);
          }
          
          this.signupForm.reset();
          
          // Set a timeout to redirect to signin page after 3 seconds
          setTimeout(() => {
            window.location.href = '/signin';
          }, 3000);
        },
        error: (error) => {
          this.message = error?.error?.message || 'An error occurred during registration';
          console.error('Registration error:', error);
          this.isLoading = false;
        },
        complete: () => {
          // The isLoading state is now handled in the next and error callbacks
        }
      });
    }
  }

  onGoogleSignup() {
    this.isLoading = true;
    this.message = '';

    // Store the current URL to redirect back after authentication
    localStorage.setItem('auth_redirect', '/profile');
    console.log('Stored redirect URL for after auth:', '/profile');

    // Get the base URL and redirect directly
    const baseUrl = this.authService.getApiUrl();
    const googleAuthUrl = `${baseUrl}/auth/google`;

    console.log('Redirecting directly to Google auth URL:', googleAuthUrl);
    window.location.href = googleAuthUrl;
  }

  onFacebookSignup() {
    this.isLoading = true;
    this.message = '';

    // Store the current URL to redirect back after authentication
    localStorage.setItem('auth_redirect', '/profile');
    console.log('Stored redirect URL for after auth:', '/profile');

    // Get the base URL and redirect directly
    const baseUrl = this.authService.getApiUrl();
    const facebookAuthUrl = `${baseUrl}/auth/facebook`;

    console.log('Redirecting directly to Facebook auth URL:', facebookAuthUrl);
    window.location.href = facebookAuthUrl;
  }
}

