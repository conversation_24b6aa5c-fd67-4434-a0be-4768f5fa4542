import { <PERSON>mponent, <PERSON><PERSON>nit, ViewChild, <PERSON>ement<PERSON>ef, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute, Params } from '@angular/router';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';
import { HttpEventType } from '@angular/common/http';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { FileUploadService } from '../../services/file-upload.service';
import { DocumentGenerationService } from '../../services/document-generation.service';
import { AuthService } from '../../services/auth.service';
import { PartnerService } from '../../services/partner.service';
import { ExtractedDataItem } from '../../models/file-upload.model';
import { Partner, Contract } from '../partner-contracts/partner-contracts.component';
import { environment } from '../../../environments/environment';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-file-upload',
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    SpinnerComponent,
    ReactiveFormsModule,
    FormsModule
  ],
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate('300ms ease-in', style({ transform: 'translateX(0%)', opacity: 1 }))
      ])
    ])
  ]
})
export class FileUploadComponent implements OnInit, OnDestroy {
  @ViewChild('fileInput') fileInput!: ElementRef;

  // File upload properties
  selectedFile: File | null = null;
  currentFileName: string = '';
  fileList: string[] = [];
  filteredFiles: string[] = [];
  isUploading: boolean = false;
  isLoadingFiles: boolean = false;
  isLoadingContract: boolean = false; // New loading state for contract loading
  uploadProgress: number = 0;
  uploadMessage: string = '';
  extractedData: ExtractedDataItem[] = [];
  isGenerating: boolean = false;
  generatedDocumentUrl: string | null = null;
  generationError: string | null = null;
  generatedUnsignedContractId: number | null = null;
  generatedDocumentId: number | null = null; // Store the ID of the generated document
  reloadCountdown: number = 0;
  maxFileSize: number = 10 * 1024 * 1024; // 10MB in bytes
  isDragOver: boolean = false;
  filePreviewUrl: SafeResourceUrl | null = null;
  showExtractedData: boolean = false; // Flag to control visibility of extracted data form

  // Search and filter properties
  searchTerm: string = '';
  fileTypeFilter: 'all' | 'pdf' | 'word' = 'all';

  // Add date-related properties
  currentDate = new Date();
  formattedDate = this.currentDate.toLocaleDateString('fr-FR');

  // Subscription management
  private subscriptions: Subscription[] = [];

  // Partner and Contract properties
  partners: Partner[] = [];
  contracts: Contract[] = [];
  selectedPartnerId: number | null = null;
  selectedContractId: number | null = null;
  isLoadingPartners: boolean = false;
  isLoadingContracts: boolean = false;

  constructor(
    private fileUploadService: FileUploadService,
    private documentGenerationService: DocumentGenerationService,
    private sanitizer: DomSanitizer,
    private router: Router,
    private route: ActivatedRoute,
    private authService: AuthService,
    private partnerService: PartnerService
  ) {}

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.processFile(file);
    }
  }

  onFileDropped(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer?.files.length) {
      const file = event.dataTransfer.files[0];
      this.processFile(file);
    }
  }

  processFile(file: File): void {
    // Check file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const validExtensions = ['pdf', 'doc', 'docx'];

    // Log file information for debugging
    console.log('Processing file:', {
      name: file.name,
      type: file.type,
      extension: fileExtension,
      size: file.size
    });

    if (!validExtensions.includes(fileExtension || '')) {
      this.uploadMessage = 'Format de fichier non supporté. Veuillez utiliser PDF ou Word (.pdf, .doc, .docx).';
      this.fileInput.nativeElement.value = '';
      return;
    }

    // Check file size
    if (file.size > this.maxFileSize) {
      this.uploadMessage = 'Le fichier est trop volumineux. La taille maximale est de 10 MB.';
      this.fileInput.nativeElement.value = '';
      return;
    }

    // Reset partner contract selection and extracted data form
    this.selectedContractId = null;
    this.selectedContractFilePath = null;
    this.showExtractedData = false; // Hide extracted data form when selecting a new file
    this.extractedData = []; // Clear any previous extracted data

    this.selectedFile = file;
    this.uploadMessage = '';
    this.uploadProgress = 0;

    // Create file preview URL for PDF files
    if (file.type === 'application/pdf') {
      const fileURL = URL.createObjectURL(file);
      this.filePreviewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileURL);
    } else {
      this.filePreviewUrl = null;
    }
  }

  uploadFile(): void {
    if (!this.selectedFile) return;

    this.isUploading = true;
    this.uploadProgress = 0;
    this.uploadMessage = '';

    // Log the file details for debugging
    console.log('Uploading file:', {
      name: this.selectedFile.name,
      type: this.selectedFile.type,
      size: this.selectedFile.size,
      extension: this.selectedFile.name.split('.').pop()?.toLowerCase()
    });

    const subscription = this.fileUploadService.uploadFile(this.selectedFile).subscribe({
      next: (event: any) => {
        if (event.type === HttpEventType.UploadProgress) {
          this.uploadProgress = Math.round(100 * (event.loaded / (event.total || event.loaded)));
          console.log(`Upload progress: ${this.uploadProgress}%`);
        } else if (event.type === HttpEventType.Response) {
          const response = event.body as any;
          console.log('Upload response:', response);

          if (response && response.success) {
            this.uploadMessage = 'File uploaded successfully';

            // Check if response has the expected structure
            if (response.file && response.file.name) {
              this.currentFileName = response.file.name; // Store the actual filename from server

              // Handle extracted data if available
              if (response.file.extractedData && response.file.extractedData.length > 0) {
                // Ensure all required fields are present
                this.extractedData = response.file.extractedData.map((item: any) => ({
                  key: item.key || '',
                  value: item.value || '',
                  original: item.original || ''
                }));
                console.log('Extracted data from upload:', this.extractedData);

                // Always show the extracted data form after successful upload
                this.showExtractedData = true;
              } else {
                // If no data was extracted, try to get it explicitly
                this.getExtractedData(response.file.name);
              }
            } else {
              console.warn('Response missing file information:', response);

              // If no data was extracted, leave the form empty
              if (!this.extractedData || this.extractedData.length === 0) {
                this.extractedData = [];
                this.showExtractedData = false;
                console.warn('No data extracted from uploaded file');
              }
            }

            this.loadFiles();



            this.selectedFile = null;
            this.fileInput.nativeElement.value = '';
          } else {
            // Handle unsuccessful response
            this.uploadMessage = response && response.message ? response.message : 'Upload failed';
            console.error('Upload failed:', response);

            // Check for specific error details
            if (response && response.details) {
              console.error('Error details:', response.details);

              // If we have mime type information, log it
              if (response.details.detected_mime) {
                console.error(`Server detected MIME type: ${response.details.detected_mime}, extension: ${response.details.extension}`);
              }
            }
          }
        }
      },
      error: (error: any) => {
        console.error('Upload error:', error);

        // Log detailed error information
        if (error.error) {
          console.error('Error details:', error.error);
        }

        // Provide more detailed error information
        let errorMessage = 'Failed to upload file';

        if (error.error && error.error.message) {
          errorMessage += ': ' + error.error.message;

          // Check for detailed error information
          if (error.error.details) {
            console.error('Server error details:', error.error.details);
          }
        } else if (error.status === 400) {
          errorMessage += ': Format de fichier non supporté ou fichier invalide';
        } else if (error.status === 413) {
          errorMessage += ': Le fichier est trop volumineux';
        } else if (error.status === 415) {
          errorMessage += ': Format de fichier non supporté';
        } else if (error.status === 0) {
          errorMessage += ': Erreur réseau - Vérifiez votre connexion internet';
        } else {
          errorMessage += ': ' + (error.message || error.statusText || 'Erreur inconnue');
        }

        this.uploadMessage = errorMessage;
        this.isUploading = false;
      },
      complete: () => {
        this.isUploading = false;
        console.log('Upload operation completed');
      }
    });

    // Track subscription for cleanup
    this.subscriptions.push(subscription);
  }

  loadFiles(): void {
    this.isLoadingFiles = true;
    const subscription = this.fileUploadService.getFiles().subscribe({
      next: (response: any) => {
        if (response.success) {
          this.fileList = response.files.map((file: any) => file.filename);
          this.filterFiles(); // Apply any active filters
        }
      },
      error: (error: any) => {
        console.error('Failed to load files:', error);
      },
      complete: () => {
        this.isLoadingFiles = false;
      }
    });

    // Track subscription for cleanup
    this.subscriptions.push(subscription);
  }

  // File utility methods
  getFileType(file: File): string {
    return file.name.toLowerCase().endsWith('.pdf') ? 'PDF' : 'Word';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  removeSelectedFile(event: Event): void {
    event.stopPropagation(); // Prevent click from propagating to parent
    this.selectedFile = null;
    this.filePreviewUrl = null;
    this.fileInput.nativeElement.value = '';
    this.uploadProgress = 0;
    this.uploadMessage = '';
  }

  // Search and filter methods
  filterFiles(): void {
    if (!this.fileList.length) {
      this.filteredFiles = [];
      return;
    }

    let result = [...this.fileList];

    // Apply search term filter
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      result = result.filter(file => file.toLowerCase().includes(term));
    }

    // Apply file type filter
    if (this.fileTypeFilter !== 'all') {
      if (this.fileTypeFilter === 'pdf') {
        result = result.filter(file => file.toLowerCase().endsWith('.pdf'));
      } else if (this.fileTypeFilter === 'word') {
        result = result.filter(file =>
          file.toLowerCase().endsWith('.doc') || file.toLowerCase().endsWith('.docx')
        );
      }
    }

    this.filteredFiles = result;
  }

  setFileTypeFilter(filter: 'all' | 'pdf' | 'word'): void {
    this.fileTypeFilter = filter;
    this.filterFiles();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.fileTypeFilter = 'all';
    this.filterFiles();
  }

  // File actions
  downloadFile(filename: string): void {
    console.log('Downloading file:', filename);
    // Use the direct API endpoint for downloading
    const url = `${environment.apiUrl}/api/files/download/${filename}`;
    console.log('Download URL:', url);
    window.open(url, '_blank');
  }

  getDownloadUrl(filename: string): string {
    return `${environment.apiUrl}/api/files/download/${filename}`;
  }

  deleteFile(filename: string): void {
    console.log('Deleting file:', filename);
    if (confirm(`Êtes-vous sûr de vouloir supprimer le fichier "${filename}"?`)) {
      const subscription = this.fileUploadService.deleteFile(filename).subscribe({
        next: (response: any) => {
          console.log('Delete response:', response);
          if (response.success) {
            this.uploadMessage = 'Fichier supprimé avec succès';
            this.loadFiles();
          } else {
            this.uploadMessage = 'Échec de la suppression du fichier: ' + (response.message || 'Unknown error');
            console.error('Failed to delete file:', response);
          }
        },
        error: (error: any) => {
          console.error('Failed to delete file:', error);
          this.uploadMessage = 'Échec de la suppression du fichier: ' +
            (error.error?.message || error.message || 'Unknown error');
        }
      });

      // Track subscription for cleanup
      this.subscriptions.push(subscription);
    }
  }

  deleteAllFiles(): void {
    if (confirm('Are you sure you want to delete all files?')) {
      const subscription = this.fileUploadService.deleteAllFiles().subscribe({
        next: (response: any) => {
          if (response.success) {
            this.fileList = [];
            this.uploadMessage = 'All files deleted successfully';
          }
        },
        error: (error: any) => {
          console.error('Failed to delete files:', error);
          this.uploadMessage = 'Failed to delete files';
        }
      });

      // Track subscription for cleanup
      this.subscriptions.push(subscription);
    }
  }

  // Methods for handling extracted data and generating files
  getExtractedData(filename: string, isPartnerContract: boolean = false): void {
    if (!filename) {
      console.error('Cannot extract data: Filename is missing');
      return;
    }

    console.log('Getting extracted data for file:', filename, isPartnerContract ? '(partner contract)' : '(user upload)');

    // Check if this is a contract file (not uploaded by the user)
    const isContractFile = isPartnerContract || (this.selectedContractId !== null && !filename.includes('_'));

    const subscription = this.fileUploadService.getExtractedData(filename, isPartnerContract).subscribe({
      next: (response: any) => {
        console.log('Extracted data response:', response);

        if (response && response.success && response.data) {
          // Ensure all required fields are present
          this.extractedData = response.data.map((item: any) => ({
            key: item.key || '',
            value: item.value || '',
            original: item.original || ''
          }));
          console.log('Processed extracted data:', this.extractedData);

          // Always show the extracted data form after successful extraction
          if (this.extractedData.length > 0) {
            this.showExtractedData = true;
          }
        } else {
          console.warn('No data extracted from file or invalid response format');
          this.extractedData = [];
          this.showExtractedData = false;
        }
      },
      error: (error: any) => {
        console.error('Failed to get extracted data:', error);
        this.extractedData = [];
        this.showExtractedData = false;

        // If this is a 404 or 405 error, the file might not be in the user's uploads
        if (error.status === 404 || error.status === 405) {
          console.warn('File not found in user uploads. This might be a contract file.');
        }
      },
      complete: () => {
        console.log('Extraction process completed');
      }
    });

    // Track subscription for cleanup
    this.subscriptions.push(subscription);
  }

  /**
   * Get extracted data from a contract by contract ID
   */
  getContractExtractedData(contractId: number): void {
    if (!contractId) {
      console.error('Cannot extract data: Contract ID is missing');
      return;
    }

    console.log('Getting extracted data for contract ID:', contractId);

    const subscription = this.fileUploadService.getContractExtractedData(contractId).subscribe({
      next: (response: any) => {
        console.log('Contract extracted data response:', response);

        if (response && response.success && response.data) {
          // Ensure all required fields are present
          this.extractedData = response.data.map((item: any) => ({
            key: item.key || '',
            value: item.value || '',
            original: item.original || ''
          }));
          console.log('Processed contract extracted data:', this.extractedData);

          // Always show the extracted data form after successful extraction
          if (this.extractedData.length > 0) {
            this.showExtractedData = true;
          } else {
            console.warn('No fields found in contract document');
            this.showExtractedData = false;
          }
        } else {
          console.warn('No data extracted from contract or invalid response format');
          this.extractedData = [];
          this.showExtractedData = false;
        }
      },
      error: (error: any) => {
        console.error('Failed to get contract extracted data:', error);
        this.extractedData = [];
        this.showExtractedData = false;

        if (error.status === 404) {
          console.warn('Contract not found or contract file not found.');
        } else if (error.status === 500) {
          console.warn('Server error during contract data extraction.');
        }
      },
      complete: () => {
        console.log('Contract extraction process completed');
      }
    });

    // Track subscription for cleanup
    this.subscriptions.push(subscription);
  }

  /**
   * Generate a document with placeholders replaced by values
   */
  generateDocument(): void {
    if (!this.currentFileName) {
      console.error('No file selected');
      this.generationError = 'No file selected';
      return;
    }

    // If we have a selected contract but no extracted data, we can still proceed
    // This allows processing of contracts that don't have placeholders
    if (this.selectedContractId && this.extractedData.length === 0) {
      console.log('Processing contract without extracted data');
      // Continue with empty replacements
    } else if (!this.selectedContractId && this.extractedData.length === 0) {
      console.error('No data to replace');
      this.generationError = 'No data to replace';
      return;
    }

    this.isGenerating = true;
    this.generatedDocumentUrl = null;
    this.generationError = null;
    this.generatedDocumentId = null;

    // Prepare the replacements object
    const replacements: {[key: string]: string} = {};
    this.extractedData.forEach(field => {
      replacements[field.key] = field.value;
    });

    console.log('Generating document with replacements:', replacements);
    console.log('Original filename:', this.currentFileName);

    // Create a document name based on the original filename
    const documentName = `Generated_${this.currentFileName.split('.')[0]}_${new Date().toISOString().slice(0, 10)}`;

    // Get the file path
    let filePath = '';

    // Determine if we're using a partner contract or an uploaded file
    const isPartnerContract = this.selectedContractId !== null && this.selectedContractFilePath !== null;

    if (isPartnerContract && this.selectedContractFilePath) {
      filePath = this.selectedContractFilePath;
      console.log('Using partner contract file path:', filePath);
    } else {
      // For regular uploads, use the uploads directory
      filePath = `uploads/${this.currentFileName}`;
      console.log('Using uploaded file path:', filePath);
    }

    // Use the file path for document generation
    const filePathOrName = filePath;

    console.log('Document generation details:', {
      isPartnerContract,
      filePathOrName,
      extractedDataCount: this.extractedData.length,
      selectedContractId: this.selectedContractId,
      selectedContractFilePath: this.selectedContractFilePath
    });

    // Try using the fileUploadService first (old method)
    const subscription = this.fileUploadService.generateDocument(
      filePathOrName,
      this.extractedData,
      isPartnerContract
    ).subscribe({
        next: (response: any) => {
          console.log('Document generated successfully with fileUploadService', response);
          this.uploadMessage = 'Document generated successfully';
          this.isGenerating = false;

          // Store the document ID for signing
          if (response.file && response.file.unsigned_contract_id) {
            this.generatedDocumentId = response.file.unsigned_contract_id;
            this.generatedUnsignedContractId = response.file.unsigned_contract_id;
            console.log('Generated document ID:', this.generatedDocumentId);
          } else {
            console.warn('No document ID in response:', response);
          }

          // Set the download URL
          if (response.file && response.file.download_url) {
            this.generatedDocumentUrl = response.file.download_url;
          }
        },
        error: (error: any) => {
          console.error('Error generating document with fileUploadService:', error);

          // Fallback to documentGenerationService if the first method fails
          console.log('Falling back to documentGenerationService...');

          // Use the correct file path based on whether it's a partner contract or user upload
          const correctFilePath = isPartnerContract ? filePathOrName : filePath;

          console.log('Falling back to documentGenerationService with path:', correctFilePath);
          console.log('Using document name:', documentName);
          console.log('Replacements:', replacements);

          const fallbackSubscription = this.documentGenerationService.generateDocument(
            correctFilePath,
            replacements,
            documentName
          ).subscribe({
              next: (response: { blob: Blob, documentId: number | null }) => {
                console.log('Document generated successfully with documentGenerationService', response);
                this.uploadMessage = 'Document generated successfully';
                this.isGenerating = false;

                // Store the document ID for signing
                this.generatedDocumentId = response.documentId;
                console.log('Generated document ID:', this.generatedDocumentId);

                // For backward compatibility with the existing code
                this.generatedDocumentUrl = URL.createObjectURL(response.blob);
                this.generatedUnsignedContractId = response.documentId;
              },
              error: (fallbackError: any) => {
                console.error('Error generating document with both services:', fallbackError);

                // Provide more detailed error information
                let errorMessage = 'Error generating document';

                if (fallbackError.error && fallbackError.error.message) {
                  errorMessage += ': ' + fallbackError.error.message;
                } else if (fallbackError.status === 404) {
                  errorMessage += ': Original file not found';
                } else if (fallbackError.status === 422) {
                  errorMessage += ': Invalid data provided';
                } else if (fallbackError.status === 500) {
                  errorMessage += ': Server error during document generation';
                } else {
                  errorMessage += ': ' + (fallbackError.message || fallbackError.statusText || 'Unknown error');
                }

                this.generationError = errorMessage;
                this.isGenerating = false;
              }
          });

          // Track subscription for cleanup
          this.subscriptions.push(fallbackSubscription);
        }
    });

    // Track subscription for cleanup
    this.subscriptions.push(subscription);
  }


  ngOnInit(): void {
    // Initialize filteredFiles
    this.filteredFiles = [...this.fileList];

    // Get partner and contract IDs from query parameters
    this.route.queryParams.subscribe((params: Params) => {
      if (params['partnerId']) {
        this.selectedPartnerId = +params['partnerId'];

        // If contract ID is provided, set it and set loading state
        if (params['contractId']) {
          this.selectedContractId = +params['contractId'];
          this.isLoadingContract = true; // Set loading state to true
          console.log('Contract ID provided in query params, loading contract:', this.selectedContractId);

          // Add a small delay to ensure the component is fully initialized
          setTimeout(() => {
            if (this.selectedContractId !== null) {
              this.loadContractDetails(this.selectedContractId);
              // Show the extracted data form immediately for partner contracts
              this.showExtractedData = true;
            }
          }, 100);
        }

        // Load partner details
        this.loadPartnerDetails(this.selectedPartnerId);
      } else {
        // If no partner ID is provided, allow direct file upload
        console.log('No partner ID provided, allowing direct file upload');
        this.uploadMessage = 'Please select a file to upload.';

        // Initialize empty extracted data - will be populated when file is uploaded and processed
        this.extractedData = [];
        this.showExtractedData = false;
      }
    });

    // Load user files
    requestAnimationFrame(() => {
      this.loadFiles();
    });

    // Add event listener for storage changes (in case the contract data is updated in another tab)
    window.addEventListener('storage', this.handleStorageChange.bind(this));
  }

  /**
   * Handle storage changes (e.g., if contract data is updated in another tab)
   */
  private handleStorageChange(event: StorageEvent): void {
    if (event.key === 'selectedFile' && this.selectedContractId !== null && !this.selectedFile) {
      console.log('Storage change detected, reloading contract:', this.selectedContractId);
      this.loadContractDetails(this.selectedContractId);
    }
  }

  /**
   * Load details for a specific partner
   * @param partnerId The ID of the partner
   */
  loadPartnerDetails(partnerId: number): void {
    const subscription = this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.success) {
          const partner = response.partners.find(p => p.id === partnerId);
          if (partner) {
            this.partners = [partner];
          } else {
            console.error('Partner not found');
            this.router.navigate(['/partner-contracts']);
          }
        }
      },
      error: (error) => {
        console.error('Error loading partner details:', error);
        this.router.navigate(['/partner-contracts']);
      }
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Load details for a specific contract
   * @param contractId The ID of the contract
   */
  loadContractDetails(contractId: number): void {
    // Set loading state to true
    this.isLoadingContract = true;
    console.log('Loading contract details for ID:', contractId);

    // Create a default contract object in case the API call fails
    const defaultContract = {
      id: contractId,
      name: `Contract ${contractId}`,
      filename: `contract_${contractId}.docx`,
      file_path: null
    };

    try {
      const subscription = this.partnerService.getContract(contractId).subscribe({
        next: (contract) => {
          console.log('Contract data received:', contract);

          if (contract) {
            try {
              this.contracts = [contract];

              // Set the contract file path with proper format
              if (contract.file_path) {
                // Ensure the file_path starts with 'contracts/' for partner contracts
                if (!contract.file_path.startsWith('contracts/')) {
                  this.selectedContractFilePath = 'contracts/' + contract.file_path.split('/').pop();
                } else {
                  this.selectedContractFilePath = contract.file_path;
                }
              } else {
                // If no file_path is provided, create a default one based on the filename
                if (contract.filename) {
                  this.selectedContractFilePath = 'contracts/' + contract.filename;
                } else {
                  this.selectedContractFilePath = `contracts/contract_${contractId}.docx`;
                }
              }

              console.log('Set contract file path to:', this.selectedContractFilePath);

              // Validate contract data and set defaults for missing properties
              if (!contract.name) {
                console.warn('Contract name is missing, using default');
                contract.name = `Contract ${contractId}`;
              }

              if (!contract.filename) {
                console.warn('Contract filename is missing, using default');
                contract.filename = `contract_${contractId}.docx`;
              }

              // Create a preview if possible
              this.createFilePreview(contract);

              // Simulate file selection with contract information
              this.simulateFileFromContract(contract);

              // Reset upload progress and uploading state
              this.uploadProgress = 0;
              this.isUploading = false;

              // Set the current filename for document generation
              this.currentFileName = contract.filename;

              // Extract data from the contract file using the contract ID
              if (contract.id) {
                console.log('Extracting data from contract ID:', contract.id);
                this.getContractExtractedData(contract.id);
              } else if (contract.filename) {
                console.log('Extracting data from contract file:', contract.filename);
                // Fallback to filename-based extraction
                this.getExtractedData(contract.filename, true);
              } else {
                console.warn('Contract has no ID or filename, cannot extract data');
                this.extractedData = [];
                this.showExtractedData = false;
              }

              // Always show the extracted data form for partner contracts
              this.showExtractedData = true;

              // Set loading state to false after everything is loaded
              setTimeout(() => {
                this.isLoadingContract = false;
                console.log('Contract loading complete, selectedFile:', !!this.selectedFile);

                // Force UI update if needed
                if (!this.selectedFile) {
                  console.warn('Selected file is still null after loading, creating fallback file');
                  this.createFallbackFile(contract);
                }
              }, 1000); // Increased delay to ensure UI updates
            } catch (error) {
              console.error('Error processing contract data:', error);
              this.isLoadingContract = false;
              this.createFallbackFile(defaultContract);
            }
          } else {
            console.error('Contract data is null or undefined');
            this.isLoadingContract = false;
            this.createFallbackFile(defaultContract);
          }
        },
        error: (error) => {
          console.error('Error loading contract details:', error);
          this.isLoadingContract = false; // Make sure to set loading to false on error
          this.createFallbackFile(defaultContract);
        }
      });

      this.subscriptions.push(subscription);
    } catch (error) {
      console.error('Exception during contract loading:', error);
      this.isLoadingContract = false;
      this.createFallbackFile(defaultContract);
    }
  }

  /**
   * Creates a fallback file when contract data is incomplete or missing
   * @param contract Partial contract data
   */
  private createFallbackFile(contract: any): void {
    console.log('Creating fallback file for contract:', contract);

    if (!contract) {
      console.error('Cannot create fallback file: contract is null or undefined');
      return;
    }

    try {
      const filename = contract.filename || `contract_${contract.id || 'unknown'}.docx`;
      const mimeType = filename.toLowerCase().endsWith('.pdf') ? 'application/pdf' : 'application/msword';

      // Create a minimal File object
      const fileContent = new Blob([`Contract ${contract.id || 'unknown'}: ${contract.name || 'Unnamed'}`], { type: mimeType });
      const file = new File([fileContent], filename, { type: mimeType });

      // Set as selected file
      this.selectedFile = file;
      this.currentFileName = filename;

      // Ensure we have a valid file path for document generation
      if (!this.selectedContractFilePath) {
        this.selectedContractFilePath = 'contracts/' + filename;
        console.log('Created default contract file path for fallback file:', this.selectedContractFilePath);
      }

      // Add to file list if not already there
      if (!this.fileList.includes(file.name)) {
        this.fileList.push(file.name);
        this.filterFiles();
      }

      // Reset upload states
      this.uploadProgress = 0;
      this.isUploading = false;
      this.isLoadingContract = false;

      console.log('Fallback file created:', file.name, 'with path:', this.selectedContractFilePath);
    } catch (error) {
      console.error('Error creating fallback file:', error);
      this.isLoadingContract = false;
    }
  }

  /**
   * Create a file preview for the selected contract
   * @param contract The contract to create a preview for
   */
  private createFilePreview(contract: any): void {
    try {
      if (!contract) {
        console.warn('Cannot create file preview: Contract is missing');
        this.filePreviewUrl = null;
        return;
      }

      if (!contract.filename) {
        console.warn('Cannot create file preview: Filename is missing');
        this.filePreviewUrl = null;
        return;
      }

      const extension = contract.filename.split('.').pop()?.toLowerCase() || '';

      // For PDF files, we can create a preview URL if the file_path is available
      if (extension === 'pdf' && contract.file_path) {
        const fileURL = `${environment.apiUrl}/api/files/view/${contract.filename}`;
        this.filePreviewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileURL);
        console.log('Created PDF preview URL:', fileURL);
      } else if (extension === 'pdf') {
        // For PDF files without a file_path, try to create a preview URL anyway
        const fileURL = `${environment.apiUrl}/api/files/view/${contract.filename}`;
        this.filePreviewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileURL);
        console.log('Created PDF preview URL (without file_path):', fileURL);
      } else {
        // For other file types, we can't create a preview
        console.log('No preview available for file type:', extension);
        this.filePreviewUrl = null;
      }
    } catch (error) {
      console.error('Error creating file preview:', error);
      this.filePreviewUrl = null;
    }
  }

  /**
   * Load partners from the API - Not used anymore, replaced by loadPartnerDetails
   * Kept for backward compatibility
   */
  loadPartners(): void {
    // This method is no longer used directly
    console.warn('loadPartners() is deprecated, use loadPartnerDetails() instead');
  }

  /**
   * Handle partner selection
   * @param partnerId The ID of the selected partner
   */
  onPartnerSelected(partnerId: number): void {
    this.selectedPartnerId = partnerId;
    this.selectedContractId = null;

    // Clear any previous upload state
    this.selectedFile = null;
    this.uploadMessage = '';
    this.uploadProgress = 0;
    this.currentFileName = '';
    this.generatedDocumentUrl = null;

    // Find the selected partner for display
    const selectedPartner = this.partners.find(p => p.id === partnerId);
    if (selectedPartner) {
      this.uploadMessage = `Ready to upload documents for ${selectedPartner.name}. Please select a file to upload.`;
    }

    this.loadPartnerContracts(partnerId);
  }

  /**
   * Navigate back to partner contracts
   */
  navigateToPartnerContracts(): void {
    this.router.navigate(['/partner-contracts']);
  }

  /**
   * Get the name of the currently selected partner
   */
  getSelectedPartnerName(): string {
    if (!this.selectedPartnerId) return '';
    const partner = this.partners.find(p => p.id === this.selectedPartnerId);
    return partner ? partner.name : '';
  }

  /**
   * Load contracts for a specific partner
   * @param partnerId The ID of the partner
   */
  loadPartnerContracts(partnerId: number): void {
    this.isLoadingContracts = true;
    const subscription = this.partnerService.getPartnerContracts(partnerId).subscribe({
      next: (response) => {
        if (response.success && response.contracts) {
          this.contracts = response.contracts;
        } else {
          this.contracts = [];
          console.error('Failed to load contracts: Invalid response format');
        }
      },
      error: (error) => {
        this.contracts = [];
        console.error('Error loading contracts:', error);
      },
      complete: () => {
        this.isLoadingContracts = false;
      }
    });

    this.subscriptions.push(subscription);
  }

  // Store the selected contract file path for generation
  private selectedContractFilePath: string | null = null;

  /**
   * Handle contract selection - automatically prepares the contract for generation
   * @param contractId The ID of the selected contract
   */
  onContractSelected(contractId: number): void {
    this.selectedContractId = contractId;
    this.isLoadingContract = true; // Set loading state to true
    // We want to show the extracted data form for partner contracts

    // Find the selected contract
    const selectedContract = this.contracts.find(contract => contract.id === contractId);

    if (selectedContract) {
      console.log('Selected contract:', selectedContract);

      // Set current filename to the contract filename for document generation
      this.currentFileName = selectedContract.filename || '';

      // Store the file_path for later use in document generation
      // Make sure it's a valid path that the backend can locate
      if (selectedContract.file_path) {
        // Ensure the file_path starts with 'contracts/' for partner contracts
        if (!selectedContract.file_path.startsWith('contracts/')) {
          this.selectedContractFilePath = 'contracts/' + selectedContract.file_path.split('/').pop();
        } else {
          this.selectedContractFilePath = selectedContract.file_path;
        }
      } else {
        // If no file_path is provided, create a default one based on the filename
        if (selectedContract.filename) {
          this.selectedContractFilePath = 'contracts/' + selectedContract.filename;
        } else {
          this.selectedContractFilePath = `contracts/contract_${contractId}.docx`;
        }
      }
      console.log('Contract file path:', this.selectedContractFilePath);

      // Simulate a file selection with contract information
      this.simulateFileFromContract(selectedContract);

      // Reset upload progress and uploading state
      this.uploadProgress = 0;
      this.isUploading = false;

      // Extract data from the contract file using the contract ID
      if (selectedContract.id) {
        console.log('Extracting data from contract ID:', selectedContract.id);
        this.getContractExtractedData(selectedContract.id);
      } else if (selectedContract.filename) {
        console.log('Extracting data from contract file:', selectedContract.filename);
        // Fallback to filename-based extraction
        this.getExtractedData(selectedContract.filename, true);
      } else {
        console.warn('Contract has no ID or filename, cannot extract data');
        this.extractedData = [];
        this.showExtractedData = false;
      }

      // Always show the extracted data form for partner contracts
      this.showExtractedData = true;

      // Set loading state to false after everything is loaded
      setTimeout(() => {
        this.isLoadingContract = false;
        console.log('Contract loading complete in onContractSelected, selectedFile:', !!this.selectedFile);
      }, 500); // Small delay to ensure UI updates
    } else {
      this.isLoadingContract = false; // Make sure to set loading to false if contract not found
    }
  }

  /**
   * Creates a simulated file object from contract metadata
   * @param contract The contract to simulate a file from
   */
  private simulateFileFromContract(contract: any): void {
    if (!contract) {
      console.error('Cannot simulate file: Contract is undefined or null');
      this.isLoadingContract = false;
      return;
    }

    try {
      // Ensure contract has required properties
      if (!contract.id) contract.id = Math.floor(Math.random() * 10000);
      if (!contract.name) contract.name = `Contract ${contract.id}`;

      if (!contract.filename) {
        console.warn('Contract filename is missing, creating default', contract);
        // Create a default filename if missing
        contract.filename = `contract_${contract.id || 'unknown'}.docx`;
      }

      // Determine file type based on extension
      const extension = contract.filename.split('.').pop()?.toLowerCase() || '';
      let mimeType = 'application/octet-stream'; // default

      if (extension === 'pdf') {
        mimeType = 'application/pdf';
      } else if (extension === 'doc' || extension === 'docx') {
        mimeType = 'application/msword';
      }

      // Create a minimal File object with contract information
      // Note: This doesn't contain actual file content, just metadata
      const fileContent = new Blob([`Contract ${contract.id || 'unknown'}: ${contract.name || 'Unnamed'}`], { type: mimeType });
      const file = new File([fileContent], contract.filename, { type: mimeType });

      // Set as selected file for the upload section
      this.selectedFile = file;

      // Set current filename for document generation
      this.currentFileName = contract.filename;

      // Add to the file list if not already there
      if (!this.fileList.includes(file.name)) {
        this.fileList.push(file.name);
        this.filterFiles();
      }

      // Ensure we have a valid file path for document generation
      if (!this.selectedContractFilePath) {
        this.selectedContractFilePath = 'contracts/' + contract.filename;
        console.log('Created default contract file path:', this.selectedContractFilePath);
      }

      console.log('File simulated from contract:', {
        name: file.name,
        type: file.type,
        size: file.size,
        contract: contract.name || 'Unnamed',
        filePath: this.selectedContractFilePath
      });
    } catch (error) {
      console.error('Error simulating file from contract:', error, contract);
      this.isLoadingContract = false;
      // Create a fallback file as a last resort
      this.createFallbackFile({
        id: contract.id || Math.floor(Math.random() * 10000),
        name: contract.name || 'Unnamed Contract',
        filename: contract.filename || `contract_${Date.now()}.docx`
      });
    }
  }



  /**
   * Navigate to the signing page with the generated document ID
   */
  navigateToSignDocument(): void {
    // Use either generatedDocumentId or generatedUnsignedContractId
    const documentId = this.generatedDocumentId || this.generatedUnsignedContractId;

    if (!documentId) {
      console.error('No document ID available for signing');
      this.uploadMessage = 'Cannot sign document: No document ID available';
      return;
    }

    // Check if user is authenticated
    if (!this.authService.isLoggedIn()) {
      console.error('User is not authenticated, cannot navigate to sign document');
      this.uploadMessage = 'Please log in to sign your document';

      // Get the auth token for debugging
      const token = this.authService.getAuthToken();
      console.log('Auth token available:', !!token, 'Length:', token ? token.length : 0);

      // Redirect to signin page with return URL
      this.router.navigate(['/signin'], {
        queryParams: {
          returnUrl: `/security`,
          id: documentId,
          message: 'Please log in to sign your document'
        } as Params
      });
      return;
    }

    console.log('Navigating to sign document with ID:', documentId);
    console.log('User is authenticated, token available');

    // IMPORTANT: Use a special parameter to indicate we want to view the file, not download it
    this.router.navigate(['/security'], {
      queryParams: {
        id: documentId,
        view: 'true'  // Add this parameter to indicate we want to view the file
      } as Params
    });
  }

  ngOnDestroy(): void {
    // Clean up all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());

    // Clean up any object URLs to prevent memory leaks
    if (this.filePreviewUrl) {
      URL.revokeObjectURL(this.filePreviewUrl.toString());
    }

    // Remove storage event listener
    window.removeEventListener('storage', this.handleStorageChange.bind(this));
  }
}
