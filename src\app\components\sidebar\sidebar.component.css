/* Sidebar Styles */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 80px;
  background: rgba(var(--elevation3-rgb, 245, 245, 245), 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 99;
  overflow-x: hidden;
  border-right: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.08);
  box-shadow: 5px 0 30px -15px rgba(0, 0, 0, 0.1);
}

.sidebar.expanded {
  width: 280px;
}

/* Header */
.sidebar-header {
  height: 70px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid rgba(var(--border-rgb, 0, 0, 0), 0.08);
  cursor: pointer;
  position: relative;
  background: rgba(var(--elevation2-rgb, 240, 240, 240), 0.5);
}

.logo {
  color: var(--text);
  display: flex;
  align-items: center;
  text-decoration: none;
  width: 100%;
  position: relative;
  z-index: 1;
}

.logo img {
  width: 38px;
  height: 38px;
  min-width: 38px;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.1));
}

.dark-theme .logo img {
  filter: brightness(0) invert(1) drop-shadow(0 2px 5px rgba(255, 255, 255, 0.1));
}

.logo span {
  margin-left: 15px;
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
  display: none;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.logo span::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary);
}

.sidebar.expanded .logo span {
  display: block;
}

/* Navigation */
.sidebar-nav {
  padding: 15px 10px;
  height: calc(100% - 70px);
  overflow-y: auto;
}

.sidebar-nav ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.sidebar-nav li {
  position: relative;
  width: 100%;
  margin: 8px 0;
}

.sidebar-nav li a {
  height: 56px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--textSecondary);
  padding: 0;
  white-space: nowrap;
  border-radius: 16px;
  position: relative;
}

.sidebar-nav li i {
  min-width: 80px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  position: relative;
  z-index: 1;
}

.sidebar.expanded .sidebar-nav li i {
  min-width: 56px;
  font-size: 24px;
  margin-left: 12px;
}

.sidebar-nav li span {
  display: none;
  padding-left: 8px;
  font-size: 15px;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.sidebar.expanded .sidebar-nav li span {
  display: block;
}

/* Remove hover effect to prevent glitching */

.sidebar-nav li.active a {
  color: white;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  box-shadow: 0 5px 15px -5px rgba(var(--primary-rgb, 78, 205, 196), 0.4);
}

/* Toggle indicator */
.sidebar-toggle {
  position: absolute;
  right: 0;
  top: 0;
  width: 4px;
  height: 70px;
  background: rgba(var(--border-rgb, 0, 0, 0), 0.1);
  overflow: hidden;
}

/* Scrollbar */
.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
  margin: 10px 0;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(var(--overlay-rgb, 100, 100, 100), 0.2);
  border-radius: 10px;
}

/* Category Labels */
.nav-category {
  padding: 0 20px;
  margin: 20px 0 10px;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--textSecondary);
  display: none;
  font-weight: 600;
}

.sidebar.expanded .nav-category {
  display: block;
}

/* Responsive */
@media (max-width: 991.98px) {
  .sidebar {
    left: -80px;
    box-shadow: none;
  }

  .sidebar.expanded {
    left: 0;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.2);
  }

  /* Overlay when sidebar is expanded on mobile */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 98;
    display: none;
  }

  .sidebar-overlay.show {
    display: block;
  }
}

/* Dark Theme Enhancements */
.dark-theme .sidebar {
  background: rgba(var(--elevation3-rgb, 25, 25, 25), 0.95);
  box-shadow: 5px 0 30px -15px rgba(0, 0, 0, 0.3);
}

.dark-theme .sidebar-header {
  background: rgba(var(--elevation2-rgb, 35, 35, 35), 0.5);
}

/* Remove hover effect in dark theme to prevent glitching */

.dark-theme .sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(var(--overlay-rgb, 150, 150, 150), 0.2);
}

.dark-theme .sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--primary-rgb, 78, 205, 196), 0.4);
}

/* Error Message */
.auth-error {
  background-color: var(--error-light);
  color: var(--error);
  padding: 10px;
  margin: 10px;
  border-radius: 4px;
  font-size: 14px;
}

/* Non-clickable menu item styling */
.non-clickable-item {
  opacity: 0.7;
  cursor: default;
}

.nav-item-display {
  height: 56px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--textSecondary);
  padding: 0;
  white-space: nowrap;
  border-radius: 16px;
  position: relative;
  pointer-events: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.nav-item-display i {
  min-width: 80px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  position: relative;
  z-index: 1;
}

.sidebar.expanded .nav-item-display i {
  min-width: 56px;
  font-size: 24px;
  margin-left: 12px;
}

.nav-item-display span {
  display: none;
  padding-left: 8px;
  font-size: 15px;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.sidebar.expanded .nav-item-display span {
  display: block;
}

/* Active state for Electronic Signature when on security page */
.non-clickable-item.active {
  opacity: 1;
}

.non-clickable-item.active .nav-item-display {
  color: white;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  box-shadow: 0 5px 15px -5px rgba(var(--primary-rgb, 78, 205, 196), 0.4);
}
