import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';

import { AuthService } from '../../services/auth.service';
import { ThemeService } from '../../services/theme.service';

import { Subject, takeUntil, Observable } from 'rxjs';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule
  ]
})
export class NavbarComponent implements OnInit, OnDestroy {
  isDarkMode = false;
  searchQuery = '';
  isProfileMenuOpen = false;
  isLoggedIn$: Observable<boolean>;

  private clickListener: ((event: MouseEvent) => void) | null = null;
  private destroy$ = new Subject<void>();

  constructor(
    private authService: AuthService,
    private themeService: ThemeService,
    private cdr: ChangeDetectorRef
  ) {
    this.isLoggedIn$ = this.authService.authState$;
  }

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isDark) => {
        this.isDarkMode = isDark;
        this.cdr.markForCheck();
      });

    // Subscribe to auth state changes
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isLoggedIn => {
        console.log('Auth state changed:', isLoggedIn);
        // Force change detection to update the UI
        this.cdr.markForCheck();
      });

    // Listen for custom auth state change events (for Google sign-in)
    if (typeof window !== 'undefined') {
      window.addEventListener('auth-state-changed', (event: any) => {
        console.log('Auth state change event received:', event.detail);
        // Force change detection to update the UI
        this.cdr.markForCheck();
      });
    }

    // Add click listener to close dropdown when clicking outside
    if (typeof document !== 'undefined') {
      this.clickListener = (event: MouseEvent) => {
        const target = event.target as Element;
        if (!target?.closest('.dropdown')) {
          this.isProfileMenuOpen = false;
          this.cdr.markForCheck();
        }
      };
      document.addEventListener('click', this.clickListener);
    }
  }

  ngOnDestroy(): void {
    if (this.clickListener && typeof document !== 'undefined') {
      document.removeEventListener('click', this.clickListener);
    }

    // Remove the auth state change event listener
    if (typeof window !== 'undefined') {
      window.removeEventListener('auth-state-changed', () => {});
    }

    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleProfileMenu(event: Event): void {
    event.stopPropagation();
    this.isProfileMenuOpen = !this.isProfileMenuOpen;
  }

  logout(): void {
    this.isProfileMenuOpen = false;
    this.authService.logout();

    // Force navigation to home page after logout
    setTimeout(() => {
      window.location.href = '/';
    }, 100);
  }



  toggleDarkMode(): void {
    this.themeService.toggleTheme();
  }


}

