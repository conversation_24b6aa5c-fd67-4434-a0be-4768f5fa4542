import { mergeApplicationConfig, ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideServerRendering } from '@angular/platform-server';
import { provideServerRouting } from '@angular/ssr';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { appConfig } from './app.config';
import { serverRoutes } from './app.routes.server';
import { HttpClient, withFetch, provideHttpClient } from '@angular/common/http';

const serverConfig: ApplicationConfig = {
  providers: [
    provideServerRendering(),
    provideServerRouting(serverRoutes),
    provideRouter(routes),
    provideHttpClient(withFetch()) // Use fetch API for better SSR compatibility
  ]
};

export const config = mergeApplicationConfig(appConfig, serverConfig);
