import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class UnsignedContractService {
  // Make apiUrl public so it can be accessed by components
  public apiUrl = environment.apiUrl + '/api/unsigned-contracts';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Get headers for API requests with authentication token
   * @returns HttpHeaders with content type and auth token
   */
  private getHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken();
    let headers = new HttpHeaders()
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json');

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.error('No auth token available for API request');
    }

    return headers;
  }

  /**
   * For backward compatibility
   */
  private getAuthHeaders(): HttpHeaders {
    return this.getHeaders();
  }

  /**
   * Get all unsigned contracts
   */
  getUnsignedContracts(): Observable<any> {
    return this.http.get(this.apiUrl, {
      headers: this.getHeaders()
    });
  }

  /**
   * Get a specific unsigned contract by ID
   */
  getUnsignedContract(id: number): Observable<any> {
    console.log(`Getting unsigned contract with ID: ${id}`);
    console.log('API URL:', `${this.apiUrl}/${id}`);

    return this.http.get(`${this.apiUrl}/${id}`, {
      headers: this.getHeaders()
    }).pipe(
      catchError(error => {
        console.error('Error getting unsigned contract:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get an unsigned contract file as a blob (for viewing in the app)
   * This doesn't trigger a browser download
   */
  getUnsignedContractFile(id: number): Observable<Blob> {
    console.log(`Getting unsigned contract file with ID: ${id}`);
    console.log('API URL:', `${this.apiUrl}/${id}/view`);

    // Get the auth token
    const token = this.authService.getAuthToken();
    console.log('Auth token available:', !!token);

    // Create headers with only Authorization (no Content-Type for blob)
    let headers = new HttpHeaders();
    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.error('No auth token available for file request');
    }

    // Use the new /view endpoint which returns the file for viewing without download
    return this.http.get(`${this.apiUrl}/${id}/view`, {
      responseType: 'blob',
      headers: headers,
      withCredentials: true // Include cookies if any
    }).pipe(
      catchError(error => {
        console.error('Error getting unsigned contract file:', error);
        if (error.status === 401) {
          console.error('Authentication error: User is not authorized to access this contract');
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Download an unsigned contract (triggers browser download)
   */
  downloadUnsignedContract(id: number): Observable<Blob> {
    console.log(`Downloading unsigned contract with ID: ${id}`);
    console.log('API URL:', `${this.apiUrl}/${id}/download`);

    // Get the auth token
    const token = this.authService.getAuthToken();
    console.log('Auth token available:', !!token);

    // Create headers with only Authorization (no Content-Type for blob)
    let headers = new HttpHeaders();
    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.error('No auth token available for download request');
    }

    return this.http.get(`${this.apiUrl}/${id}/download`, {
      responseType: 'blob',
      headers: headers,
      withCredentials: true // Include cookies if any
    }).pipe(
      catchError(error => {
        console.error('Error downloading unsigned contract:', error);
        if (error.status === 401) {
          console.error('Authentication error: User is not authorized to download this contract');
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Delete an unsigned contract
   */
  deleteUnsignedContract(id: number): Observable<any> {
    console.log(`Deleting unsigned contract with ID: ${id}`);
    console.log('API URL:', `${this.apiUrl}/${id}`);

    // Get the auth token
    const token = this.authService.getAuthToken();
    console.log('Auth token available for delete request:', !!token);

    // Create headers with Authorization
    let headers = this.getHeaders();

    return this.http.delete(`${this.apiUrl}/${id}`, {
      headers: headers,
      withCredentials: true // Include cookies if any
    }).pipe(
      catchError(error => {
        console.error('Error deleting unsigned contract:', error);
        if (error.status === 401) {
          console.error('Authentication error: User is not authorized to delete this contract');
        } else if (error.status === 404) {
          console.error('Contract not found or already deleted');
        }
        return throwError(() => error);
      })
    );
  }
}
