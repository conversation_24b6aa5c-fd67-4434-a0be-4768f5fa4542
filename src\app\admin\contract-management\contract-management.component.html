<div class="container contract-management-container">
  <div *ngIf="loading && !partner" class="loading-container">
    <div class="spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <div *ngIf="partner">
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-file-contract"></i>
        Contracts for {{ partner.name }}
      </h1>
      <a routerLink="/admin/partners" class="back-button">
        <i class="fas fa-arrow-left"></i> Back to Partners
      </a>
    </div>

    <!-- Contract Form -->
    <div class="card mb-4">
      <div class="card-header">
        <h5>{{ editMode ? 'Edit Contract' : 'Add New Contract' }}</h5>
      </div>
      <div class="card-body">
        <form [formGroup]="contractForm" (ngSubmit)="onSubmit()">
          <div class="form-group">
            <label for="name" class="form-label">
              <i class="fas fa-file-signature"></i> Contract Name
            </label>
            <input
              type="text"
              class="form-control"
              id="name"
              formControlName="name"
              placeholder="Enter contract name">
            <div *ngIf="contractForm.get('name')?.invalid && contractForm.get('name')?.touched" class="text-danger">
              <i class="fas fa-exclamation-circle"></i> Name is required
            </div>
          </div>

          <div class="form-group">
            <label for="description" class="form-label">
              <i class="fas fa-align-left"></i> Description
            </label>
            <textarea
              class="form-control"
              id="description"
              rows="3"
              formControlName="description"
              placeholder="Enter contract description (optional)"></textarea>
          </div>

          <div class="form-group">
            <label for="file" class="form-label">
              <i class="fas fa-upload"></i> {{ editMode ? 'Replace File (optional)' : 'Contract File' }}
            </label>
            <input type="file" class="form-control" id="file" (change)="onFileSelected($event)" accept=".pdf,.doc,.docx,.txt">
            <small class="form-text">Accepted formats:  DOC, DOCX, TXT (Max: 10MB)</small>
            <div *ngIf="!editMode && !selectedFile && contractForm.get('name')?.touched" class="text-danger">
              <i class="fas fa-exclamation-circle"></i> File is required for new contracts
            </div>
          </div>

          <div class="btn-group">
            <button type="submit" class="btn btn-primary" [disabled]="contractForm.invalid || loading || (!editMode && !selectedFile)">
              <i class="fas fa-{{ editMode ? 'save' : 'plus' }}"></i>
              {{ editMode ? 'Update Contract' : 'Add Contract' }}
            </button>
            <button type="button" class="btn btn-secondary" (click)="resetForm()" [disabled]="loading">
              <i class="fas fa-times"></i> Cancel
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Contracts Table -->
    <div class="card">
      <div class="card-header">
        <h5>
          <i class="fas fa-list"></i>
          Contracts List
        </h5>
      </div>
      <div class="card-body">
        <div *ngIf="loading" class="loading-container">
          <div class="spinner" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <div *ngIf="!loading && contracts.length === 0" class="no-data-message">
          <i class="fas fa-file-contract"></i>
          <p>No contracts found for this partner.</p>
        </div>



        <div class="table-responsive" *ngIf="!loading && contracts.length > 0">
          <table class="table">
            <thead>
              <tr>
                <th><i class="fas fa-hashtag"></i> ID</th>
                <th><i class="fas fa-file-signature"></i> Name</th>
                <th><i class="fas fa-align-left"></i> Description</th>
                <th><i class="fas fa-file"></i> File</th>
                <th><i class="fas fa-tag"></i> Type</th>
                <th><i class="fas fa-cogs"></i> Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let contract of contracts; trackBy: trackByContractId"
                  [style.background-color]="isDarkMode ? '#1a1a1a' : ''"
                  [style.color]="isDarkMode ? '#ffffff' : ''"
                  class="table-row"
                  [class.dark-mode-row]="isDarkMode">
                <td>{{ contract.id || 'N/A' }}</td>
                <td>
                  <strong>{{ contract.name || 'Unnamed Contract' }}</strong>
                </td>
                <td>{{ contract.description || 'No description' }}</td>
                <td>
                  <i class="fas fa-file-alt"></i>
                  {{ contract.filename || 'No file' }}
                </td>
                <td>
                  <span class="badge"
                        [class.bg-danger]="contract.type === 'pdf'"
                        [class.bg-primary]="contract.type === 'word'"
                        [class.bg-secondary]="contract.type === 'text'">
                    {{ contract.type || 'unknown' }}
                  </span>
                </td>
                <td>
                  <div class="btn-group">
                    <button class="btn btn-sm btn-primary" (click)="editContract(contract)" title="Edit Contract">
                      <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-danger" (click)="contract.id !== undefined && deleteContract(contract.id)" title="Delete Contract">
                      <i class="fas fa-trash"></i> Delete
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
