{"name": "my-angular-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:my-angular-app": "node dist/my-angular-app/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.2.1", "@angular/cdk": "^19.2.1", "@angular/common": "^19.1.0", "@angular/compiler": "^19.2.1", "@angular/core": "^19.2.1", "@angular/forms": "^19.1.0", "@angular/material": "^19.2.6", "@angular/platform-browser": "^19.2.1", "@angular/platform-browser-dynamic": "^19.2.1", "@angular/platform-server": "^19.1.0", "@angular/router": "^19.2.2", "@angular/ssr": "^19.1.5", "@fortawesome/fontawesome-free": "^6.7.2", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@pdf-lib/fontkit": "^1.1.1", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.3", "chart.js": "^4.4.8", "express": "^4.21.2", "ngx-extended-pdf-viewer": "^23.2.0", "pdf-lib": "^1.17.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.5", "@angular/cli": "^19.1.5", "@angular/compiler-cli": "^19.1.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}