<div class="signup-container">
  <div class="signup-left">
    <div class="logo-section">
      <img src="assets/images/logo.png" class="logo" />
      <a href="/" class="back-link"><i class="material-icons" style="font-size: 16px; vertical-align: middle; margin-right: 4px;">arrow_back</i> Back to website</a>
    </div>
    <div class="hero-content">
      <h2>Sign up for your account</h2>
      <p class="hero-subtitle">Join thousands of professionals who trust our platform for their contract management needs.</p>
      <div class="hero-dots">
        <span class="dot active"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
      <i class="bi bi-file-earmark-text contract-icon"></i>
    </div>
  </div>

  <div class="signup-right">
    <div class="signup-form-container">
      <h1>Create an account</h1>
      <p class="login-link">Already have an account? <a href="/signin">Log in</a></p>

      <form [formGroup]="signupForm" (ngSubmit)="onSubmit()" class="signup-form">
        <div *ngIf="message" class="message" [class.error]="message.includes('error')">
          {{ message }}
        </div>

        <div class="name-fields">
          <div class="form-field">
            <input type="text" formControlName="firstname" placeholder="First name" />
            <div *ngIf="signupForm.get('firstname')?.invalid && signupForm.get('firstname')?.touched" class="error-message">
              First name is required
            </div>
          </div>
          <div class="form-field">
            <input type="text" formControlName="lastname" placeholder="Last name" />
            <div *ngIf="signupForm.get('lastname')?.invalid && signupForm.get('lastname')?.touched" class="error-message">
              Last name is required
            </div>
          </div>
        </div>

        <div class="form-field">
          <input type="email" formControlName="email" placeholder="Email" />
          <div *ngIf="signupForm.get('email')?.invalid && signupForm.get('email')?.touched" class="error-message">
            Enter a valid email address
          </div>
        </div>

        <div class="form-field password-field">
          <input [type]="showPassword ? 'text' : 'password'"
                 formControlName="password"
                 placeholder="Enter your password" />
          <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
            <i class="bi" [class.bi-eye]="!showPassword" [class.bi-eye-slash]="showPassword"></i>
          </button>
          <div *ngIf="signupForm.get('password')?.invalid && signupForm.get('password')?.touched" class="error-message">
            Password must be at least 6 characters
          </div>
        </div>

        <div class="form-field password-field">
          <input [type]="showPassword ? 'text' : 'password'"
                 formControlName="password_confirmation"
                 placeholder="Confirm password" />
          <div *ngIf="signupForm.get('password_confirmation')?.invalid && signupForm.get('password_confirmation')?.touched" class="error-message">
            Confirm password is required
          </div>
        </div>

        <div class="terms-checkbox">
          <input type="checkbox" id="terms" formControlName="terms" />
          <label for="terms">
            I agree to the <a href="/terms" class="terms-link">Terms & Conditions</a>
          </label>
          <div *ngIf="signupForm.get('terms')?.invalid && signupForm.get('terms')?.touched" class="error-message">
            You must agree to the terms and conditions
          </div>
        </div>

        <button type="submit" class="signup-button" [disabled]="!signupForm.valid || isLoading">
          {{ isLoading ? 'Creating Account...' : 'Create Account' }}
        </button>


      </form>
    </div>
  </div>
</div>

