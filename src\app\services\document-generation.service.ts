// src/app/services/document-generation.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';
import {
  DocumentGenerationRequest,
  DocumentGenerationResponse,
  GeneratedDocument
} from '../models/document-generation.model';

@Injectable({
  providedIn: 'root'
})
export class DocumentGenerationService {
  private apiUrl = environment.apiUrl + '/api'; // Laravel API URL

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Get auth headers for API requests
   * @returns HttpHeaders with authentication token if available
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken();
    let headers = new HttpHeaders()
      .set('Content-Type', 'application/json');

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.warn('No auth token available for document generation request');
    }

    return headers;
  }

  /**
   * Generate a document by replacing placeholders with values
   *
   * @param filePath Path to the original file in storage
   * @param replacements Key-value pairs for replacements (e.g. {'name': 'John Doe'})
   * @param documentName Optional name for the document
   * @returns Observable with the generated document as a Blob and document ID in the response
   */
  generateDocument(
    filePath: string,
    replacements: Record<string, string>,
    documentName?: string
  ): Observable<{ blob: Blob, documentId: number | null }> {
    const url = `${this.apiUrl}/documents/generate`;

    console.log('Preparing document generation request for:', filePath);

    // Create a simple request body
    let filename = '';
    let file_path = '';

    // Handle both partner contracts and user uploads
    if (filePath.includes('contracts/')) {
      // This is a partner contract
      file_path = filePath;
      filename = filePath.split('/').pop() || '';
      console.log('Processing partner contract:', filename);
    } else if (filePath.includes('uploads/')) {
      // This is a user upload with full path
      file_path = filePath;
      filename = filePath.split('/').pop() || '';
      console.log('Processing uploaded file with path:', file_path);
    } else {
      // This is just a filename - add uploads/ prefix
      filename = filePath;
      file_path = 'uploads/' + filePath;
      console.log('Processing uploaded file by name:', filename);
    }

    // Validate replacements
    if (!replacements || Object.keys(replacements).length === 0) {
      console.warn('No replacements provided for document generation');
    } else {
      console.log('Replacements count:', Object.keys(replacements).length);
    }

    // Create a simple request body
    const finalRequest = {
      filename: filename,
      file_path: file_path,
      document_name: documentName || '',
      replacements: replacements,
      is_partner_contract: filePath.includes('contracts/') // Add this flag to help the backend identify partner contracts
    };

    console.log('Document generation request:', JSON.stringify(finalRequest));

    // Set headers for blob response with authentication
    const headers = this.getAuthHeaders()
      .set('Accept', 'application/octet-stream');

    // Return the response as a blob with document ID from header
    return this.http.post(url, finalRequest, {
      headers: headers,
      responseType: 'blob',
      observe: 'response' // Get the full response to access headers
    }).pipe(
      map(response => {
        // Log all headers for debugging
        console.log('Response headers received:');
        response.headers.keys().forEach(key => {
          console.log(`${key}: ${response.headers.get(key)}`);
        });

        // Try to get document ID from header
        let documentId = response.headers.get('X-Document-Id');
        console.log('Extracted document ID from header:', documentId);

        // If header doesn't work, try to get from cookies as fallback
        if (!documentId && typeof document !== 'undefined') {
          const cookieValue = this.getCookie('document_id');
          if (cookieValue) {
            documentId = cookieValue;
            console.log('Extracted document ID from cookie:', documentId);
          }
        }

        return {
          blob: response.body as Blob,
          documentId: documentId ? parseInt(documentId, 10) : null
        };
      })
    );
  }

  /**
   * Download a generated document
   *
   * @param blob The document blob
   * @param filename The filename to save as
   */
  downloadGeneratedDocument(blob: Blob, filename: string): void {
    // Create a URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a link element
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;

    // Append to body, click, and remove
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  /**
   * Get all generated documents
   *
   * @returns Observable with the list of generated documents
   */
  getGeneratedDocuments(): Observable<any> {
    const url = `${this.apiUrl}/generated-documents`;
    return this.http.get(url, { headers: this.getAuthHeaders() });
  }

  /**
   * Get a specific generated document
   *
   * @param id Document ID
   * @returns Observable with the document details
   */
  getGeneratedDocument(id: number): Observable<any> {
    const url = `${this.apiUrl}/generated-documents/${id}`;
    return this.http.get(url, { headers: this.getAuthHeaders() });
  }

  /**
   * Get the download URL for a generated document
   *
   * @param id Document ID
   * @returns The download URL
   */
  getDownloadUrl(id: number): string {
    return `${this.apiUrl}/generated-documents/${id}/download`;
  }

  /**
   * Get the preview URL for a generated document
   *
   * @param id Document ID
   * @returns The preview URL
   */
  getPreviewUrl(id: number): string {
    return `${this.apiUrl}/generated-documents/${id}/preview`;
  }

  /**
   * Delete a generated document
   *
   * @param id Document ID
   * @returns Observable with the deletion result
   */
  deleteGeneratedDocument(id: number): Observable<any> {
    const url = `${this.apiUrl}/generated-documents/${id}`;
    return this.http.delete(url, { headers: this.getAuthHeaders() });
  }

  /**
   * Helper method to get a cookie value by name
   *
   * @param name Cookie name
   * @returns Cookie value or empty string if not found
   */
  private getCookie(name: string): string {
    if (typeof document === 'undefined') return '';

    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);

    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || '';
    }

    return '';
  }
}
