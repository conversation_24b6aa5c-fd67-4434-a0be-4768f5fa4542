/* Contact Styles - Modern and Enhanced */
:host {
  display: block;
  width: 100%;
}

/* Base Styles and Variables */
:root {
  --primary-color: #4ECDC4;
  --primary-dark: #3DACA4;
  --primary-light: #6FDED7;
  --accent-color: #FF6B6B;
  --dark-bg: #1E1E2E;
  --card-bg: #252535;
  --form-bg: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  --form-text: #FFFFFF;
  --form-text-secondary: rgba(255, 255, 255, 0.7);
  --form-placeholder: rgba(255, 255, 255, 0.6);
  --form-border: rgba(255, 255, 255, 0.2);
  --form-focus-border: rgba(255, 255, 255, 0.5);
  --form-input-bg: rgba(255, 255, 255, 0.1);
  --form-input-focus-bg: rgba(255, 255, 255, 0.15);
  --text-primary: #FFFFFF;
  --text-secondary: #B3B3B3;
  --success-gradient: linear-gradient(135deg, #10b981, #059669);
  --error-gradient: linear-gradient(135deg, #ef4444, #b91c1c);
  --border-radius-sm: 8px;
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light mode styles */
body:not(.dark-mode) {
  --text-primary: #333333;
  --text-secondary: #666666;
  --card-bg: #FFFFFF;
  --dark-bg: #F5F5F5;
  --form-bg: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  --form-text: #FFFFFF;
  --form-text-secondary: rgba(255, 255, 255, 0.8);
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

/* Enhanced form container for light mode */
body:not(.dark-mode) .form-container {
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(78, 205, 196, 0.3);
  border: none;
  background: linear-gradient(135deg, #f0f9f9, #e0f2f1);
  position: relative;
  transform: translateY(-5px); /* Slight lift effect */
  margin-top: 3rem;
  margin-bottom: 2rem;
  padding: 3rem;
  border-radius: 20px;
}

/* Add distinctive border to container for light mode */
body:not(.dark-mode) .form-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light), var(--accent-color), var(--primary-color));
  border-radius: 22px;
  z-index: -1;
}

/* Add subtle accent line to the top of the container for light mode */
body:not(.dark-mode) .form-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCI+PGNpcmNsZSBjeD0iMSIgY3k9IjEiIHI9IjEiIGZpbGw9InJnYmEoNzgsIDIwNSwgMTk2LCAwLjEpIi8+PC9zdmc+');
  background-size: 20px 20px;
  opacity: 0.5;
  pointer-events: none;
  z-index: 0;
}

/* Add stronger visual styling to the form for light mode */
body:not(.dark-mode) .contact-form {
  border: 2px solid rgba(78, 205, 196, 0.3);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Improve form input visibility in light mode */
body:not(.dark-mode) .form-control {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

body:not(.dark-mode) .form-control:focus {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

/* Make form titles more visible in light mode */
body:not(.dark-mode) .form-title,
body:not(.dark-mode) .form-subtitle {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Dark mode styles */
body.dark-mode {
  --text-primary: #FFFFFF;
  --text-secondary: #CCCCCC;
  --card-bg: #252535;
  --dark-bg: #1E1E2E;
  --form-bg: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  --form-text: #FFFFFF;
  --form-text-secondary: rgba(255, 255, 255, 0.7);
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Dark mode form container */
body.dark-mode .form-container {
  background-color: var(--card-bg);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Dark mode form styling */
body.dark-mode .contact-form {
  background: var(--form-bg);
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Dark mode form inputs */
body.dark-mode .form-control {
  background-color: var(--form-input-bg);
  border: 1px solid var(--form-border);
  color: var(--form-text);
}

body.dark-mode .form-control:focus {
  background-color: var(--form-input-focus-bg);
  border-color: var(--form-focus-border);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

/* Dark mode form text and labels */
body.dark-mode .form-title {
  color: var(--form-text);
}

body.dark-mode .form-subtitle {
  color: var(--form-text-secondary);
}

body.dark-mode .input-label {
  color: var(--form-text);
}

body.dark-mode .form-control::placeholder {
  color: var(--form-placeholder);
}

.contact-card {
  width: 100%;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  margin-bottom: 24px;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  position: relative;
}

.contact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

/* Component Container */
.component-container {
  width: 100%;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  margin-bottom: 30px;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.component-container:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
  transform: translateY(-3px);
}

/* Main Layout Styles */
.contact-section {
  padding: 4rem 2.5rem;
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSIyIiBmaWxsPSJyZ2JhKDc4LCAyMDUsIDE5NiwgMC4xKSIvPjwvc3ZnPg==');
  pointer-events: none;
  z-index: -1;
  opacity: 0.3;
}

.feature-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2.5rem;
  transition: var(--transition);
  position: relative;
  z-index: 1;
}

.section-header {
  text-align: center;
  margin-bottom: 1rem;
  position: relative;
  padding: 1rem 0;
}

.section-header::after {
  content: '';
  display: block;
  width: 80px;
  height: 3px;
  margin: 1rem auto 0;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 3px;
}

.animated-element {
  position: relative;
  z-index: 2;
}

.decorative-dots {
  position: absolute;
  top: -10px;
  right: 10%;
  width: 140px;
  height: 140px;
  background-image: radial-gradient(var(--primary-color) 1px, transparent 1px);
  background-size: 18px 18px;
  opacity: 0.15;
  z-index: 1;
  border-radius: 50%;
  animation: rotateSlow 30s linear infinite;
}

h2 {
  font-size: 2.8rem;
  color: #FFFFFF;
  margin-bottom: 0.5rem;
  font-weight: 700;
  letter-spacing: -0.5px;
  position: relative;
  display: block;
  text-align: center;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

h3 {
  font-size: 1.8rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-weight: 600;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #CCCCCC;
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.highlight {
  color: #4ECDC4;
  position: relative;
  display: inline-block;
  font-weight: 700;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.5rem;
  margin-top: 3rem;
  width: 100%;
}

.contact-info {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 2.5rem;
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.125);

  position: relative;
  overflow: hidden;
}

.contact-info::before {
  content: '';
  position: absolute;
  top: -80px;
  left: -80px;
  width: 160px;
  height: 160px;
  background: radial-gradient(circle, rgba(78, 205, 196, 0.2) 0%, rgba(78, 205, 196, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.card-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, transparent 50%, rgba(255, 255, 255, 0.03) 50%);
  z-index: 0;
}

/* Social Links Styles */
.social-links {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  position: relative;
  z-index: 1;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  font-size: 1rem;
  transition: var(--transition);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-color);
  opacity: 0;
  transition: var(--transition);
  z-index: -1;
}

.social-link:hover {
  transform: translateY(-3px);
  color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-link:hover::before {
  opacity: 1;
}

.contact-info:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
}

.info-title {
  position: relative;
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
  display: inline-block;
}

.info-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 3px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 1.25rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.info-item i {
  font-size: 1.75rem;
  color: var(--primary-color);
  background: rgba(78, 205, 196, 0.1);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.info-item:hover i {
  transform: scale(1.1) rotate(5deg);
  color: var(--primary-light);
  background: rgba(78, 205, 196, 0.2);
}

.info-item .info-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.3rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-weight: 500;
}

.info-item .info-value {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.contact-link {
  color: var(--primary-color);
  text-decoration: none;
  position: relative;
  transition: var(--transition);
}

.contact-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--primary-color);
  transition: var(--transition);
}

.contact-link:hover {
  color: var(--primary-light);
}

.contact-link:hover::after {
  width: 100%;
}

.contact-map {
  position: relative;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.125);

}

.contact-map:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.contact-map iframe {
  width: 100%;
  height: 340px;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  border: none;
  filter: grayscale(20%) contrast(1.1);
  transition: var(--transition);
}

.contact-map:hover iframe {
  filter: grayscale(0%) contrast(1.1);
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.15), rgba(255, 107, 107, 0.1));
  pointer-events: none;
  z-index: 1;
  opacity: 0.6;
  mix-blend-mode: overlay;
  transition: var(--transition);
}

.contact-map:hover .map-overlay {
  opacity: 0;
}

.map-caption {
  font-size: 0.9rem;
  text-align: center;
  color: var(--text-secondary);
  padding: 0.8rem 1rem;
  background-color: rgba(51, 51, 51, 0.8);
  backdrop-filter: blur(5px);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.map-caption i {
  color: var(--primary-color);
  font-size: 0.9rem;
  animation: pulse 1.5s infinite;
}



/* Form Container Styles */
.form-container {
  width: 100%;
  max-width: 1000px;
  margin: 2rem auto 0;
  position: relative;
  padding: 2.5rem;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.125);
  transition: var(--transition);
}

.form-container::before {
  content: '';
  position: absolute;
  top: -150px;
  right: -150px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(78, 205, 196, 0.08) 0%, rgba(255, 255, 255, 0) 80%);
  z-index: 0;
  border-radius: 50%;
}

.form-container::after {
  content: '';
  position: absolute;
  bottom: -150px;
  left: -150px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.08) 0%, rgba(255, 255, 255, 0) 80%);
  z-index: 0;
  border-radius: 50%;
}

/* Contact Form Styles */
.contact-form {
  background: var(--form-bg);
  padding: 3rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  width: 100%;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.contact-form::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 0;
  pointer-events: none;
}

.form-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background-image: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: 0;
}

.form-title {
  margin-bottom: 0.5rem;
  color: var(--form-text);
  text-align: center;
  position: relative;
  z-index: 1;
}

.form-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.form-subtitle {
  text-align: center;
  color: var(--form-text-secondary);
  margin-bottom: 2rem;
  font-size: 0.95rem;
  max-width: 80%;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
}

.privacy-note {
  text-align: center;
  color: var(--form-text-secondary);
  font-size: 0.85rem;
  margin-top: 1.5rem;
  position: relative;
  z-index: 1;
}

.privacy-link {
  color: var(--form-text);
  text-decoration: none;
  position: relative;
  transition: var(--transition);
}

.privacy-link::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background: white;
  transition: var(--transition);
}

.privacy-link:hover::after {
  width: 100%;
}

.contact-form .form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  position: relative;
  z-index: 1;
}

.form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}

.input-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: var(--form-text);
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.input-label .required {
  color: var(--accent-color);
  font-size: 1.2rem;
  line-height: 1;
  position: relative;
  top: 2px;
}

.form-control {
  padding: 1rem 1.2rem;
  font-size: 1rem;

  border-radius: var(--border-radius-sm);
  background-color: var(--form-input-bg);
  color: var(--form-text);
  outline: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.form-control::placeholder {
  color: var(--form-placeholder);
}

.form-control:focus {
  border-color: var(--form-focus-border);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  background-color: var(--form-input-focus-bg);
}

.form-control.is-invalid {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
}

.error-message {
  color: var(--accent-color);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  animation: fadeIn 0.3s ease;
}

.error-message::before {
  content: '⚠';
  font-size: 0.8rem;
}

.full-width {
  grid-column: span 2;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 2.5rem;
}

.btn {
  background-color: white;
  color: var(--primary-dark);
  padding: 0.9rem 2.5rem;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 1.05rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  letter-spacing: 0.5px;
  z-index: 1;
}

.btn i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, var(--primary-color), var(--primary-dark));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.btn:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.btn:hover::before {
  opacity: 1;
}

.btn:hover i {
  transform: translateX(3px);
}

.btn:hover::after {
  transform: translateX(100%);
}

.btn:active {
  transform: translateY(-1px);
}

.btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

.btn:disabled::before,
.btn:disabled::after {
  display: none;
}

/* Success Message Styles */
.success-message-container {
  width: 100%;
  max-width: 900px;
  margin: 2rem auto;
  padding: 2.5rem;
  border-radius: var(--border-radius);
  background: var(--success-gradient);
  box-shadow: 0 15px 40px rgba(16, 185, 129, 0.25);
  animation: slideInUp 0.6s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  position: relative;
  overflow: hidden;
}

.success-message-container::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  pointer-events: none;
}

.success-message {
  display: flex;
  align-items: center;
  gap: 2rem;
  color: white;
  position: relative;
  z-index: 1;
}

.success-icon {
  font-size: 3.5rem;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite, spin 10s linear infinite;
}

.success-content {
  flex: 1;
}

.success-content h3 {
  font-size: 2rem;
  margin-bottom: 0.8rem;
  color: white;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.success-content p {
  font-size: 1.1rem;
  margin-bottom: 0.8rem;
  opacity: 0.9;
  line-height: 1.5;
}

.redirect-text {
  font-size: 0.95rem;
  opacity: 0.8;
  margin-top: 1.5rem;
  font-style: italic;
  display: inline-block;
  position: relative;
  padding-left: 20px;
}

.redirect-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 14px;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.7);
  animation: loadingDots 1.5s infinite;
}

/* Error Message Styles */
.error-message-container {
  width: 100%;
  max-width: 900px;
  margin: 2rem auto;
  padding: 1.8rem 2.5rem;
  border-radius: var(--border-radius);
  background: var(--error-gradient);
  box-shadow: 0 15px 40px rgba(239, 68, 68, 0.25);
  animation: slideInUp 0.6s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  position: relative;
  overflow: hidden;
}

.error-message-container::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  pointer-events: none;
}

.error-message-box {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  color: white;
  position: relative;
  z-index: 1;
}

.error-message-box i {
  font-size: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
}

.error-message-box p {
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.5;
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loadingDots {
  0%, 100% {
    opacity: 0.2;
    width: 7px;
  }
  50% {
    opacity: 1;
    width: 14px;
  }
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
  40%, 60% { transform: translate3d(3px, 0, 0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes rotateSlow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Form Validation Styles */
.form-control.is-invalid {
  border-color: var(--accent-color);
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23FF6B6B'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23FF6B6B' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* Enhanced Responsive Design */
@media (max-width: 992px) {
  .contact-form {
    padding: 2.5rem;
  }

  .contact-section {
    padding: 3.5rem 2rem;
  }

  h2 {
    font-size: 2.4rem;
  }
}

@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-form .form-grid {
    grid-template-columns: 1fr;
    gap: 1.75rem;
  }

  .contact-form {
    padding: 2rem 1.75rem;
  }

  h2 {
    font-size: 2.2rem;
  }

  h3 {
    font-size: 1.6rem;
  }

  .section-subtitle {
    font-size: 0.95rem;
  }

  .success-message {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .success-icon {
    margin-bottom: 0.5rem;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .contact-section {
    padding: 3rem 1.25rem;
  }

  .contact-form {
    padding: 1.75rem 1.5rem;
  }

  h2 {
    font-size: 1.8rem;
  }

  h3 {
    font-size: 1.4rem;
  }

  .info-item i {
    font-size: 1.5rem;
    width: 45px;
    height: 45px;
  }

  .form-control {
    padding: 0.9rem 1rem;
  }

  .success-icon {
    width: 60px;
    height: 60px;
    font-size: 2.5rem;
  }

  .success-content h3 {
    font-size: 1.6rem;
  }
}
