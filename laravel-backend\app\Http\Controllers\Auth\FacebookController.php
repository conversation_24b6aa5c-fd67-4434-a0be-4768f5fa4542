<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
// Removed Socialite dependency

class FacebookController extends Controller
{
    public function redirectToFacebook(Request $request)
    {
        try {
            Log::info('Initiating Facebook OAuth redirect', [
                'origin' => $request->header('Origin'),
                'is_ajax' => $request->ajax(),
                'wants_json' => $request->wantsJson(),
                'accept' => $request->header('Accept')
            ]);

            // Manually build Facebook OAuth URL
            $clientId = config('services.facebook.client_id');
            $redirectUri = config('services.facebook.redirect');

            // Make sure the redirect URI is properly formatted and lowercase
            $redirectUri = str_replace('\\', '/', $redirectUri);
            $redirectUri = str_replace('/Facebook/', '/facebook/', $redirectUri);
            $redirectUri = strtolower($redirectUri);

            // Log all relevant configuration
            Log::info('Facebook OAuth Configuration', [
                'client_id' => config('services.facebook.client_id'),
                'redirect_uri' => $redirectUri,
                'env_redirect_uri' => env('FACEBOOK_REDIRECT_URI'),
                'config_redirect_uri' => config('services.facebook.redirect')
            ]);

            $params = [
                'client_id' => $clientId,
                'redirect_uri' => $redirectUri,
                'response_type' => 'code',
                'scope' => 'email,public_profile',
                'state' => 'facebook' // Add state parameter to identify Facebook callbacks
            ];

            $url = 'https://www.facebook.com/v12.0/dialog/oauth?' . http_build_query($params);

            Log::info('Facebook OAuth URL generated', ['url' => $url]);

            // Always redirect directly to Facebook
            Log::info('Directly redirecting to Facebook OAuth', ['url' => $url]);
            return redirect()->away($url);

        } catch (\Exception $e) {
            Log::error('Facebook OAuth redirect error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'client_id' => config('services.facebook.client_id') ? 'Set' : 'Not set',
                'redirect_uri' => config('services.facebook.redirect') ?: 'Not set'
            ]);

            // If it's an API request, return JSON error
            if ($request->wantsJson() || $request->ajax() || $request->is('api/*')) {
                return response()->json([
                    'error' => 'OAuth Error',
                    'message' => 'Failed to initiate Facebook authentication.',
                    'details' => config('app.debug') ? $e->getMessage() : null
                ], 500)->header('Access-Control-Allow-Origin', $request->header('Origin') ?: '*')
                    ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
                    ->header('Access-Control-Allow-Credentials', 'true');
            }

            return $this->errorResponse('Failed to redirect to Facebook. Please try again.', 500, $e, $request);
        }
    }

    public function handleFacebookCallback(Request $request)
    {
        try {
            Log::info('Facebook callback received', [
                'query' => $request->all(),
                'full_url' => $request->fullUrl(),
                'headers' => $request->header()
            ]);

            // Get the authorization code from the request
            $code = $request->input('code');
            if (!$code) {
                throw new \Exception('Missing authorization code');
            }

            // Verify state parameter to ensure this is a Facebook callback
            $state = $request->input('state');
            Log::info('State parameter received', ['state' => $state]);
            if ($state !== 'facebook') {
                Log::warning('State parameter mismatch', ['expected' => 'facebook', 'received' => $state]);
                // Continue anyway, but log the warning
            }

            // Exchange the code for an access token
            $client = new \GuzzleHttp\Client(['timeout' => 30]);

            $redirectUri = config('services.facebook.redirect');
            // Make sure the redirect URI is properly formatted and lowercase
            $redirectUri = str_replace('\\', '/', $redirectUri);
            $redirectUri = str_replace('/Facebook/', '/facebook/', $redirectUri);
            $redirectUri = strtolower($redirectUri);
            Log::info('Using redirect URI for token exchange', ['uri' => $redirectUri]);

            $tokenParams = [
                'client_id' => config('services.facebook.client_id'),
                'client_secret' => config('services.facebook.client_secret'),
                'redirect_uri' => $redirectUri,
                'code' => $code
            ];

            Log::info('Exchanging code for token', ['params' => $tokenParams]);

            try {
                Log::info('Sending token exchange request to Facebook');
                $response = $client->post('https://graph.facebook.com/v12.0/oauth/access_token', [
                    'form_params' => $tokenParams,
                    'headers' => [
                        'Accept' => 'application/json'
                    ]
                ]);
                Log::info('Token exchange request successful');
            } catch (\Exception $e) {
                Log::error('Token exchange request failed', [
                    'error' => $e->getMessage(),
                    'code' => $e->getCode()
                ]);
                throw $e;
            }

            $responseBody = $response->getBody()->getContents();
            Log::info('Token exchange response', ['response' => $responseBody]);

            $token = json_decode($responseBody, true);
            $accessToken = $token['access_token'] ?? null;

            if (!$accessToken) {
                Log::error('Failed to obtain access token', ['token_response' => $token]);
                throw new \Exception('Failed to obtain access token');
            }

            // Get user profile with the access token
            try {
                Log::info('Fetching user profile from Facebook');
                $userResponse = $client->get('https://graph.facebook.com/v12.0/me', [
                    'query' => [
                        'fields' => 'id,name,email,first_name,last_name,picture',
                        'access_token' => $accessToken
                    ],
                    'headers' => [
                        'Accept' => 'application/json'
                    ]
                ]);
                Log::info('User profile fetch successful');
            } catch (\Exception $e) {
                Log::error('User profile fetch failed', [
                    'error' => $e->getMessage(),
                    'code' => $e->getCode()
                ]);
                throw $e;
            }

            $userResponseBody = $userResponse->getBody()->getContents();
            Log::info('Facebook user profile response', ['response' => $userResponseBody]);

            $facebookUser = json_decode($userResponseBody, true);
            Log::info('Facebook user data received', [
                'id' => $facebookUser['id'] ?? 'not provided',
                'name' => $facebookUser['name'] ?? 'not provided',
                'email' => $facebookUser['email'] ?? 'not provided',
                'has_picture' => isset($facebookUser['picture']) ? 'yes' : 'no'
            ]);

            // Create a user object that matches the structure expected by findOrCreateUser
            $userObj = new \stdClass();
            $userObj->id = $facebookUser['id'];
            $userObj->name = $facebookUser['name'];
            $userObj->email = $facebookUser['email'] ?? null;
            $userObj->first_name = $facebookUser['first_name'] ?? null;
            $userObj->last_name = $facebookUser['last_name'] ?? null;

            // Handle avatar URL - use a shorter version or null if too long
            $avatarUrl = $facebookUser['picture']['data']['url'] ?? null;
            // Limit the avatar URL length to 255 characters to avoid database issues
            $userObj->avatar = $avatarUrl ? (strlen($avatarUrl) > 255 ? null : $avatarUrl) : null;

            Log::info('User object created for database', [
                'id' => $userObj->id,
                'name' => $userObj->name,
                'email' => $userObj->email,
                'has_avatar' => $userObj->avatar ? 'yes' : 'no'
            ]);

            $user = $this->findOrCreateUser($userObj);
            $token = $user->createToken('facebook-auth')->plainTextToken;

            Log::info('Authentication successful', ['user_id' => $user->id]);

            // For API requests, return JSON
            if ($request->wantsJson() || $request->ajax() || $request->header('Accept') === 'application/json') {
                return response()->json([
                    'user' => $user,
                    'token' => $token
                ])->header('Access-Control-Allow-Origin', $request->header('Origin') ?: '*')
                  ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                  ->header('Access-Control-Allow-Headers', 'Content-Type, X-Auth-Token, Origin, Authorization, X-Requested-With, Accept')
                  ->header('Access-Control-Allow-Credentials', 'true');
            }

            // For browser requests, redirect to the frontend with token and user data
            $frontendUrl = config('app.frontend_url', 'http://localhost:4200');

            // Create a simplified user object for the frontend
            $userData = [
                'id' => $user->id,
                'name' => $user->firstname . ' ' . $user->lastname,
                'firstname' => $user->firstname,
                'lastname' => $user->lastname,
                'email' => $user->email,
                'avatar' => $user->avatar
            ];

            // Use URL-safe base64 encoding for the user data
            $encodedUserData = rtrim(strtr(base64_encode(json_encode($userData)), '+/', '-_'), '=');
            $redirectUrl = "{$frontendUrl}/auth/callback?token={$token}&user={$encodedUserData}";

            Log::info('Redirecting to frontend', ['url' => $redirectUrl]);
            return redirect($redirectUrl);
        } catch (\Exception $e) {
            Log::error('Facebook callback exception: ' . $e->getMessage());
            Log::error('Exception trace: ' . $e->getTraceAsString());

            // Log additional information for debugging
            Log::error('Request details:', [
                'query_params' => $request->all(),
                'headers' => $request->header()
            ]);

            if ($request->wantsJson() || $request->ajax() || $request->is('api/*')) {
                return $this->errorResponse('Failed to authenticate with Facebook. Please try again.', 401, $e, $request);
            }

            // For browser requests, redirect to frontend with error
            $frontendUrl = config('app.frontend_url', 'http://localhost:4200');
            $errorMessage = 'Facebook authentication failed: ' . $e->getMessage();
            Log::error('Redirecting to frontend with error: ' . $errorMessage);

            $errorUrl = "{$frontendUrl}/auth/callback?error=" . urlencode($errorMessage);
            return redirect($errorUrl);
        }
    }

    private function findOrCreateUser($facebookUser)
    {
        Log::info('Facebook user data', [
            'id' => $facebookUser->id,
            'email' => $facebookUser->email,
            'name' => $facebookUser->name,
        ]);

        $user = User::where('facebook_id', $facebookUser->id)->first();

        if (!$user) {
            $user = User::where('email', $facebookUser->email)->first();
        }

        // Use first_name and last_name if available, otherwise split the name
        if (isset($facebookUser->first_name) && isset($facebookUser->last_name)) {
            $firstname = $facebookUser->first_name;
            $lastname = $facebookUser->last_name;
            Log::info('Using first_name and last_name from Facebook', [
                'firstname' => $firstname,
                'lastname' => $lastname
            ]);
        } else {
            $nameParts = explode(' ', $facebookUser->name);
            $firstname = $nameParts[0];
            $lastname = count($nameParts) > 1 ? end($nameParts) : '';
            Log::info('Split name into parts', [
                'firstname' => $firstname,
                'lastname' => $lastname
            ]);
        }

        $userData = [
            'firstname' => $firstname,
            'lastname' => $lastname,
            'email' => $facebookUser->email,
            'facebook_id' => $facebookUser->id,
        ];

        if (Schema::hasColumn('users', 'avatar')) {
            $userData['avatar'] = $facebookUser->avatar;
        }

        if ($user) {
            Log::info('Updating existing user', ['id' => $user->id]);
            $user->update($userData);
        } else {
            Log::info('Creating new user');
            $userData['password'] = bcrypt(Str::random(16));
            $user = User::create($userData);
        }

        return $user;
    }

    private function errorResponse(string $message, int $status = 400, ?\Exception $e = null, ?Request $request = null)
    {
        if ($e !== null) {
            logger()->error('OAuth error', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        $response = response()->json([
            'error' => 'Authentication failed',
            'message' => $message,
            'details' => config('app.debug') && $e !== null ? $e->getMessage() : 'Something went wrong',
        ], $status);

        // Add CORS headers if request is provided
        if ($request !== null) {
            $origin = $request->header('Origin');
            if ($origin) {
                $response->header('Access-Control-Allow-Origin', $origin);
                $response->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
                $response->header('Access-Control-Allow-Headers', 'Content-Type, X-Auth-Token, Origin, Authorization');
                $response->header('Access-Control-Allow-Credentials', 'true');
            }
        }

        return $response;
    }
}
