
<section class="dashboard-section" aria-labelledby="dashboard-heading">
  <div class="feature-content">
    <!-- En-tête avec animation -->
    <header class="section-header">
      <h2 id="dashboard-heading" class="animate__fadeInDown">
        <span class="highlight">Control panel
        </span>
      </h2>
      <p class="section-subtitle">View and manage your contracts at a glance</p>
    </header>

    <div class="dashboard-container" [class.loading]="isLoading">
  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Loading data...</p>
  </div>

  <!-- Refresh Button -->
  <div class="refresh-container">
    <button class="btn-refresh" (click)="refreshData()" title="Refresh data">
      <i class="fas fa-sync-alt"></i>
    </button>
  </div>

  <!-- Metrics Row -->
  <div class="metrics-row">
    <!-- Total Contracts Metric -->
    <div class="metric-card">
      <div class="metric-header">
        <div>
          <div class="metric-title">Total Contracts</div>
          <div class="metric-value">{{ metrics.total }}</div>
          <div class="trend trend-up">
            <i class="fas fa-arrow-up"></i> 10% from previous period
          </div>
        </div>
        <div class="metric-icon total">
          <i class="fas fa-file-contract"></i>
        </div>
      </div>
    </div>

    <!-- Signed Contracts Metric -->
    <div class="metric-card">
      <div class="metric-header">
        <div>
          <div class="metric-title">Signed Contracts</div>
          <div class="metric-value">{{ metrics.signed }}</div>
          <div class="trend trend-up">
            <i class="fas fa-arrow-up"></i> 8% from previous period
          </div>
        </div>
        <div class="metric-icon signed">
          <i class="fas fa-file-signature"></i>
        </div>
      </div>
    </div>

    <!-- Unsigned Contracts Metric -->
    <div class="metric-card">
      <div class="metric-header">
        <div>
          <div class="metric-title">Unsigned Contracts</div>
          <div class="metric-value">{{ metrics.unsigned }}</div>
          <div class="trend trend-up">
            <i class="fas fa-arrow-up"></i> 15% from previous period
          </div>
        </div>
        <div class="metric-icon unsigned">
          <i class="fas fa-file-alt"></i>
        </div>
      </div>
    </div>

    <!-- Pending Contracts Metric -->
    <div class="metric-card">
      <div class="metric-header">
        <div>
          <div class="metric-title">Pending Contracts</div>
          <div class="metric-value">{{ metrics.active }}</div>
          <div class="trend trend-down">
            <i class="fas fa-arrow-down"></i> 5% from previous period
          </div>
        </div>
        <div class="metric-icon pending">
          <i class="fas fa-hourglass-half"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Row -->
  <div class="charts-row">
    <!-- Activity Chart -->
    <div class="chart-card activity-chart-card">
      <div class="chart-header">
        <h2 class="chart-title">Contract Activity</h2>
        <div class="chart-subtitle">Tracking signed vs unsigned contracts over time</div>
      </div>
      <div class="chart-canvas-container">
        <canvas #activityChart></canvas>
      </div>
    </div>

    <!-- Completion Chart -->
    <div class="chart-card">
      <div class="chart-header">
        <h2 class="chart-title">Contract Status</h2>
      </div>
      <div class="chart-canvas-container">
        <canvas #completionChart></canvas>
      </div>
    </div>
  </div>

  <!-- Contracts Table -->
  <div class="contracts-card">
    <div class="contracts-header">
      <div class="contracts-header-top">
        <h2 class="contracts-title">Contracts</h2>
        <div class="contracts-actions">
          <div class="search-bar">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="Search contracts..." (input)="searchContracts($event)">
          </div>
          <button class="btn-export" (click)="exportToCSV()" title="Export to CSV">
            <i class="fas fa-file-export"></i> Export
          </button>
          <button class="btn-add" (click)="addNewContract()">
            <i class="fas fa-plus"></i> Add Contract
          </button>
        </div>
      </div>

      <!-- Contract Tabs -->
      <div class="contract-tabs">
        <button class="tab-btn" [class.active]="activeTab === 'all'" (click)="setActiveTab('all')">
          All Contracts
        </button>
        <button class="tab-btn" [class.active]="activeTab === 'signed'" (click)="setActiveTab('signed')">
          Signed Contracts
        </button>
        <button class="tab-btn" [class.active]="activeTab === 'unsigned'" (click)="setActiveTab('unsigned')">
          Unsigned Contracts
        </button>
      </div>
    </div>

    <table class="contracts-table">
      <thead>
        <tr>
          <th (click)="setSortField('id')" class="sortable-header">
            ID
            <i class="fas"
               [class.fa-sort]="sortField !== 'id'"
               [class.fa-sort-up]="sortField === 'id' && sortDirection === 'asc'"
               [class.fa-sort-down]="sortField === 'id' && sortDirection === 'desc'"></i>
          </th>
          <th (click)="setSortField('client')" class="sortable-header">
            Name
            <i class="fas"
               [class.fa-sort]="sortField !== 'client'"
               [class.fa-sort-up]="sortField === 'client' && sortDirection === 'asc'"
               [class.fa-sort-down]="sortField === 'client' && sortDirection === 'desc'"></i>
          </th>
          <th (click)="setSortField('date')" class="sortable-header">
            Date
            <i class="fas"
               [class.fa-sort]="sortField !== 'date'"
               [class.fa-sort-up]="sortField === 'date' && sortDirection === 'asc'"
               [class.fa-sort-down]="sortField === 'date' && sortDirection === 'desc'"></i>
          </th>
          <th *ngIf="activeTab !== 'signed'" (click)="setSortField('status')" class="sortable-header">
            Status
            <i class="fas"
               [class.fa-sort]="sortField !== 'status'"
               [class.fa-sort-up]="sortField === 'status' && sortDirection === 'asc'"
               [class.fa-sort-down]="sortField === 'status' && sortDirection === 'desc'"></i>
          </th>
          <th *ngIf="activeTab === 'signed' || activeTab === 'all'">Signer Email</th>
          <th (click)="setSortField('type')" class="sortable-header">
            Type
            <i class="fas"
               [class.fa-sort]="sortField !== 'type'"
               [class.fa-sort-up]="sortField === 'type' && sortDirection === 'asc'"
               [class.fa-sort-down]="sortField === 'type' && sortDirection === 'desc'"></i>
          </th>
          <th>Progress</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let contract of filteredContracts">
          <td>{{ contract.id }}</td>
          <td>{{ contract.client || contract.nom_contrat }}</td>
          <td>{{ contract.startDate | date:'MMM d, y' }}</td>
          <td *ngIf="activeTab !== 'signed'">
            <span class="contract-status"
                  [ngClass]="{
                    'status-active': contract.status === 'active' || contract.status === 'pending',
                    'status-signed': contract.status === 'signed',
                    'status-unsigned': contract.type === 'unsigned' && contract.status !== 'pending'
                  }">
              {{ contract.status | titlecase }}
            </span>
          </td>
          <td *ngIf="activeTab === 'signed' || activeTab === 'all'">
            {{ contract.email_signataire || 'N/A' }}
          </td>
          <td>
            <span class="contract-type"
                  [ngClass]="{
                    'type-signed': contract.type === 'signed',
                    'type-unsigned': contract.type === 'unsigned'
                  }">
              {{ contract.type | titlecase }}
            </span>
          </td>
          <td>
            <div class="progress-bar">
              <div class="progress-value"
                  [style.width.%]="contract.progress"
                  [style.background-color]="
                    contract.type === 'unsigned' && contract.status === 'pending' ? '#f59e0b' :
                    contract.type === 'unsigned' ? '#3b82f6' : '#10b981'">
              </div>
            </div>
          </td>
          <td>
            <div class="table-actions">
              <button class="action-btn" title="View details" (click)="viewContractDetails(contract)">
                <i class="fas fa-eye"></i>
              </button>
              <button class="action-btn" title="Download contract" (click)="downloadContract(contract)">
                <i class="fas fa-download"></i>
              </button>
              <button class="action-btn" *ngIf="contract.type === 'unsigned'" title="Sign contract" (click)="signContract(contract)">
                <i class="fas fa-signature"></i>
              </button>
              <button class="action-btn" *ngIf="contract.type === 'unsigned'" title="Delete contract" (click)="deleteContract(contract)">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
        <tr *ngIf="filteredContracts.length === 0">
          <td colspan="8" class="no-data">
            <div class="empty-state">
              <i class="fas fa-file-contract empty-icon"></i>
              <p>No contracts found</p>
              <button class="btn-add" (click)="addNewContract()">
                <i class="fas fa-plus"></i> Add Your First Contract
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Pagination -->
    <div class="pagination" *ngIf="filteredContracts.length > 0">
      <button class="pagination-btn" [disabled]="currentPage === 1" (click)="currentPage = 1">
        <i class="fas fa-angle-double-left"></i>
      </button>
      <button class="pagination-btn" [disabled]="currentPage === 1" (click)="currentPage = currentPage - 1">
        <i class="fas fa-angle-left"></i>
      </button>
      <span class="pagination-info">Page {{ currentPage }} of {{ Math.ceil(totalItems / pageSize) }}</span>
      <button class="pagination-btn" [disabled]="currentPage >= Math.ceil(totalItems / pageSize)" (click)="currentPage = currentPage + 1">
        <i class="fas fa-angle-right"></i>
      </button>
      <button class="pagination-btn" [disabled]="currentPage >= Math.ceil(totalItems / pageSize)" (click)="currentPage = Math.ceil(totalItems / pageSize)">
        <i class="fas fa-angle-double-right"></i>
      </button>
    </div>
  </div>

  <!-- Contract Details Modal -->
  <div class="modal" *ngIf="selectedContract">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Contract Details</h2>
        <button class="modal-close" (click)="closeContractDetails()" title="Close">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <!-- Basic Information Section -->
        <div class="detail-group">
          <div class="modal-section-title">Basic Information</div>

          <div class="detail-row">
            <div class="detail-label">Contract ID</div>
            <div class="detail-value">{{ selectedContract.id }}</div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Contract Name</div>
            <div class="detail-value">{{ selectedContract.client || selectedContract.nom_contrat || 'Unnamed Contract' }}</div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Creation Date</div>
            <div class="detail-value">{{ selectedContract.startDate | date:'MMMM d, y' }}</div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Last Updated</div>
            <div class="detail-value">{{ selectedContract.updated_at ? (selectedContract.updated_at | date:'MMMM d, y') : 'Not available' }}</div>
          </div>
        </div>

        <!-- Status Information Section -->
        <div class="detail-group">
          <div class="modal-section-title">Status Information</div>

          <div class="detail-row">
            <div class="detail-label">Contract Type</div>
            <div class="detail-value">
              <span class="detail-badge"
                    [ngClass]="{
                      'type-signed': selectedContract.type === 'signed',
                      'type-unsigned': selectedContract.type === 'unsigned'
                    }">
                <i class="fas" [ngClass]="{
                  'fa-file-signature': selectedContract.type === 'signed',
                  'fa-file-alt': selectedContract.type === 'unsigned'
                }"></i>
                {{ selectedContract.type | titlecase }}
              </span>
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Status</div>
            <div class="detail-value">
              <span class="detail-badge"
                    [ngClass]="{
                      'status-active': selectedContract.status === 'active' || selectedContract.status === 'pending',
                      'status-signed': selectedContract.status === 'signed',
                      'status-unsigned': selectedContract.type === 'unsigned' && selectedContract.status !== 'pending'
                    }">
                <i class="fas" [ngClass]="{
                  'fa-hourglass-half': selectedContract.status === 'active' || selectedContract.status === 'pending',
                  'fa-check-circle': selectedContract.status === 'signed',
                  'fa-clock': selectedContract.type === 'unsigned' && selectedContract.status !== 'pending'
                }"></i>
                {{ selectedContract.status | titlecase }}
              </span>
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-label">Completion Progress</div>
            <div class="detail-value">
              <div class="progress-container">
                <div class="progress-bar">
                  <div class="progress-value"
                      [style.width.%]="selectedContract.progress"
                      [style.background-color]="
                        selectedContract.type === 'unsigned' && selectedContract.status === 'pending' ? '#f59e0b' :
                        selectedContract.type === 'unsigned' ? '#3b82f6' : '#10b981'">
                  </div>
                </div>
                <span class="progress-text">{{ selectedContract.progress }}% Complete</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information Section -->
        <div class="detail-group">
          <div class="modal-section-title">Contact Information</div>

          <div class="detail-row">
            <div class="detail-label">Signer Email</div>
            <div class="detail-value" [class.empty]="!selectedContract.email_signataire">
              {{ selectedContract.email_signataire || 'No email provided' }}
            </div>
          </div>

          <div class="detail-row" *ngIf="selectedContract.user_id">
            <div class="detail-label">Associated User</div>
            <div class="detail-value">{{ selectedContract.user_id }}</div>
          </div>
        </div>

        <!-- File Information Section -->
        <div class="detail-group">
          <div class="modal-section-title">File Information</div>

          <div class="detail-row">
            <div class="detail-label">File Type</div>
            <div class="detail-value" [class.empty]="!selectedContract.file_type">
              {{ selectedContract.file_type || 'Unknown' }}
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-label">File Path</div>
            <div class="detail-value" [class.empty]="!selectedContract.file_path">
              {{ selectedContract.file_path || 'Not available' }}
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn-secondary" (click)="closeContractDetails()">
          <i class="fas fa-times"></i> Close
        </button>
        <button class="btn-primary" (click)="downloadContract(selectedContract)">
          <i class="fas fa-download"></i> Download Contract
        </button>
        <button *ngIf="selectedContract.type === 'unsigned'" class="btn-success" (click)="signContract(selectedContract)">
          <i class="fas fa-signature"></i> Sign Contract
        </button>
        <button *ngIf="selectedContract.type === 'unsigned'" class="btn-danger" (click)="deleteContract(selectedContract)">
          <i class="fas fa-trash"></i> Delete Contract
        </button>
      </div>
    </div>
  </div>
    </div>
  </div>
</section>


