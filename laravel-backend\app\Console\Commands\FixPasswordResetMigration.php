<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class FixPasswordResetMigration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:password-reset-migration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix pending password reset migration issue';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking password reset migration status...');

        // Check if the table already exists
        $tableExists = Schema::hasTable('password_reset_tokens');
        $this->info('Password reset tokens table exists: ' . ($tableExists ? 'Yes' : 'No'));

        if ($tableExists) {
            // Check if migration exists in migrations table
            $migrationExists = DB::table('migrations')
                ->where('migration', '2024_04_12_000000_create_password_reset_tokens_table')
                ->exists();

            $this->info('Migration entry exists: ' . ($migrationExists ? 'Yes' : 'No'));

            if (!$migrationExists) {
                // Get the latest batch number
                $latestBatch = DB::table('migrations')->max('batch');
                $newBatch = $latestBatch + 1;

                $this->info('Latest batch: ' . $latestBatch . ', New batch: ' . $newBatch);

                // Insert the migration entry
                try {
                    DB::table('migrations')->insert([
                        'migration' => '2024_04_12_000000_create_password_reset_tokens_table',
                        'batch' => $newBatch
                    ]);

                    $this->info('Successfully added password reset migration to migrations table');
                    Log::info('Password reset migration fixed successfully');
                } catch (\Exception $e) {
                    $this->error('Failed to fix migration: ' . $e->getMessage());
                    Log::error('Failed to fix password reset migration', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else {
                $this->info('Migration entry already exists, no action needed');
            }
        } else {
            $this->error('Password reset tokens table does not exist, please run migrations');
            $this->info('Run: php artisan migrate');
        }

        return 0;
    }
}
