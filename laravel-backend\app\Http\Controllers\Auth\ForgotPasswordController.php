<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class ForgotPasswordController extends Controller
{
    /**
     * Check if a user exists with the given email address.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please enter a valid email address'
            ], 422);
        }

        // Check if user exists
        $user = DB::table('users')->where('email', $request->email)->first();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'We could not find a user with that email address.'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Email found in our records.'
        ]);
    }

    /**
     * Send a reset link to the given user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendResetLinkEmail(Request $request)
    {
        // Log at the beginning for debugging
        Log::info('Password reset request received', ['email' => $request->email]);

        // Check mail configuration
        Log::info('Mail configuration', [
            'driver' => config('mail.default'),
            'host' => config('mail.mailers.smtp.host'),
            'port' => config('mail.mailers.smtp.port'),
            'from_address' => config('mail.from.address'),
            'from_name' => config('mail.from.name'),
        ]);

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            Log::warning('Password reset validation failed', ['errors' => $validator->errors()]);
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Check if user exists
            $user = DB::table('users')->where('email', $request->email)->first();

            if (!$user) {
                Log::warning('Password reset requested for non-existent user', ['email' => $request->email]);
                return response()->json([
                    'success' => false,
                    'message' => 'We could not find a user with that email address.'
                ], 404);
            }

            // Check if password_reset_tokens table exists
            $tableExists = DB::getSchemaBuilder()->hasTable('password_reset_tokens');
            Log::info('Password reset tokens table exists: ' . ($tableExists ? 'true' : 'false'));

            if (!$tableExists) {
                Log::error('password_reset_tokens table does not exist');
                return response()->json([
                    'success' => false,
                    'message' => 'Password reset functionality is not available at this time.'
                ], 500);
            }

            // Generate token
            $token = Str::random(60);

            // Store token in database
            try {
                DB::table('password_reset_tokens')->updateOrInsert(
                    ['email' => $request->email],
                    [
                        'token' => $token,
                        'created_at' => now()
                    ]
                );
                Log::info('Password reset token created successfully', ['email' => $request->email]);
            } catch (\Exception $e) {
                Log::error('Error storing password reset token', [
                    'email' => $request->email,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }

            // Build reset URL (frontend URL)
            $resetUrl = 'http://localhost:4200/reset-password?token=' . $token . '&email=' . urlencode($request->email);
            Log::info('Password reset URL generated', ['url' => $resetUrl]);

            // Send email with reset link using the new template
            try {
                Mail::send('emails.reset-password', ['resetUrl' => $resetUrl], function($message) use ($request) {
                    $message->to($request->email)
                           ->subject('Reset Your Password');
                });
                Log::info('Password reset email sent successfully', ['email' => $request->email]);
            } catch (\Exception $e) {
                Log::error('Error sending password reset email', [
                    'email' => $request->email,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'mail_driver' => config('mail.default'),
                    'mail_host' => config('mail.mailers.smtp.host'),
                    'mail_port' => config('mail.mailers.smtp.port'),
                    'mail_from' => config('mail.from.address'),
                ]);

                // For SMTP connection problems, try to use another transport as a fallback
                try {
                    Log::info('Trying alternative mail transport (log)');
                    \Config::set('mail.default', 'log');

                    Mail::send('emails.reset-password', ['resetUrl' => $resetUrl], function($message) use ($request) {
                        $message->to($request->email)
                               ->subject('Reset Your Password');
                    });

                    Log::info('Password reset email logged successfully with fallback driver');

                    return response()->json([
                        'success' => true,
                        'message' => 'We have created a password reset link. Due to technical issues, please check your application logs or contact support.',
                        'debug_url' => $resetUrl
                    ]);
                } catch (\Exception $fallbackEx) {
                    Log::error('Fallback mail also failed', [
                        'error' => $fallbackEx->getMessage(),
                        'trace' => $fallbackEx->getTraceAsString(),
                    ]);
                    throw $e;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'We have emailed your password reset link!'
            ]);

        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Password reset error', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the password reset link.'
            ], 500);
        }
    }
}
