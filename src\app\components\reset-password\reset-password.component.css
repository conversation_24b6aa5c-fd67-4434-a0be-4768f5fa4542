.component-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.reset-password-container {
  display: flex;
  min-height: calc(100vh - 20px);
  background: #1a1a1a; /* Exact dark gray from the image */
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.reset-password-left {
  flex: 1;
  background:
    linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
    url('/assets/images/signin.jpg') no-repeat center center;
  background-size: cover;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  color: white;
  min-height: 100%;
}

.logo-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: auto;
}

.logo {
  height: 40px;
}

.back-link {
  color: white;
  text-decoration: none;
  font-size: 0.875rem;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.back-link:hover {
  opacity: 1;
}

.hero-content {
  margin-bottom: 2rem;
}

.hero-content h2 {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1rem;
  opacity: 0.8;
  margin-bottom: 2rem;
}

.hero-dots {
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
}

.dot.active {
  background: white;
  width: 24px;
  border-radius: 4px;
}

.reset-password-right {
  flex: 1.5;
  background: #1a1a1a; /* Exact dark gray from the image */
  padding: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-password-form-container {
  width: 100%;
  max-width: 800px;
  color: white;
}

.reset-password-form-container h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.signin-link {
  color: #666;
  margin-bottom: 2rem;
}

.signin-link a {
  color: #4ECDC4; /* Teal/Turquoise color */
  text-decoration: none;
}

.reset-password-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-size: 0.875rem;
  color: #aaa;
}

.password-input {
  position: relative;
  width: 100%;
}

.form-field input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: 0.5rem;
  color: white;
  font-size: 0.875rem;
}

.form-field input:focus {
  outline: none;
  border-color: #4ECDC4; /* Teal/Turquoise color */
}

.form-field input.is-invalid {
  border-color: #ef4444;
}

.toggle-password {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #aaa;
  cursor: pointer;
  font-size: 1rem;
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
}

.password-strength {
  background: #2a2a2a;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 0.5rem;
}

.strength-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.strength-label span:first-child {
  color: #aaa;
}

.strength-meter {
  height: 0.25rem;
  background: #3a3a3a;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1rem;
}

.strength-bar {
  height: 100%;
  width: 0%;
  border-radius: 1rem;
  transition: width 0.3s ease;
}

.strength-bar.weak {
  width: 33%;
  background: #ef4444;
}

.strength-bar.medium {
  width: 66%;
  background: #f59e0b;
}

.strength-bar.strong {
  width: 100%;
  background: #10b981;
}

.password-requirements {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #aaa;
}

.requirement {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.requirement.met {
  color: #10b981;
}

.weak {
  color: #ef4444;
}

.medium {
  color: #f59e0b;
}

.strong {
  color: #10b981;
}

.submit-button, .primary-button {
  padding: 0.75rem;
  border-radius: 0.5rem;
  background: #4ECDC4; /* Teal/Turquoise color */
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.submit-button:hover:not(:disabled), .primary-button:hover {
  background: #3DACA4; /* Darker shade of teal/turquoise */
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.message {
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.message.error {
  background: rgba(220, 38, 38, 0.1);
  color: #ef4444;
  border: 1px solid rgba(220, 38, 38, 0.1);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Success View Styles */
.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
}

.success-icon {
  font-size: 3rem;
  color: #10b981;
  margin-bottom: 1.5rem;
}

.success-container h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.success-message {
  font-size: 1rem;
  color: #10b981;
  margin-bottom: 1rem;
}

.instruction-text {
  color: #aaa;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.primary-button {
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .reset-password-container {
    flex-direction: column;
  }

  .reset-password-left, .reset-password-right {
    flex: none;
  }

  .reset-password-left {
    max-height: 30vh;
  }

  .reset-password-right {
    padding: 2rem;
  }
}
