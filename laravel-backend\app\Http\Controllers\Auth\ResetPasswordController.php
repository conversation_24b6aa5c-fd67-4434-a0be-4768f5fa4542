<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class ResetPasswordController extends Controller
{
    /**
     * Reset the given user's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Find the token record
            $tokenRecord = DB::table('password_reset_tokens')
                ->where('email', $request->email)
                ->where('token', $request->token)
                ->first();

            if (!$tokenRecord) {
                return response()->json([
                    'success' => false,
                    'message' => 'This password reset token is invalid or has expired.'
                ], 400);
            }

            // Check if token is expired (24 hours)
            if (now()->diffInHours($tokenRecord->created_at) > 24) {
                // Remove expired token
                DB::table('password_reset_tokens')
                    ->where('email', $request->email)
                    ->delete();

                return response()->json([
                    'success' => false,
                    'message' => 'This password reset token has expired.'
                ], 400);
            }

            // Find the user
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'We could not find a user with that email address.'
                ], 404);
            }

            // Update the password
            $user->password = Hash::make($request->password);
            $user->setRememberToken(Str::random(60));
            $user->save();

            // Delete the token
            DB::table('password_reset_tokens')
                ->where('email', $request->email)
                ->delete();

            // Fire the password reset event
            event(new PasswordReset($user));

            return response()->json([
                'success' => true,
                'message' => 'Your password has been reset successfully.'
            ]);

        } catch (\Exception $e) {
            // Log the error for debugging
            logger()->error('Password reset error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while resetting your password.'
            ], 500);
        }
    }

    /**
     * Validate a password reset token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateToken(Request $request, $token)
    {
        try {
            $email = $request->query('email');

            if (!$email) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email is required to validate the token.'
                ], 400);
            }

            // Check if token exists and is valid
            $tokenRecord = DB::table('password_reset_tokens')
                ->where('email', $email)
                ->where('token', $token)
                ->first();

            if (!$tokenRecord) {
                return response()->json([
                    'success' => false,
                    'message' => 'This password reset token is invalid.'
                ], 400);
            }

            // Check if token is expired (24 hours)
            if (now()->diffInHours($tokenRecord->created_at) > 24) {
                return response()->json([
                    'success' => false,
                    'message' => 'This password reset token has expired.'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Token is valid',
                'token' => $token
            ]);

        } catch (\Exception $e) {
            // Log the error for debugging
            logger()->error('Token validation error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while validating the token.'
            ], 500);
        }
    }
}
