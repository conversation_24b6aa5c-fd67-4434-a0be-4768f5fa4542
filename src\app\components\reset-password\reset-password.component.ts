import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink]
})
export class ResetPasswordComponent implements OnInit {
  resetPasswordForm!: FormGroup;
  loading = false;
  errorMessage = '';
  successMessage = '';
  resetComplete = false;
  token = '';
  email = '';
  showPassword = false;
  showConfirmPassword = false;
  passwordMismatch = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.getTokenFromRoute();
  }

  initializeForm(): void {
    this.resetPasswordForm = this.fb.group({
      password: ['', [Validators.required, Validators.minLength(8)]],
      passwordConfirmation: ['', [Validators.required]]
    });

    // Check for password match on every change
    this.resetPasswordForm.valueChanges.subscribe(() => {
      this.checkPasswordMatch();
    });
  }

  getTokenFromRoute(): void {
    this.route.queryParams.subscribe(params => {
      if (params['token']) {
        this.token = params['token'];
      } else {
        this.errorMessage = 'Invalid password reset link. Please request a new one.';
      }

      if (params['email']) {
        this.email = params['email'];
      } else {
        this.errorMessage = 'Email address is missing from the reset link. Please request a new one.';
      }
    });
  }

  onSubmit(): void {
    if (this.resetPasswordForm.valid && !this.loading && !this.passwordMismatch) {
      this.loading = true;
      this.errorMessage = '';

      const data = {
        token: this.token,
        email: this.email,
        password: this.resetPasswordForm.value.password,
        password_confirmation: this.resetPasswordForm.value.passwordConfirmation
      };

      this.authService.resetPassword(data).subscribe({
        next: (response) => {
          this.successMessage = response.message || 'Your password has been reset successfully.';
          this.resetComplete = true;
          this.loading = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to reset password. Please try again.';
          this.loading = false;
        }
      });
    }
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  checkPasswordMatch(): void {
    const password = this.resetPasswordForm.get('password')?.value;
    const confirmation = this.resetPasswordForm.get('passwordConfirmation')?.value;

    if (password && confirmation) {
      this.passwordMismatch = password !== confirmation;
    } else {
      this.passwordMismatch = false;
    }
  }

  // Password strength methods
  getPasswordStrength(): string {
    const password = this.resetPasswordForm.get('password')?.value || '';
    if (!password) return '';

    let score = 0;
    if (this.hasMinLength()) score++;
    if (this.hasUppercase()) score++;
    if (this.hasLowercase()) score++;
    if (this.hasNumber()) score++;

    if (score <= 2) return 'Weak';
    if (score === 3) return 'Medium';
    return 'Strong';
  }

  getPasswordStrengthClass(): string {
    const strength = this.getPasswordStrength();
    if (!strength) return '';
    return strength.toLowerCase();
  }

  hasMinLength(): boolean {
    const password = this.resetPasswordForm.get('password')?.value || '';
    return password.length >= 8;
  }

  hasUppercase(): boolean {
    const password = this.resetPasswordForm.get('password')?.value || '';
    return /[A-Z]/.test(password);
  }

  hasLowercase(): boolean {
    const password = this.resetPasswordForm.get('password')?.value || '';
    return /[a-z]/.test(password);
  }

  hasNumber(): boolean {
    const password = this.resetPasswordForm.get('password')?.value || '';
    return /[0-9]/.test(password);
  }

  get password() {
    return this.resetPasswordForm.get('password');
  }

  get passwordConfirmation() {
    return this.resetPasswordForm.get('passwordConfirmation');
  }
}
