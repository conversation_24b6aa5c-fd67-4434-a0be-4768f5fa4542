<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class TestResetPasswordMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The reset URL.
     *
     * @var string
     */
    public $resetUrl;

    /**
     * Create a new message instance.
     */
    public function __construct()
    {
        // Set a sample reset URL for testing
        $this->resetUrl = 'https://example.com/reset-password?token=sample-token-for-testing-the-email-template';
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Test Reset Password Mail',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.reset-password',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
