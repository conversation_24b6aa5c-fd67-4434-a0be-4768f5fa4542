import { AuthService } from './../../services/auth.service';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router, RouterLink, ActivatedRoute } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AuthResponse } from '../../services/auth.service';
import { BrowserStorageService } from '../../services/browser-storage.service';

@Component({
  selector: 'app-signin',
  templateUrl: './sign-in.component.html',
  styleUrls: ['./sign-in.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    MatButtonModule,
    MatIconModule,
  ]
})
export class SigninComponent implements OnInit {
  signinForm: FormGroup;
  showPassword = false;
  message: string = '';
  isLoading = false;
  googleAuthUrl: string = '';
  facebookAuthUrl: string = '';
  returnUrl: string = '/profile';
  contractId: number | null = null;

  constructor(
    private authService: AuthService,
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private storage: BrowserStorageService
  ) {
    this.signinForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      remember: [false]
    });

    // Initialize auth URLs
    const baseUrl = this.authService.getApiUrl();
    this.googleAuthUrl = `${baseUrl}/auth/google`;
    this.facebookAuthUrl = `${baseUrl}/auth/facebook`;

    console.log('Google Auth URL:', this.googleAuthUrl);
    console.log('Facebook Auth URL:', this.facebookAuthUrl);
  }

  ngOnInit(): void {
    // Get return URL and contract ID from route parameters
    this.route.queryParams.subscribe(params => {
      if (params['returnUrl']) {
        this.returnUrl = params['returnUrl'];
        console.log('Return URL set from query params:', this.returnUrl);
      }

      // Support multiple parameter names for contract ID
      const contractId = params['id'] || params['contractId'] || params['unsignedContractId'] || params['documentId'];
      if (contractId) {
        this.contractId = +contractId; // Convert to number
        console.log('Contract ID set from query params:', this.contractId);
      }
    });
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  onSubmit() {
    if (this.signinForm.valid) {
      this.isLoading = true;
      this.message = '';

      const credentials = {
        email: this.signinForm.get('email')?.value,
        password: this.signinForm.get('password')?.value
      };

      console.log('Attempting login with email:', credentials.email);

      // Clear any existing auth data to prevent conflicts
      this.storage.removeItem('user');
      this.storage.removeItem('token');
      console.log('Cleared existing auth data from storage');

      this.authService.login(credentials.email, credentials.password).subscribe({
        next: (response: AuthResponse) => {
          console.log('Login response:', response);

          // Check if we have a valid response with token and user
          if (response && response.token && response.user) {
            console.log('Valid login response with token and user data');

            // Store auth data in storage
            this.storage.setItem('token', response.token);
            this.storage.setItem('user', JSON.stringify(response.user));

            // Display success message
            this.message = 'Login successful!';

            // Navigate to return URL or profile page
            setTimeout(() => {
              if (this.returnUrl && this.returnUrl !== '/signin') {
                console.log('Navigating to return URL:', this.returnUrl);
                this.router.navigateByUrl(this.returnUrl);
              } else {
                console.log('Navigating to default profile page');
                this.router.navigate(['/profile']);
              }
            }, 500);
          } else {
            console.error('Invalid login response:', response);
            this.message = 'Invalid response from server';
            this.isLoading = false;
          }
        },
        error: (error) => {
          console.error('Login error:', error);
          if (error.status === 422) {
            this.message = error.error?.errors?.email?.[0] || 'Invalid credentials';
          } else if (error.status === 401) {
            this.message = 'Email or password incorrect';
          } else {
            this.message = 'An error occurred during login';
          }
          this.isLoading = false;
        },
        complete: () => {
          this.isLoading = false;
        }
      });
    }
  }

  onGoogleSignIn() {
    this.isLoading = true;
    this.message = '';

    // Define storage keys
    const STORAGE_KEYS = {
      USER: 'user',
      TOKEN: 'token',
      AUTH_REDIRECT: 'auth_redirect'
    };

    // Store the return URL to redirect back after authentication
    this.storage.setItem(STORAGE_KEYS.AUTH_REDIRECT, this.returnUrl || '/profile');
    console.log('Stored redirect URL for after auth:', this.returnUrl || '/profile');

    // Store contract ID if available
    if (this.contractId) {
      this.storage.setItem('contract_id', this.contractId.toString());
      console.log('Stored contract ID for after auth:', this.contractId);
    }

    // Clear any existing auth data to prevent conflicts
    this.storage.removeItem(STORAGE_KEYS.USER);
    console.log('Cleared existing user data from storage');

    // Get the base URL and redirect directly
    const baseUrl = this.authService.getApiUrl();
    const googleAuthUrl = `${baseUrl}/auth/google`;

    console.log('Redirecting directly to Google auth URL:', googleAuthUrl);
    window.location.href = googleAuthUrl;
  }

  onFacebookSignIn() {
    this.isLoading = true;
    this.message = '';

    // Define storage keys
    const STORAGE_KEYS = {
      USER: 'user',
      TOKEN: 'token',
      AUTH_REDIRECT: 'auth_redirect'
    };

    // Store the return URL to redirect back after authentication
    this.storage.setItem(STORAGE_KEYS.AUTH_REDIRECT, this.returnUrl || '/profile');
    console.log('Stored redirect URL for after auth:', this.returnUrl || '/profile');

    // Store contract ID if available
    if (this.contractId) {
      this.storage.setItem('contract_id', this.contractId.toString());
      console.log('Stored contract ID for after auth:', this.contractId);
    }

    // Clear any existing auth data to prevent conflicts
    this.storage.removeItem(STORAGE_KEYS.USER);
    this.storage.removeItem(STORAGE_KEYS.TOKEN);
    console.log('Cleared existing auth data from storage');

    // Get the base URL and redirect directly
    const baseUrl = this.authService.getApiUrl();

    // Add state parameter to identify Facebook callback
    const state = 'facebook';
    this.storage.setItem('facebook_state', state);
    const facebookAuthUrl = `${baseUrl}/auth/facebook?state=${state}`;

    console.log('Redirecting directly to Facebook auth URL:', facebookAuthUrl);
    window.location.href = facebookAuthUrl;
  }
}
