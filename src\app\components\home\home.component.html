<!-- Home content is now wrapped by the shared container in app.component.html -->
<div class="home-container">
  <section class="hero">
    <div class="hero-overlay"></div>
    <div class="hero-content">
      <div class="award-badge"><i class="material-icons">description</i> Smart Contract Management</div>
      <h1>Streamline Your <span class="highlight">Contract</span> Workflow</h1>
      <p>Simplify document management with our powerful contract platform. Upload, sign, and manage all your contracts in one secure place.</p>
      <div class="cta-buttons">
        <button class="btn-primary" (click)="goToUpload()">
          <i class="material-icons">cloud_upload</i> Upload Documents
        </button>
        <button class="btn-outline" (click)="navigateTo('/contact')">
          <i class="material-icons">mail_outline</i> Contact Us
        </button>
      </div>
    </div>
    <div class="hero-shape"></div>
  </section>

  <div class="section-header">
    <h2>Our <span class="highlight">Features</span></h2>
    <p>Comprehensive contract management tools to streamline your workflow</p>
  </div>

  <section class="features">
    <div class="feature-card" (click)="goToUpload()">
      <div class="icon-container">
        <i class="material-icons">upload_file</i>
      </div>
      <h3>Document Upload</h3>
      <p>Easily upload and store your contracts in a secure cloud environment</p>
      <a class="feature-link">Get Started <i class="material-icons">arrow_forward</i></a>
    </div>
    <div class="feature-card" (click)="goToSignature()">
      <div class="icon-container">
        <i class="material-icons">draw</i>
      </div>
      <h3>Electronic Signature</h3>
      <p>Sign documents electronically with legally binding signatures</p>
      <a class="feature-link">Try Now <i class="material-icons">arrow_forward</i></a>
    </div>
    <div class="feature-card" (click)="goToDashboard()">
      <div class="icon-container">
        <i class="material-icons">dashboard</i>
      </div>
      <h3>Contract Dashboard</h3>
      <p>Monitor all your contracts with a comprehensive dashboard view</p>
      <a class="feature-link">View Dashboard <i class="material-icons">arrow_forward</i></a>
    </div>
    <div class="feature-card" (click)="navigateTo('/contact')">
      <div class="icon-container">
        <i class="material-icons">support_agent</i>
      </div>
      <h3>24/7 Support</h3>
      <p>Get help whenever you need it with our dedicated support team</p>
      <a class="feature-link">Contact Support <i class="material-icons">arrow_forward</i></a>
    </div>
  </section>

  <!-- How It Works Section -->
  <section class="workflow-section">
    <div class="section-header">
      <h2>How It <span class="highlight">Works</span></h2>
      <p>Our simple three-step process makes contract management effortless</p>
    </div>

    <div class="workflow-steps">
      <div class="workflow-step">
        <div class="step-number">1</div>
        <div class="step-icon"><i class="material-icons">cloud_upload</i></div>
        <h3>Upload</h3>
        <p>Upload your documents to our secure platform</p>
      </div>
      <div class="workflow-connector"></div>
      <div class="workflow-step">
        <div class="step-number">2</div>
        <div class="step-icon"><i class="material-icons">edit_document</i></div>
        <h3>Process</h3>
        <p>Our system extracts key information automatically</p>
      </div>
      <div class="workflow-connector"></div>
      <div class="workflow-step">
        <div class="step-number">3</div>
        <div class="step-icon"><i class="material-icons">draw</i></div>
        <h3>Sign</h3>
        <p>Electronically sign and share with all parties</p>
      </div>
    </div>
  </section>

  <section class="content-section">
    <div class="content-text">
      <span class="section-tag">Why Choose Us</span>
      <h2>Why Choose <span class="highlight">Our Platform</span>?</h2>
      <p>Our contract management solution offers unparalleled features designed to streamline your document workflow.</p>
      <ul class="benefits-list">
        <li>
          <div class="benefit-icon"><i class="material-icons">security</i></div>
          <div class="benefit-text">
            <strong>Enterprise-Grade Security</strong> for all your sensitive documents
          </div>
        </li>
        <li>
          <div class="benefit-icon"><i class="material-icons">speed</i></div>
          <div class="benefit-text">
            <strong>Fast Processing</strong> with automated data extraction
          </div>
        </li>
        <li>
          <div class="benefit-icon"><i class="material-icons">integration_instructions</i></div>
          <div class="benefit-text">
            <strong>Seamless Integration</strong> with your existing workflows
          </div>
        </li>
        <li>
          <div class="benefit-icon"><i class="material-icons">analytics</i></div>
          <div class="benefit-text">
            <strong>Comprehensive Analytics</strong> to track document status
          </div>
        </li>
        <li>
          <div class="benefit-icon"><i class="material-icons">devices</i></div>
          <div class="benefit-text">
            <strong>Multi-Device Support</strong> for access anywhere, anytime
          </div>
        </li>
      </ul>
      <button class="btn-primary" (click)="isLoggedIn ? goToDashboard() : navigateTo('/signup')">
        {{ isLoggedIn ? 'Go to Dashboard' : 'Sign Up Now' }}
      </button>
    </div>
    <div class="content-image">
      <div class="image-container">
        <img src="assets/images/contract-document.svg" alt="Contract Management Platform">
        <div class="image-overlay"></div>
      </div>
    </div>
  </section>



  <section class="cta-section">
    <div class="cta-content">
      <h2>Ready to Streamline Your Contract Management?</h2>
      <p>Join thousands of satisfied users who have transformed their document workflows.</p>
      <div class="cta-buttons">
        <button class="btn-primary" (click)="isLoggedIn ? goToUpload() : navigateTo('/signup')">
          {{ isLoggedIn ? 'Upload Documents' : 'Get Started Free' }}
        </button>
        <button class="btn-outline" (click)="navigateTo('/contact')">
          <i class="material-icons">help_outline</i> Learn More
        </button>
      </div>
    </div>
  </section>
</div>
