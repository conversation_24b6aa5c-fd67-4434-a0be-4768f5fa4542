// src/app/models/document-generation.model.ts

/**
 * Interface for document generation request
 */
export interface DocumentGenerationRequest {
  file_path?: string;
  filename?: string;
  replacements: Record<string, string>;
  document_name?: string;
}

/**
 * Interface for document generation response
 */
export interface DocumentGenerationResponse {
  success: boolean;
  message: string;
  file?: {
    name: string;
    path: string;
    download_url?: string;
    unsigned_contract_id?: number;
  };
  errors?: Record<string, string[]>;
}

/**
 * Interface for generated document
 */
export interface GeneratedDocument {
  id: number;
  name: string;
  original_file_path: string;
  generated_file_path: string;
  file_type: string;
  replacements: Record<string, string>;
  user_id: number | null;
  status: string;
  created_at: string;
  updated_at: string;
}
