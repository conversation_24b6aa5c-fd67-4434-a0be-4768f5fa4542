import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class IsAdminGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    console.log('IsAdminGuard: Checking if user is admin');

    // Force a check of the current authentication state
    this.authService.isLoggedIn();

    return this.authService.currentUser$.pipe(
      take(1),
      map(user => {
        console.log('IsAdminGuard: Current user:', user);

        // Check if user is logged in and is an admin
        const isAdmin = user && user.is_admin === true;
        console.log('IsAdminGuard: Is admin?', isAdmin);

        if (!isAdmin) {
          console.log('Access denied - Admin privileges required');
          // Show an alert to the user
          if (user) {
            alert('You need admin privileges to access this page. Please contact the administrator.');
          } else {
            alert('You need to be logged in with admin privileges to access this page.');
          }
          this.router.navigate(['/dashboard']);
          return false;
        }

        console.log('Access granted - User has admin privileges');
        return true;
      })
    );
  }
}
