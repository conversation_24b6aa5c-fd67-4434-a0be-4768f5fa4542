/* Home Styles - Improved */
:host {
  display: block;
  width: 100%;
  --primary: #4ECDC4;
  --primary-light: #6CDED6;
  --primary-dark: #3DACA4;
  --border-radius: 16px;
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Theme-adaptive variables */
:host {
  --home-bg: var(--background, #ffffff);
  --home-card-bg: var(--surface, #f8fafc);
  --home-text-primary: var(--text-primary, #1e293b);
  --home-text-secondary: var(--text-secondary, #64748b);
  --home-text-light: var(--text-light, #94a3b8);
  --home-border-color: rgba(0, 0, 0, 0.1);
  --home-hero-bg: #f8fafc;
  --home-hero-overlay: rgba(78, 205, 196, 0.1);
}

body.dark-mode :host {
  --home-bg: #121212;
  --home-card-bg: #252525;
  --home-text-primary: #ffffff;
  --home-text-secondary: #cccccc;
  --home-text-light: #94a3b8;
  --home-border-color: rgba(255, 255, 255, 0.1);
  --home-hero-bg: #1a1a1a;
  --home-hero-overlay: rgba(78, 205, 196, 0.3);
}

.home-card {
  width: 100%;
  background-color: var(--home-card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 24px;
  transition: all var(--transition-normal);
  border: 1px solid var(--home-border-color);
  color: var(--home-text-primary);
  cursor: pointer;
}

.home-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-3px);
}

/* Component Container */
.component-container {
  width: 100%;
  background-color: var(--home-card-bg);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
  transition: all 0.3s ease;
  color: var(--home-text-primary);
}

/* Home Container */
.home-container {
  max-width: 1600px; /* Increased from 1200px */
  margin: 0 auto;
  padding: 30px; /* Increased from 20px */
}

/* Hero Section */
.hero {
  background-color: var(--home-hero-bg);
  color: var(--home-text-primary);
  padding: 120px 20px;
  text-align: center;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  margin-bottom: 80px;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, var(--home-hero-overlay), transparent 60%);
  z-index: 1;
}

.hero-shape {
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(78, 205, 196, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content {
  max-width: 1000px; /* Increased from 800px */
  margin: 0 auto;
}

.award-badge {
  background: linear-gradient(135deg, #4ECDC4, #3DACA4); /* Teal/Turquoise colors */
  display: inline-flex;
  align-items: center;
  padding: 0.875rem 1.5rem;
  border-radius: 50px;
  margin-bottom: 30px;
  box-shadow: 0 8px 20px rgba(78, 205, 196, 0.4); /* Teal/Turquoise with opacity */
  font-weight: 600;
  letter-spacing: 0.5px;
  transform: translateY(-20px);
  animation: fadeInDown 0.8s ease-out forwards;
}

.award-badge i {
  margin-right: 8px;
  font-size: 1.2rem;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

h1 {
  font-size: 3.5em;
  color: var(--home-text-primary);
  margin-bottom: 25px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  animation: fadeIn 1s ease-out 0.3s forwards;
  opacity: 0;
  line-height: 1.2;
}

.highlight {
  color: #4ECDC4; /* Teal/Turquoise color */
  position: relative;
  display: inline-block;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(78, 205, 196, 0.3); /* Teal/Turquoise with opacity */
  z-index: -1;
  transform: skewX(-15deg);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

p {
  font-size: 1.25rem;
  margin: 1rem 0 2.5rem;
  line-height: 1.6;
  color: var(--home-text-secondary);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  animation: fadeIn 1s ease-out 0.6s forwards;
  opacity: 0;
}

.cta-buttons {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 20px;
  animation: fadeIn 1s ease-out 0.9s forwards;
  opacity: 0;
}

.btn-primary {
  background-color: #4ECDC4; /* Teal/Turquoise color */
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3); /* Teal/Turquoise with opacity */
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 180px;
}

.btn-primary i {
  margin-right: 8px;
  font-size: 1.2rem;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4ECDC4, #3DACA4); /* Teal/Turquoise colors */
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.5); /* Teal/Turquoise with opacity */
}

.btn-primary:hover::after {
  transform: translateX(100%);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3); /* Teal/Turquoise with opacity */
}

.btn-outline {
  background: transparent;
  border: 2px solid #4ECDC4; /* Teal/Turquoise color */
  color: var(--home-text-primary);
  padding: 15px 30px;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 180px;
}

.btn-outline i {
  margin-right: 8px;
  font-size: 1.2rem;
}

.btn-outline:hover {
  background: rgba(78, 205, 196, 0.1); /* Teal/Turquoise with opacity */
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.3); /* Teal/Turquoise with opacity */
}

.btn-outline:active {
  transform: translateY(0);
  box-shadow: 0 4px 8px rgba(78, 205, 196, 0.15); /* Teal/Turquoise with opacity */
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 2.5rem;
  color: var(--home-text-primary);
  margin-bottom: 15px;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--home-text-secondary);
  max-width: 700px;
  margin: 0 auto;
}

.section-tag {
  display: inline-block;
  background-color: rgba(78, 205, 196, 0.1);
  color: #4ECDC4;
  padding: 8px 16px;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Features Section */
.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  padding: 40px 0 80px;
}

.feature-card {
  text-align: center;
  padding: 3rem 2rem;
  background-color: var(--home-card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--home-border-color);
  transition: all var(--transition-normal);
  color: var(--home-text-primary);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #4ECDC4, #3DACA4);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.icon-container {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: rgba(78, 205, 196, 0.1); /* Teal/Turquoise with opacity */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  position: relative;
  transition: all 0.3s ease;
}

.icon-container::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px dashed rgba(78, 205, 196, 0.3);
  animation: spin 20s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.feature-card:hover .icon-container {
  transform: scale(1.1);
  background: rgba(78, 205, 196, 0.2);
}

.feature-card i {
  font-size: 40px;
  color: #4ECDC4; /* Teal/Turquoise color */
}

.feature-card h3 {
  color: var(--home-text-primary);
  margin-bottom: 15px;
  font-size: 1.5rem;
  font-weight: 600;
}

.feature-card p {
  color: var(--home-text-secondary);
  line-height: 1.6;
  font-size: 1rem;
  margin-bottom: 25px;
}

.feature-link {
  color: #4ECDC4;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  margin-top: auto;
  transition: all 0.3s ease;
}

.feature-link i {
  font-size: 1rem;
  margin-left: 5px;
  transition: transform 0.3s ease;
}

.feature-link:hover {
  color: white;
}

.feature-link:hover i {
  transform: translateX(5px);
}

/* Content Section */
.content-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 4rem; /* Increased from 3rem */
  padding: 7rem 0; /* Increased from 6rem */
  width: 100%;
  max-width: 1600px; /* Increased from 1200px */
  margin: 0 auto;
  position: relative;
}

.content-section::before {
  content: '';
  position: absolute;
  left: -200px;
  top: 50%;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(78, 205, 196, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
  transform: translateY(-50%);
}

.content-text {
  max-width: 600px;
  position: relative;
  z-index: 1;
}

.content-text h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--home-text-primary);
}

.content-text p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  color: var(--home-text-secondary);
}

.benefits-list {
  list-style-type: none;
  padding: 0;
  margin: 30px 0;
}

.benefits-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 15px;
  background-color: rgba(78, 205, 196, 0.1);  border-radius: 10px;
  transition: all 0.3s ease;
}

.benefits-list li:hover {
  transform: translateX(10px);
}

.benefit-icon {
  background-color: rgba(78, 205, 196, 0.2);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.benefit-icon i {
  color: #4ECDC4;
  font-size: 1.2rem;
}

.benefit-text {
  color: var(--home-text-secondary);
  font-size: 1rem;
  line-height: 1.5;
}

.benefit-text strong {
  color: var(--home-text-primary);
  display: block;
  margin-bottom: 3px;
}

.content-image {
  flex: 1;
  position: relative;
  z-index: 1;
}

.image-container {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  transform: perspective(1000px) rotateY(-5deg);
  transition: all 0.5s ease;
}

.image-container:hover {
  transform: perspective(1000px) rotateY(0deg);
}

.content-image img {
  max-width: 100%;
  height: auto;
  display: block;
  transition: all 0.5s ease;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.3) 0%, transparent 100%);
  z-index: 1;
}

/* Partnership Section */
.partnership-section {
  background: var(--home-hero-bg);
  color: var(--home-text-primary);
  padding: 8rem 0;
  text-align: center;
  width: 100%;
  position: relative;
  overflow: hidden;
  margin-top: 80px;
  border-radius: 16px;
}

.partnership-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(78, 205, 196, 0.2) 0%, transparent 70%);
  z-index: 1;
}

.section-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.partnership-section h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: var(--home-text-primary);
}

.partnership-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2.5rem; /* Increased from 2rem */
  max-width: 1600px; /* Increased from 1200px */
  margin: 40px auto 0;
}

.partnership-stat {
  background: var(--home-card-bg);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.partnership-stat::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #4ECDC4, transparent);
}

.partnership-stat:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(78, 205, 196, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.stat-icon i {
  font-size: 28px;
  color: #4ECDC4;
}

.stat-number {
  font-size: 3.5rem;
  font-weight: bold;
  color: #4ECDC4; /* Teal/Turquoise color */
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(78, 205, 196, 0.2); /* Teal/Turquoise with opacity */
  position: relative;
  display: inline-block;
}

.stat-number::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  height: 10px;
  background-color: rgba(78, 205, 196, 0.1);
  z-index: -1;
  transform: skewX(-15deg);
}

.stat-label {
  font-size: 1.25rem;
  color: var(--home-text-secondary);
  font-weight: 500;
}

/* Workflow Section */
.workflow-section {
  padding: 80px 0;
  position: relative;
  overflow: hidden;
  margin: 60px 0;
}

.workflow-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 60px;
  padding: 0 40px;
}

.workflow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
  flex: 1;
  max-width: 250px;
}

.step-number {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: var(--text-white);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.step-icon {
  width: 80px;
  height: 80px;
  background-color: rgba(78, 205, 196, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.step-icon i {
  font-size: 36px;
  color: var(--primary);
}

.workflow-step h3 {
  color: var(--home-text-primary);
  margin-bottom: 10px;
  font-size: 1.4rem;
  font-weight: 600;
}

.workflow-step p {
  color: var(--home-text-secondary);
  font-size: 1rem;
  max-width: 200px;
  margin: 0 auto;
}

.workflow-connector {
  height: 3px;
  background: linear-gradient(90deg, var(--primary-light), var(--primary-dark));
  flex-grow: 1;
  margin: 0 15px;
  position: relative;
  top: -50px;
  max-width: 100px;
}

.workflow-connector::before,
.workflow-connector::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--primary);
  top: 50%;
  transform: translateY(-50%);
}

.workflow-connector::before {
  left: 0;
}

.workflow-connector::after {
  right: 0;
}



/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.9), rgba(61, 172, 164, 0.9));
  border-radius: 16px;
  padding: 80px 40px;
  margin: 80px 0 40px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.cta-section h2 {
  font-size: 2.5rem;
  color: white;
  margin-bottom: 20px;
}

.cta-section p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
}

.cta-section .cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .partnership-grid {
    grid-template-columns: repeat(2, 1fr);
  }


}

@media (max-width: 992px) {
  .content-section {
    flex-direction: column;
    gap: 3rem;
    padding: 5rem 0;
  }

  .content-text {
    max-width: 100%;
    text-align: center;
  }

  .benefits-list li {
    justify-content: center;
    text-align: left;
  }

  .image-container {
    transform: none;
    max-width: 600px;
    margin: 0 auto;
  }

  .workflow-steps {
    flex-direction: column;
    gap: 40px;
  }

  .workflow-connector {
    width: 3px;
    height: 40px;
    margin: 0;
    top: 0;
  }

  .workflow-step {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5em;
  }

  .hero {
    padding: 80px 20px;
  }

  .features {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin: 0 auto;
  }

  .partnership-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
    margin: 40px auto 0;
  }



  .cta-section {
    padding: 60px 20px;
  }

  .cta-section h2 {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .btn-primary, .btn-outline {
    width: 100%;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 2em;
  }

  .award-badge {
    font-size: 0.9rem;
    padding: 0.7rem 1.2rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .feature-card {
    padding: 2rem 1.5rem;
  }

  .workflow-step p {
    max-width: 100%;
  }


}
