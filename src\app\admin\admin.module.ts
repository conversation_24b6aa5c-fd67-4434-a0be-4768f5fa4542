import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { PartnerManagementComponent } from './partner-management/partner-management.component';
import { ContractManagementComponent } from './contract-management/contract-management.component';

// Admin routes
const routes: Routes = [
  { path: 'partners', component: PartnerManagementComponent },
  { path: 'contracts/:partnerId', component: ContractManagementComponent },
  { path: '', redirectTo: 'partners', pathMatch: 'full' }
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    MatSnackBarModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule,
    PartnerManagementComponent,
    ContractManagementComponent
  ]
})
export class AdminModule { }
