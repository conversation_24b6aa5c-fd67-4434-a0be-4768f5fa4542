import { TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { UploadFileComponent } from './upload-file/upload-file.component';
import { of } from 'rxjs';
import { CommonModule } from '@angular/common';

describe('AppComponent', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, UploadFileComponent], // Use imports instead of declarations
      providers: [
        {
          provide: 'BackendService', // Add the missing provider token
          useValue: {
            getBackendMessage: () => of({ message: 'Hello from the backend!' })  // Mocked backend message
          }
        }
      ]
    }).compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });
});
