.signin-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* Left Side - Magenta Section */
.signin-left {
  flex: 1;
  background: #4ECDC4; /* Teal/Turquoise color */
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: white;
  position: relative;
  overflow: hidden;
}

/* Professional contract-themed overlay */
.signin-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M0 0h20L0 20z'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
}

/* Document-like pattern overlay */
.signin-left::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20.83l2.83-2.83 1.41 1.41L1.41 22H0v-1.17zM0 3.07l2.83-2.83 1.41 1.41L1.41 4.24H0V3.07zm28.24 35.52l1.41-1.41 2.83 2.83V40h-1.41l-2.83-2.83zm14.18-14.17l1.41-1.41 2.83 2.83V26h-1.41l-2.83-2.83zM3.07 0l1.41 1.41-2.83 2.83H.24V3.07L3.07 0zm39.17 0l-1.41 1.41 2.83 2.83h1.17V3.07L42.24 0zm-16.93 0l-1.41 1.41 2.83 2.83h1.17V3.07L25.31 0zM20.83 0l-1.41 1.41 2.83 2.83h1.17V3.07L20.83 0zm-16.93 0l-1.41 1.41 2.83 2.83h1.17V3.07L3.9 0zM40 14.18V16h-1.17l-2.83-2.83 1.41-1.41L40 14.18zm0-11.11V4.24h-1.17l-2.83-2.83 1.41-1.41L40 3.07zm0 16.93v1.17h-1.17l-2.83-2.83 1.41-1.41L40 20zm0 16.93v1.17h-1.17l-2.83-2.83 1.41-1.41L40 36.93zM26 40h1.17l2.83-2.83-1.41-1.41L26 38.59V40zm-11.17 0h1.17l2.83-2.83-1.41-1.41-2.59 2.59V40zM14.18 40h1.17l2.83-2.83-1.41-1.41-2.59 2.59V40zM3.07 40h1.17l2.83-2.83-1.41-1.41L3.07 38.34V40z'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.1;
}

/* Position content above the background patterns */
.signin-left .logo-section,
.signin-left .hero-content {
  position: relative;
  z-index: 2;
}

.logo-section {
  margin-bottom: 2rem;
}

.logo {
  height: 40px;
}

.back-link {
  display: block;
  color: white;
  text-decoration: none;
  margin-top: 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.back-link:hover {
  opacity: 1;
  text-decoration: underline;
}

.hero-content {
  margin-top: auto;
  margin-bottom: 3rem;
  position: relative;
}

.hero-content h2 {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.5px;
}

.hero-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.5;
  max-width: 400px;
  margin-bottom: 2rem;
}

.contract-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 5rem;
  opacity: 0.2;
  transform: rotate(10deg);
}

/* Right Side - Dark Section */
.signin-right {
  flex: 1;
  background-color: var(--background);
  color: var(--text);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.signin-form-container {
  width: 100%;
  max-width: 400px; /* More reasonable form width */
  color: var(--text);
  padding: 2rem;
}

.signin-form-container h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.signup-link {
  color: var(--textSecondary);
  margin-bottom: 2.5rem;
  font-size: 0.95rem;
}

.signup-link a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.signup-link a:hover {
  text-decoration: underline;
}

.signin-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  position: relative;
}

.form-field input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: var(--elevation2);
  color: var(--text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-field input:focus {
  outline: none;
  border-color: #4ECDC4; /* Teal/Turquoise color */
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.25);
}

.password-field {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--textSecondary);
  cursor: pointer;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--textSecondary);
  cursor: pointer;
}

.remember-me input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
}

.remember-me span {
  margin-left: 0.5rem;
}

.forgot-password {
  color: #7c3aed;
  text-decoration: none;
  font-size: 0.875rem;
}

.forgot-password-link {
  color: var(--primary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.forgot-password-link:hover {
  text-decoration: underline;
}

.form-group {
  margin-top: 1.5rem;
}

.form-group button {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.form-group button:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.form-group button:disabled {
  background-color: var(--primary-light);
  opacity: 0.5;
  cursor: not-allowed;
}

.message {
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.message.error {
  background: rgba(220, 38, 38, 0.1);
  color: #ef4444;
  border: 1px solid rgba(220, 38, 38, 0.1);
}

.message.success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.1);
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.3rem;
  margin-left: 0.5rem;
}

.form-field input.ng-invalid.ng-touched {
  border-color: #ef4444;
}

/* Social login styles */
.social-login-divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 1.5rem 0;
  color: var(--textSecondary);
  position: relative;
}

.social-login-divider::before,
.social-login-divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid var(--border);
}

.social-login-divider span {
  padding: 0 10px;
  font-size: 0.875rem;
}

.social-login-buttons, .social-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  margin-top: 1rem;
  width: 100%;
}

.social-login-buttons button, .social-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.875rem;
  background-color: var(--elevation2);
  border: 1px solid var(--border);
  border-radius: 8px;
  color: var(--text);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-weight: 500;
  position: relative;
}

.social-login-buttons button:hover:not(:disabled),
.social-login-buttons a.social-btn:hover,
.social-button:hover {
  background-color: var(--surfaceHover);
}

.social-login-buttons button:active,
.social-login-buttons a.social-btn:active,
.social-button:active {
  transform: translateY(1px);
}

.social-login-buttons button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.social-login-buttons img, .social-buttons img {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  border-radius: 3%;
}

.social-button.google, .google-btn {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-color: rgba(var(--primary-rgb), 0.3);
  color: var(--primary);
}

.social-button.google:hover, .google-btn:hover {
  background-color: rgba(var(--primary-rgb), 0.2);
}

.social-button.facebook, .facebook-btn {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-color: rgba(var(--primary-rgb), 0.3);
  color: var(--primary);
}

.social-button.facebook:hover, .facebook-btn:hover {
  background-color: rgba(var(--primary-rgb), 0.2);
}
