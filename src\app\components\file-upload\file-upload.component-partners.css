/* Partners Card Styles */
.partners-card {
  margin-bottom: 2rem;
}

.partners-container {
  padding: 1rem 0;
}

.partners-description {
  margin-bottom: 1.5rem;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.partner-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.partner-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.25rem;
  border-radius: 8px;
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.partner-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: #d1d1d1;
}

.partner-item.selected {
  border-color: var(--primary);
  background-color: rgba(var(--primary-rgb, 78, 205, 196), 0.05);
  box-shadow: 0 5px 15px rgba(var(--primary-rgb, 78, 205, 196), 0.1);
}

.partner-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.partner-icon img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.partner-icon i {
  font-size: 2.5rem;
  color: var(--text-secondary);
}

.partner-details {
  text-align: center;
  width: 100%;
}

.partner-name {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--text);
}

.partner-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Contract section styles */
.contracts-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border);
}

.contracts-heading {
  font-size: 1.15rem;
  color: var(--text);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contracts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contract-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.contract-item:hover {
  background-color: var(--surface-hover);
  border-color: #d1d1d1;
}

.contract-item.selected {
  border-color: var(--primary);
  background-color: rgba(var(--primary-rgb, 78, 205, 196), 0.05);
}

.contract-icon {
  font-size: 1.75rem;
  color: var(--text-secondary);
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
}

.contract-details {
  flex: 1;
}

.contract-name {
  font-weight: 600;
  font-size: 1rem;
  margin: 0 0 0.25rem 0;
  color: var(--text);
}

.contract-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
}

.contract-type {
  display: inline-block;
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.contract-type.pdf {
  background-color: var(--error-light);
  color: var(--error);
}

.contract-type.word {
  background-color: var(--info-light);
  color: var(--info);
}

/* Loading and empty states */
.loading-partners,
.no-partners,
.loading-contracts,
.no-contracts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.loading-partners i,
.no-partners i,
.loading-contracts i,
.no-contracts i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .partner-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .partner-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
}

@media (max-width: 576px) {
  .partner-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .partner-icon {
    width: 50px;
    height: 50px;
  }
  
  .partner-icon i {
    font-size: 2rem;
  }
  
  .partner-name {
    font-size: 1rem;
  }
}
