import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ContactService } from '../../../services/contact.service';
import { FormsModule } from '@angular/forms';

interface ContactMessage {
  id: number;
  name: string;
  email: string;
  subject: string;
  message: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.css']
})
export class NotificationsComponent implements OnInit {
  messages: ContactMessage[] = [];
  filteredMessages: ContactMessage[] = [];
  isLoading = true;
  errorMessage: string | null = null;
  successMessage: string | null = null;
  searchTerm = '';
  filterOption = 'all'; // 'all', 'read', 'unread'
  sortOption = 'newest'; // 'newest', 'oldest'
  selectedMessage: ContactMessage | null = null;

  constructor(private contactService: ContactService) { }

  ngOnInit(): void {
    this.loadMessages();
  }

  loadMessages(): void {
    this.isLoading = true;
    this.errorMessage = null;

    this.contactService.getContactMessages().subscribe({
      next: (response) => {
        this.messages = response.data;
        this.applyFiltersAndSort();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading messages:', error);
        this.errorMessage = error.message || 'Failed to load messages. Please try again.';
        this.isLoading = false;
      }
    });
  }

  applyFiltersAndSort(): void {
    // Apply filter
    if (this.filterOption === 'all') {
      this.filteredMessages = [...this.messages];
    } else if (this.filterOption === 'read') {
      this.filteredMessages = this.messages.filter(msg => msg.is_read);
    } else if (this.filterOption === 'unread') {
      this.filteredMessages = this.messages.filter(msg => !msg.is_read);
    }

    // Apply search
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      this.filteredMessages = this.filteredMessages.filter(msg =>
        msg.name.toLowerCase().includes(term) ||
        msg.email.toLowerCase().includes(term) ||
        msg.subject.toLowerCase().includes(term) ||
        msg.message.toLowerCase().includes(term)
      );
    }

    // Apply sort
    if (this.sortOption === 'newest') {
      this.filteredMessages.sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    } else if (this.sortOption === 'oldest') {
      this.filteredMessages.sort((a, b) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
    }
  }

  onFilterChange(): void {
    this.applyFiltersAndSort();
  }

  onSortChange(): void {
    this.applyFiltersAndSort();
  }

  onSearch(): void {
    this.applyFiltersAndSort();
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.filterOption = 'all';
    this.sortOption = 'newest';
    this.applyFiltersAndSort();
  }

  viewMessage(message: ContactMessage): void {
    this.selectedMessage = message;

    // If message is unread, mark it as read
    if (!message.is_read) {
      this.markAsRead(message);
    }
  }

  closeMessageView(): void {
    this.selectedMessage = null;
  }

  markAsRead(message: ContactMessage): void {
    if (message.is_read) return;

    this.contactService.markAsRead(message.id).subscribe({
      next: () => {
        message.is_read = true;
        // Update the original message in the messages array
        const index = this.messages.findIndex(m => m.id === message.id);
        if (index !== -1) {
          this.messages[index].is_read = true;
        }
      },
      error: (error) => {
        console.error('Error marking message as read:', error);
      }
    });
  }

  markAllAsRead(): void {
    this.contactService.markAllAsRead().subscribe({
      next: () => {
        // Update all messages to read
        this.messages.forEach(msg => msg.is_read = true);
        this.filteredMessages.forEach(msg => msg.is_read = true);

        this.successMessage = 'All messages marked as read';
        setTimeout(() => this.successMessage = null, 3000);
      },
      error: (error) => {
        console.error('Error marking all messages as read:', error);
        this.errorMessage = error.message || 'Failed to mark all messages as read';
        setTimeout(() => this.errorMessage = null, 3000);
      }
    });
  }

  deleteMessage(message: ContactMessage, event?: Event): void {
    if (event) {
      event.stopPropagation(); // Prevent opening the message when clicking delete
    }

    if (confirm('Are you sure you want to delete this message?')) {
      this.contactService.deleteMessage(message.id).subscribe({
        next: () => {
          // Remove from arrays
          this.messages = this.messages.filter(m => m.id !== message.id);
          this.filteredMessages = this.filteredMessages.filter(m => m.id !== message.id);

          // Close message view if the deleted message was selected
          if (this.selectedMessage && this.selectedMessage.id === message.id) {
            this.selectedMessage = null;
          }

          this.successMessage = 'Message deleted successfully';
          setTimeout(() => this.successMessage = null, 3000);
        },
        error: (error) => {
          console.error('Error deleting message:', error);
          this.errorMessage = error.message || 'Failed to delete message';
          setTimeout(() => this.errorMessage = null, 3000);
        }
      });
    }
  }

  getFormattedDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  getUnreadCount(): number {
    return this.messages.filter(msg => !msg.is_read).length;
  }
}
