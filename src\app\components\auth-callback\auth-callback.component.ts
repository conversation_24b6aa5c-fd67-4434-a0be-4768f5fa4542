import { Component, OnInit } from '@angular/core';

import { ActivatedRoute, Router } from '@angular/router';
import { AuthService, AuthResponse } from '../../services/auth.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-auth-callback',
  template: `
    <div class="auth-callback-container">
      <div class="spinner" *ngIf="loading"></div>
      <p [class.error]="isError">{{ message }}</p>
    </div>
  `,
  styles: [`
    .auth-callback-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      text-align: center;
      padding: 20px;
    }
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #09f;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    .error {
      color: #dc3545;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `],
  standalone: true,
  imports: [CommonModule]
})
export class AuthCallbackComponent implements OnInit {
  message = 'Processing authentication...';
  loading = true;
  isError = false;
  private readonly REDIRECT_DELAY = 1500;
  private readonly ERROR_REDIRECT_DELAY = 3000;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe({
      next: params => {
        console.log('Auth callback params:', params);

        if (params['error']) {
          this.handleError(`Authentication failed: ${params['error']}`);
          return;
        }

        if (params['code']) {
          // Check if this is a Facebook callback by looking at the state parameter
          if (params['state'] === 'facebook') {
            console.log('Detected Facebook callback with state parameter');
            // Store the state parameter to ensure it's passed in the callback
            localStorage.setItem('facebook_state', 'facebook');
            // Add a small delay to ensure the state parameter is stored
            setTimeout(() => {
              this.handleFacebookCallback(params['code']); // Use the Facebook-specific handler
            }, 100);
          } else {
            console.log('Detected Google callback');
            this.handleGoogleCallback(params['code']);
          }
        } else if (params['token']) {
          console.log('Detected direct callback with token');
          this.handleDirectCallback(params['token'], params['user']);
        } else {
          console.error('No authentication code or token received in params:', params);
          this.handleError('No authentication code or token received');
        }
      },
      error: err => {
        console.error('Error reading query params:', err);
        this.handleError('Failed to process authentication parameters');
      }
    });
  }

  private handleFacebookCallback(code: string): void {
    console.log('Processing Facebook callback with code length:', code.length);

    // Define storage keys
    const STORAGE_KEYS = {
      USER: 'user',
      TOKEN: 'token'
    };

    // Get the state parameter from localStorage
    const state = localStorage.getItem('facebook_state') || 'facebook';
    console.log('Using state parameter for Facebook callback:', state);

    this.authService.handleFacebookCallback(code).subscribe({
      next: (response: AuthResponse) => {
        console.log('Facebook callback response:', response);

        if (!response) {
          throw new Error('Empty response from authentication server');
        }

        // Log the structure of the response
        console.log('Response type:', typeof response);
        console.log('Response keys:', Object.keys(response));

        // Handle different response formats for token
        let token = null;
        if (response.token) {
          token = response.token;
        } else if (response.data && response.data.token) {
          token = response.data.token;
        }

        // Store token if found
        if (token && typeof token === 'string') {
          console.log('Setting token from response, length:', token.length);
          this.authService.setToken(token);
          localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        } else {
          console.warn('No valid token found in response');
        }

        // Handle different response formats for user data
        let userData = null;
        if (response.user) {
          userData = response.user;
        } else if (response.data && response.data.user) {
          userData = response.data.user;
        }

        // Store user data if found
        if (userData) {
          console.log('Setting user from response:', userData);
          const normalizedUser = this.authService.normalizeUserData(userData);
          this.authService.setCurrentUser(normalizedUser);
          localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
        } else {
          // If no user data in response, try to fetch it
          console.log('No user data in response, attempting to fetch from API');
          this.authService.fetchCurrentUser().subscribe({
            next: (fetchedUser) => {
              if (fetchedUser) {
                console.log('Successfully fetched user data from API:', fetchedUser);
                localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(fetchedUser));
              } else {
                console.warn('Failed to fetch user data from API');
              }
            },
            error: (fetchError) => {
              console.error('Error fetching user data from API:', fetchError);
            }
          });
        }

        // Check if we have a token in either service or localStorage
        const serviceToken = this.authService.getAuthToken();
        const localStorageToken = localStorage.getItem(STORAGE_KEYS.TOKEN);

        if (!serviceToken && !localStorageToken) {
          console.error('No token available after processing response');
          console.error('Response was:', response);
          throw new Error('No authentication token received');
        }

        // If token is in localStorage but not in service, recover it
        if (!serviceToken && localStorageToken) {
          console.log('Token found in localStorage but not in service, recovering');
          this.authService.setToken(localStorageToken);
        }

        // For Facebook, use direct navigation to profile page
        if (localStorage.getItem('facebook_state') === 'facebook') {
          console.log('Facebook authentication successful, navigating directly to profile');
          // Clear the state parameter
          localStorage.removeItem('facebook_state');
          // Navigate directly to profile
          window.location.href = '/profile';
        } else {
          this.handleSuccess('Facebook authentication successful!');
        }
      },
      error: (error) => {
        console.error('Facebook authentication error:', error);
        console.error('Error details:', error.error);
        this.handleError(error.message || 'Failed to authenticate with Facebook');
      }
    });
  }

  private handleGoogleCallback(code: string): void {
    console.log('Processing Google callback with code length:', code.length);

    // Define storage keys
    const STORAGE_KEYS = {
      USER: 'user',
      TOKEN: 'token'
    };

    this.authService.handleGoogleCallback(code).subscribe({
      next: (response: AuthResponse) => {
        console.log('Google callback response:', response);

        if (!response) {
          throw new Error('Empty response from authentication server');
        }

        // Log the structure of the response
        console.log('Response type:', typeof response);
        console.log('Response keys:', Object.keys(response));

        // Handle different response formats for token
        let token = null;
        if (response.token) {
          token = response.token;
        } else if (response.data && response.data.token) {
          token = response.data.token;
        }

        // Store token if found
        if (token && typeof token === 'string') {
          console.log('Setting token from response, length:', token.length);
          this.authService.setToken(token);
          localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        } else {
          console.warn('No valid token found in response');
        }

        // Handle different response formats for user data
        let userData = null;
        if (response.user) {
          userData = response.user;
        } else if (response.data && response.data.user) {
          userData = response.data.user;
        }

        // Store user data if found
        if (userData) {
          console.log('Setting user from response:', userData);
          const normalizedUser = this.authService.normalizeUserData(userData);
          this.authService.setCurrentUser(normalizedUser);
          localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
        } else {
          // If no user data in response, try to fetch it
          console.log('No user data in response, attempting to fetch from API');
          this.authService.fetchCurrentUser().subscribe({
            next: (fetchedUser) => {
              if (fetchedUser) {
                console.log('Successfully fetched user data from API:', fetchedUser);
                localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(fetchedUser));
              } else {
                console.warn('Failed to fetch user data from API');
              }
            },
            error: (fetchError) => {
              console.error('Error fetching user data from API:', fetchError);
            }
          });
        }

        // Check if we have a token in either service or localStorage
        const serviceToken = this.authService.getAuthToken();
        const localStorageToken = localStorage.getItem(STORAGE_KEYS.TOKEN);

        if (!serviceToken && !localStorageToken) {
          console.error('No token available after processing response');
          console.error('Response was:', response);
          throw new Error('No authentication token received');
        }

        // If token is in localStorage but not in service, recover it
        if (!serviceToken && localStorageToken) {
          console.log('Token found in localStorage but not in service, recovering');
          this.authService.setToken(localStorageToken);
        }

        this.handleSuccess('Google authentication successful!');
      },
      error: (error) => {
        console.error('Google authentication error:', error);
        console.error('Error details:', error.error);
        this.handleError(error.message || 'Failed to authenticate with Google');
      }
    });
  }

  private handleDirectCallback(token: string, encodedUserData?: string): void {
    console.log('Processing direct callback with token length:', token ? token.length : 0);

    // Define storage keys
    const STORAGE_KEYS = {
      USER: 'user',
      TOKEN: 'token'
    };

    try {
      // Ensure token is valid and store it in both service and localStorage
      if (token && typeof token === 'string') {
        console.log('Valid token received, setting in auth service and localStorage');
        this.authService.setToken(token);
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
      } else {
        console.error('Invalid token received:', token);
        return this.handleError('Invalid authentication token received');
      }

      // Process user data if available
      if (encodedUserData) {
        try {
          const userData = JSON.parse(atob(encodedUserData));
          console.log('Decoded user data:', userData);
          const normalizedUser = this.authService.normalizeUserData(userData);
          this.authService.setCurrentUser(normalizedUser);
          localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
        } catch (e) {
          console.error('Failed to decode user data:', e);
          // Don't return error here, we can still proceed with just the token
          console.warn('Continuing with authentication despite user data error');
        }
      } else {
        // If no user data, try to fetch it
        console.log('No user data provided, attempting to fetch from API');
        this.authService.fetchCurrentUser().subscribe({
          next: (userData) => {
            if (userData) {
              console.log('Successfully fetched user data from API:', userData);
              localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(userData));
            } else {
              console.warn('Failed to fetch user data from API');
            }
          },
          error: (fetchError) => {
            console.error('Error fetching user data from API:', fetchError);
          }
        });
      }

      // Double-check token is set before proceeding
      if (!this.authService.getAuthToken() && !localStorage.getItem(STORAGE_KEYS.TOKEN)) {
        console.error('Token not properly set after processing');
        return this.handleError('Failed to set authentication token');
      }

      this.handleSuccess('Authentication successful!');
    } catch (error) {
      console.error('Direct callback error:', error);
      this.handleError('Failed to process authentication response');
    }
  }

  private handleSuccess(message: string): void {
    this.message = message;
    this.loading = false;
    this.isError = false;

    // Define storage keys
    const STORAGE_KEYS = {
      USER: 'user',
      TOKEN: 'token'
    };

    // Verify authentication state before redirecting
    const token = this.authService.getAuthToken();
    this.authService.getCurrentUser().subscribe(user => {
      console.log('Current user before redirect:', user);
    });

    console.log('Auth state before redirect:', this.authService.isLoggedIn());
    console.log('Token before redirect:', token ? 'exists' : 'not found');
    console.log('Token in localStorage:', localStorage.getItem(STORAGE_KEYS.TOKEN) ? 'exists' : 'not found');
    console.log('User in localStorage:', localStorage.getItem(STORAGE_KEYS.USER) ? 'exists' : 'not found');

    // Double-check that we have a token
    if (!token && !localStorage.getItem(STORAGE_KEYS.TOKEN)) {
      console.error('No token found before redirect, authentication may have failed');
      this.handleError('Authentication failed: No token found');
      return;
    }

    // If token is in localStorage but not in service, recover it
    if (!token && localStorage.getItem(STORAGE_KEYS.TOKEN)) {
      const storedToken = localStorage.getItem(STORAGE_KEYS.TOKEN);
      console.log('Token found in localStorage but not in service, recovering');
      this.authService.setToken(storedToken!);
    }

    // Notify other components that auth state has changed
    window.dispatchEvent(new CustomEvent('auth-state-changed', {
      detail: { isLoggedIn: true }
    }));

    // Get the redirect URL or default to profile
    let redirectUrl = localStorage.getItem('auth_redirect') || '/profile';

    // Check if we have a stored contract ID
    const contractId = localStorage.getItem('contract_id');
    if (contractId && redirectUrl.includes('/security')) {
      // Append the contract ID to the security URL if needed
      if (!redirectUrl.includes('?')) {
        redirectUrl = `${redirectUrl}?id=${contractId}`;
      } else if (!redirectUrl.includes('id=')) {
        redirectUrl = `${redirectUrl}&id=${contractId}`;
      }
      console.log('Added contract ID to redirect URL:', redirectUrl);
    }

    console.log('Redirecting to:', redirectUrl);
    localStorage.removeItem('auth_redirect');
    localStorage.removeItem('contract_id');

    // Redirect after a short delay
    setTimeout(() => {
      console.log('Executing navigation to:', redirectUrl);
      console.log('Auth state at navigation time:', this.authService.isLoggedIn());

      // Force a reload of the auth state before navigation
      if (!this.authService.isLoggedIn()) {
        console.log('Auth state is false before navigation, attempting to recover');
        const storedToken = localStorage.getItem(STORAGE_KEYS.TOKEN);
        if (storedToken) {
          this.authService.setToken(storedToken);
          console.log('Recovered token from localStorage');
        }
      }

      // Use window.location.href for more reliable navigation
      console.log('Using direct window.location.href navigation to:', redirectUrl);
      window.location.href = redirectUrl;
    }, this.REDIRECT_DELAY);
  }

  private handleError(message: string): void {
    console.error('Auth error:', message);
    this.message = message;
    this.loading = false;
    this.isError = true;

    // Redirect to login after error
    setTimeout(() => {
      this.router.navigate(['/signin']);
    }, this.ERROR_REDIRECT_DELAY);
  }
}

