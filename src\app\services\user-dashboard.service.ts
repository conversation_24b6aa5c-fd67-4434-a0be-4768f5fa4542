import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class UserDashboardService {
  private apiUrl = environment.apiUrl + '/api';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Get auth headers for API requests
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken();
    let headers = new HttpHeaders()
      .set('Accept', 'application/json')
      .set('Content-Type', 'application/json');

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.error('No auth token available for user dashboard API request');
    }

    return headers;
  }

  /**
   * Get dashboard statistics for the current user
   */
  getUserDashboardStats(dateRange: string = 'last12Months'): Observable<any> {
    console.log('Fetching user dashboard stats with auth token');
    return this.http.get(`${this.apiUrl}/user-dashboard-stats`, {
      headers: this.getAuthHeaders(),
      params: { date_range: dateRange },
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error fetching user dashboard stats:', error);
        return throwError(() => new Error('Failed to load dashboard statistics. Please try again.'));
      })
    );
  }
}
