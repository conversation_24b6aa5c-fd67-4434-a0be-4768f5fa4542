<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SignedContract;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class SignedContractController extends Controller
{
    /**
     * Display a listing of signed contracts.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            Log::info('Fetching all signed contracts');

            // If user is authenticated, show only their contracts
            if (Auth::check()) {
                $contracts = SignedContract::where('user_id', Auth::id())
                    ->orderBy('created_at', 'desc')
                    ->paginate(10);

                Log::info('Fetching signed contracts for authenticated user', [
                    'user_id' => Auth::id(),
                    'count' => $contracts->count()
                ]);
            } else {
                // If not authenticated, show no contracts
                Log::warning('Unauthenticated user trying to access signed contracts');
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to view contracts'
                ], 401);
            }

            Log::info('Signed contracts fetched successfully', ['count' => $contracts->count()]);
            return response()->json($contracts);
        } catch (\Exception $e) {
            Log::error('Error fetching signed contracts', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Error fetching signed contracts: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created signed contract in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            Log::info('Creating new signed contract', ['request' => $request->except('signature_data')]);

            // Validate the request
            $validator = Validator::make($request->all(), [
                'nom_contrat' => 'required|string|max:255',
                'email_signataire' => 'required|email|max:255',
                'signature_data' => 'required|string',
                'file' => 'required|file|mimes:pdf,doc,docx',
            ]);

            if ($validator->fails()) {
                Log::error('Validation failed for signed contract creation', ['errors' => $validator->errors()]);
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Process the uploaded file
            if ($request->hasFile('file') && $request->file('file')->isValid()) {
                $uploadedFile = $request->file('file');
                $originalFileName = $uploadedFile->getClientOriginalName();

                // Determine file type
                $fileType = $uploadedFile->getClientOriginalExtension();
                if (in_array($fileType, ['doc', 'docx'])) {
                    $fileType = 'word';
                } else if ($fileType === 'pdf') {
                    $fileType = 'pdf';
                } else {
                    $fileType = 'other';
                }

                // Generate a unique filename with timestamp
                $fileName = time() . '_' . $originalFileName;
                $filePath = $uploadedFile->storeAs('contracts/signed', $fileName, 'public');

                Log::info('File stored successfully', ['path' => $filePath]);

                $contract = SignedContract::create([
                    'nom_contrat' => $request->input('nom_contrat'),
                    'email_signataire' => $request->input('email_signataire'),
                    'signature_data' => $request->input('signature_data'),
                    'file_path' => $filePath,
                    'file_type' => $fileType,
                    'user_id' => Auth::id()
                ]);

                Log::info('Signed contract created successfully', ['contract_id' => $contract->id]);

                return response()->json([
                    'success' => true,
                    'message' => 'Contract signed and stored successfully',
                    'data' => $contract
                ], 201);
            } else {
                Log::error('No valid file uploaded for signed contract');
                return response()->json([
                    'success' => false,
                    'message' => 'No valid file uploaded'
                ], 422);
            }
        } catch (\Exception $e) {
            Log::error('Error creating signed contract', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Error creating signed contract: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified signed contract.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            Log::info('Fetching signed contract details', ['contract_id' => $id]);

            // Only allow access to the authenticated user's contracts
            if (Auth::check()) {
                $contract = SignedContract::where('user_id', Auth::id())
                    ->where('id', $id)
                    ->first();

                if ($contract) {
                    Log::info('Found signed contract for authenticated user', ['contract_id' => $id, 'user_id' => Auth::id()]);
                    return response()->json($contract);
                } else {
                    Log::warning('Authenticated user trying to access unauthorized signed contract', [
                        'user_id' => Auth::id(),
                        'contract_id' => $id
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Signed contract not found or not authorized'
                    ], 404);
                }
            } else {
                // If not authenticated, deny access
                Log::warning('Unauthenticated user trying to access signed contract', ['contract_id' => $id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to view contract'
                ], 401);
            }
        } catch (\Exception $e) {
            Log::error('Error fetching signed contract details', [
                'contract_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Signed contract not found'
            ], 404);
        }
    }

    /**
     * Download a signed contract file.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function download($id)
    {
        try {
            Log::info('Downloading signed contract', ['contract_id' => $id]);

            // Only allow access to the authenticated user's contracts
            if (Auth::check()) {
                $contract = SignedContract::where('user_id', Auth::id())
                    ->where('id', $id)
                    ->first();

                if (!$contract) {
                    Log::warning('Authenticated user trying to download unauthorized signed contract', [
                        'user_id' => Auth::id(),
                        'contract_id' => $id
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Signed contract not found or not authorized'
                    ], 404);
                }

                Log::info('Found signed contract for authenticated user download', [
                    'contract_id' => $id,
                    'user_id' => Auth::id()
                ]);
            } else {
                // If user not authenticated, deny access
                Log::warning('Unauthenticated user trying to download signed contract', ['contract_id' => $id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to download contract'
                ], 401);
            }

            if (!$contract->file_path || !Storage::disk('public')->exists($contract->file_path)) {
                Log::error('Signed contract file not found', [
                    'contract_id' => $id,
                    'file_path' => $contract->file_path
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Signed contract file not found'
                ], 404);
            }

            $fullPath = Storage::disk('public')->path($contract->file_path);
            $fileName = basename($contract->file_path);

            // Determine the MIME type from the file or use the stored file_type
            $mimeType = $contract->file_type ?? File::mimeType($fullPath);

            Log::info('Serving signed contract file', [
                'contract_id' => $id,
                'file_path' => $fullPath,
                'mime_type' => $mimeType
            ]);

            return Response::download($fullPath, $fileName, [
                'Content-Type' => $mimeType,
            ]);
        } catch (\Exception $e) {
            Log::error('Error downloading signed contract', [
                'contract_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error downloading signed contract: ' . $e->getMessage()
            ], 500);
        }
    }
}
