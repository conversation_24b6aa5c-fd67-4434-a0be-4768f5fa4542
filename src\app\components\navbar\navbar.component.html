<!-- Navbar -->
<nav class="navbar" [class.dark-theme]="isDarkMode">
  <div class="navbar-container">
    <!-- Search Form -->
    <form class="search-form">
      <div class="input-group">
        <span class="input-group-text">
          <i class="material-icons">search</i>
        </span>
        <input
          type="text"
          class="form-control"
          placeholder="Search"
          [(ngModel)]="searchQuery"
          name="search">
      </div>
    </form>

    <!-- Right Side Items -->
    <div class="navbar-right">


      <!-- Theme Toggle -->
      <button class="btn-icon" (click)="toggleDarkMode()" [title]="isDarkMode ? 'Light Mode' : 'Dark Mode'">
        <i class="material-icons">{{ isDarkMode ? 'light_mode' : 'dark_mode' }}</i>
      </button>

      <!-- Authentication Section - Conditionally shows either profile icon or sign-in/sign-up buttons -->
      <ng-container *ngIf="isLoggedIn$ | async; else authButtons">
        <!-- User Menu (Only visible when logged in) -->
        <div class="user-menu">
          <div class="dropdown" [class.show]="isProfileMenuOpen">
            <button class="btn-icon dropdown-toggle" type="button" id="userDropdown" (click)="toggleProfileMenu($event)">
              <i class="material-icons">account_circle</i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" [class.show]="isProfileMenuOpen">
              <li><a class="dropdown-item" routerLink="/profile" (click)="isProfileMenuOpen = false">Profile</a></li>
              <li><a class="dropdown-item" routerLink="/settings" (click)="isProfileMenuOpen = false">Settings</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" (click)="logout()">Logout</a></li>
            </ul>
          </div>
        </div>
      </ng-container>

      <!-- Login/Register Buttons (Only visible when logged out) -->
      <ng-template #authButtons>
        <div class="auth-buttons">
          <a routerLink="/signin" class="btn btn-outline-primary">
            <i class="material-icons">login</i>
            Sign In
          </a>
          <a routerLink="/signup" class="btn btn-primary">
            <i class="material-icons">person_add</i>
            Sign Up
          </a>
        </div>
      </ng-template>
    </div>
  </div>
</nav>
