import { Component, OnInit, HostListener, PLATFORM_ID, Inject, Output, EventEmitter } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { RouterModule, Router, NavigationEnd } from '@angular/router';
import { filter, map } from 'rxjs/operators';
import { AuthService } from '../../services/auth.service';
import { Observable } from 'rxjs';

interface RouteInfo {
  path: string;
  title: string;
  icon: string;
  class: string;
  requiresAuth?: boolean;
  requiresAdmin?: boolean;
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css']
})
export class SidebarComponent implements OnInit {
  menuItems: RouteInfo[] = [];
  isMobile = false;
  private isBrowser: boolean;
  isExpanded = true;
  authError: string | null = null;
  isLoggedIn$: Observable<boolean>;

  @Output() sidebarToggled = new EventEmitter<boolean>();

  constructor(
    private router: Router,
    public authService: AuthService,
    @Inject(PLATFORM_ID) platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(platformId);
    this.isLoggedIn$ = this.authService.authState$;

    // Close sidebar on mobile when route changes
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      if (this.isMobile) {
        this.isExpanded = false;
      }
    });
  }

  ngOnInit(): void {
    this.menuItems = [
      // Public routes
      { path: '/', title: 'Home', icon: 'home', class: '' },
      // Terms & Conditions menu item removed

      // Protected routes
      { path: '/profile', title: 'Profile', icon: 'person', class: '', requiresAuth: true },
      { path: '/settings', title: 'Settings', icon: 'settings', class: '', requiresAuth: true },
      { path: '/ai-chatbot', title: 'AI Assistant', icon: 'smart_toy', class: '', requiresAuth: true },
      { path: '/file-upload', title: 'Upload Documents', icon: 'cloud_upload', class: '', requiresAuth: true },
      { path: '/security', title: 'Electronic Signature', icon: 'draw', class: '', requiresAuth: true },
      { path: '/dashboard', title: 'Dashboard', icon: 'dashboard', class: '', requiresAuth: true },

      // Admin routes - only visible to admin users
      { path: '/admin', title: 'Admin Panel', icon: 'admin_panel_settings', class: '', requiresAuth: true, requiresAdmin: true },
      { path: '/admin/notifications', title: 'Notifications', icon: 'notifications', class: '', requiresAuth: true, requiresAdmin: true },
      { path: '/admin/partners', title: 'Partner Management', icon: 'business', class: '', requiresAuth: true, requiresAdmin: true },
      // Manage Terms menu item removed

      // Support routes
      { path: '/contact', title: 'Contact', icon: 'contact_support', class: '' }
    ];

    if (this.isBrowser) {
      this.checkScreenSize();
    }

    // Emit initial state
    setTimeout(() => {
      this.sidebarToggled.emit(this.isExpanded);
    }, 0);
  }

  @HostListener('window:resize')
  onResize() {
    if (this.isBrowser) {
      this.checkScreenSize();
    }
  }

  checkScreenSize() {
    if (!this.isBrowser) return;

    const wasDesktop = !this.isMobile;
    this.isMobile = window.innerWidth < 992;

    // If transitioning from desktop to mobile, close the sidebar
    if (wasDesktop && this.isMobile) {
      this.isExpanded = false;
      this.sidebarToggled.emit(this.isExpanded);
    }

    // If transitioning from mobile to desktop, open the sidebar
    if (!wasDesktop && !this.isMobile) {
      this.isExpanded = true;
      this.sidebarToggled.emit(this.isExpanded);
    }
  }

  toggleSidebar(): void {
    // Set a flag to prevent multiple toggles in quick succession
    if (this._isToggling) return;
    this._isToggling = true;

    // Toggle the sidebar state
    this.isExpanded = !this.isExpanded;
    this.sidebarToggled.emit(this.isExpanded);

    // Reset the flag after a short delay
    setTimeout(() => {
      this._isToggling = false;
    }, 300);
  }

  private _isToggling = false;

  handleAuthError(error: any) {
    console.error('Authentication error:', error);
    this.authError = 'Unable to sign in. Please try again later.';
    // Clear error after 5 seconds
    setTimeout(() => {
      this.authError = null;
    }, 5000);
  }

  toggleUserMenu() {
    if (this.authError) {
      this.handleAuthError({ message: 'Server error: 0 - Unknown Error' });
      return;
    }
    // Implement user menu toggle logic
  }

  closeSidebar() {
    this.isExpanded = false;
    this.sidebarToggled.emit(this.isExpanded);
  }

  isAdmin(): Observable<boolean> {
    // Force a check of the current authentication state
    this.authService.isLoggedIn();

    return this.authService.currentUser$.pipe(
      map(user => {
        const isAdmin = !!user?.is_admin;
        console.log('Sidebar - Current user:', user);
        console.log('Sidebar - Is admin?', isAdmin);
        return isAdmin;
      })
    );
  }

  // Check if signature component is active
  isSignatureActive(): boolean {
    return this.router.url === '/security' || this.router.url.startsWith('/security');
  }
}
