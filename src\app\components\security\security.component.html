

<section class="security-section" aria-labelledby="security-heading">
  <div class="feature-content">
    <!-- En-tête avec animation -->
    <header class="section-header">
      <h2 id="security-heading" class="animate__fadeInDown">
          <span class="highlight">Signature</span>
      </h2>
      <p class="section-subtitle">Secure your documents with our electronic signature system</p>
    </header>

    <div class="security-container">
      <!-- Section Aperçu du contrat -->
      <div class="contract-preview">
        <h2><i class="fas fa-file-contract"></i>Contract to sign</h2>
        <div class="contract-content">
          <!-- File Preview Section -->
          <div class="file-preview-container">
            <!-- Document Preview - Universal for PDF and Word -->
            <div class="document-container">
              <!-- Document icon and info -->
              <div class="document-preview-content">
                <!-- Use file extension directly to determine the correct icon and text -->
                <i class="fas" [ngClass]="{
                  'fa-file-pdf': pdfFile && pdfFile.name.toLowerCase().endsWith('.pdf'),
                  'fa-file-word': pdfFile && (pdfFile.name.toLowerCase().endsWith('.doc') || pdfFile.name.toLowerCase().endsWith('.docx')),
                  'fa-file': !pdfFile
                }"></i>
                <h3>{{ pdfFile ? 'Document ' + (pdfFile.name.split('.').pop()?.toUpperCase() || '') : 'No document selected' }}</h3>
                <p *ngIf="pdfFile">{{ pdfFile.name }}</p>
                <p>Your document is ready to sign.</p>
                <div class="document-actions">
                  <button class="btn btn-primary" (click)="openExternalViewer()">
                    <i class="fas fa-external-link-alt"></i> Open in New Tab
                  </button>
                </div>
              </div>
            </div>

            <!-- We don't need a separate Word preview anymore as we're using a universal approach -->

            <!-- Loading State -->
            <div *ngIf="isLoading" class="loading-state">
              <div class="spinner"></div>
              <p>Loading document... Please wait</p>
            </div>

            <!-- Error State -->
            <div *ngIf="errorMessage && !isLoading" class="error-state">
              <i class="fas fa-exclamation-triangle"></i>
              <p>{{ errorMessage }}</p>
              <button class="btn btn-secondary" (click)="errorMessage = null">Dismiss</button>
              <p *ngIf="errorMessage.includes('not found')">
                <small>Try refreshing the page or going back to the dashboard.</small>
              </p>
            </div>

            <!-- No File Selected -->
            <div *ngIf="!filePreviewType && !pdfFile && !isLoading && !errorMessage" class="no-file-selected">
              <i class="fas fa-cloud-upload-alt upload-icon"></i>
              <p>Waiting for document to load...</p>
              <p><small>If nothing appears after a few seconds, you can manually select a file below.</small></p>
            </div>
          </div>

          <!-- File upload control -->
          <div class="form-group upload-container">
            <label for="pdfFile" class="upload-label">
              <i class="fas fa-cloud-upload-alt icon"></i>
              <span class="label-text">Upload your document here</span>
              <small>Supported formats: PDF, DOCX, DOC</small>
              <span class="file-status" *ngIf="pdfFile">
                <i class="fas fa-check-circle"></i> {{ pdfFile.name }}
              </span>
            </label>
            <input
              type="file"
              id="pdfFile"
              (change)="onFileSelected($event)"
              accept=".pdf,.docx,.doc"
              class="upload-input">
          </div>

          <!-- Document Information Summary -->
          <div class="document-info" *ngIf="pdfFile">
            <h3><i class="fas fa-info-circle"></i> Document Information</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Name:</span>
                <span class="info-value">{{ pdfFile.name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Type:</span>
                <span class="info-value">{{ pdfFile ? 'Document ' + (pdfFile.name.split('.').pop()?.toUpperCase() || '') : '' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Size:</span>
                <span class="info-value">{{ pdfFile ? (pdfFile.size / 1024).toFixed(1) + ' KB' : '' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Date:</span>
                <span class="info-value">{{ getCurrentDate() }}</span>
              </div>
            </div>

            <!-- Document Actions -->
            <div class="document-actions">
              <!-- Document View Options - For both PDF and Word -->
              <button class="btn btn-secondary" (click)="openExternalViewer()">
                <i class="fas fa-external-link-alt"></i> Open in New Tab
              </button>

              <!-- Download Button (if needed) -->
              <!-- <button *ngIf="filePreviewType === 'pdf' && pdfFile" class="btn btn-primary">
                <i class="fas fa-download"></i> Download Original
              </button> -->
            </div>

            <!-- Signature Position Selector - Show for all document types -->
            <div class="signature-position-selector">
              <h4><i class="fas fa-map-marker-alt"></i> Signature Position</h4>
              <div class="position-options">
                <div class="position-option" [class.active]="signaturePosition === 'bottom-left'" (click)="setSignaturePosition('bottom-left')">
                  <div class="position-preview bottom-left"></div>
                  <span>Bottom left</span>
                </div>
                <div class="position-option" [class.active]="signaturePosition === 'bottom-center'" (click)="setSignaturePosition('bottom-center')">
                  <div class="position-preview bottom-center"></div>
                  <span>Bottom center</span>
                </div>
                <div class="position-option" [class.active]="signaturePosition === 'bottom-right'" (click)="setSignaturePosition('bottom-right')">
                  <div class="position-preview bottom-right"></div>
                  <span>Bottom right</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Section de signature -->
      <div class="signing-section">
        <form [formGroup]="signForm" class="signer-info">
          <h2><i class="fas fa-user-edit"></i>Signer Information</h2>

          <div class="form-group">
            <label for="name">
              <i class="fas fa-user"></i> Full Name
            </label>
            <input
              type="text"
              id="name"
              formControlName="fullName"
              placeholder="Jean Dupont"
              class="modern-input">
            <small
              class="error-message"
              *ngIf="signForm.get('fullName')?.invalid && signForm.get('fullName')?.touched">
              <i class="fas fa-exclamation-circle"></i> Full name is required
            </small>
          </div>

          <div class="form-group">
            <label for="email">
              <i class="fas fa-envelope"></i> Email address
            </label>
            <input
              type="email"
              id="email"
              formControlName="email"
              placeholder="<EMAIL>"
              class="modern-input">
            <small
              class="error-message"
              *ngIf="signForm.get('email')?.invalid && signForm.get('email')?.touched">
              <i class="fas fa-exclamation-circle"></i> Please enter a valid email address
            </small>
          </div>

          <!-- Zone de signature -->
          <div class="signature-area">
            <h3><i class="fas fa-pen-fancy"></i> Electronic Signature</h3>

            <!-- Signature Type Selector -->
            <div class="signature-type-selector">
              <button type="button" class="signature-type-btn" [class.active]="signatureType === 'draw'" (click)="setSignatureType('draw')">
                <i class="fas fa-pen"></i> Draw
              </button>
              <button type="button" class="signature-type-btn" [class.active]="signatureType === 'upload'" (click)="setSignatureType('upload')">
                <i class="fas fa-image"></i> Upload
              </button>
            </div>

            <!-- Draw Signature -->
            <div *ngIf="signatureType === 'draw'" class="signature-draw-container">
              <div class="signature-options">
                <div class="pen-color-options">
                  <span class="option-label">Color:</span>
                  <div class="color-option" [class.active]="penColor === '#1e3c72'" (click)="setPenColor('#1e3c72')" style="background-color: #1e3c72;"></div>
                  <div class="color-option" [class.active]="penColor === '#000000'" (click)="setPenColor('#000000')" style="background-color: #000000;"></div>
                  <div class="color-option" [class.active]="penColor === '#2563eb'" (click)="setPenColor('#2563eb')" style="background-color: #2563eb;"></div>
                  <div class="color-option" [class.active]="penColor === '#7c3aed'" (click)="setPenColor('#7c3aed')" style="background-color: #7c3aed;"></div>
                </div>
                <div class="pen-size-options">
                  <span class="option-label">Thickness:</span>
                  <div class="size-option" [class.active]="penSize === 1" (click)="setPenSize(1)">S</div>
                  <div class="size-option" [class.active]="penSize === 2" (click)="setPenSize(2)">M</div>
                  <div class="size-option" [class.active]="penSize === 3" (click)="setPenSize(3)">L</div>
                </div>
              </div>

              <canvas #signaturePad
                      (mousedown)="startDrawing($event)"
                      (mousemove)="draw($event)"
                      (mouseup)="endDrawing()"
                      (touchstart)="startDrawing($event)"
                      (touchmove)="draw($event)"
                      (touchend)="endDrawing()"></canvas>
            </div>



            <!-- Upload Signature -->
            <div *ngIf="signatureType === 'upload'" class="signature-upload-container">
              <div class="upload-signature-area">
                <div class="upload-instructions" *ngIf="!uploadedSignatureUrl">
                  <i class="fas fa-upload"></i>
                  <p>Upload your signature image</p>
                  <small>Supported formats: PNG, JPG, JPEG</small>
                </div>
                <div class="signature-preview" *ngIf="uploadedSignatureUrl">
                  <img [src]="uploadedSignatureUrl" alt="Your signature" class="signature-image">
                </div>
                <input type="file"
                       id="signatureImage"
                       accept="image/png,image/jpeg,image/jpg"
                       (change)="onSignatureImageSelected($event)"
                       class="signature-file-input"
                       #signatureFileInput>
                <button type="button" class="btn btn-outline" (click)="signatureFileInput.click()">
                  <i class="fas fa-upload"></i> {{ uploadedSignatureUrl ? 'Change Image' : 'Select Image' }}
                </button>
              </div>
              <div class="signature-helper-text">Upload a clear image of your signature on a transparent or white background</div>
            </div>


            <div class="signature-controls">
              <button type="button" class="btn btn-secondary"
                      (click)="undo()" [disabled]="!canUndo || signatureType !== 'draw'">
                <i class="fas fa-undo"></i> Undo
              </button>
              <button type="button" class="btn btn-secondary"
                      (click)="redo()" [disabled]="!canRedo || signatureType !== 'draw'">
                <i class="fas fa-redo"></i> Redo
              </button>
              <button type="button" class="btn btn-secondary"
                      (click)="clearSignature()">
                <i class="fas fa-eraser"></i> Clear
              </button>
            </div>
          </div>

          <!-- Case à cocher de consentement -->
          <label class="consent-check">
            <input type="checkbox" formControlName="consent">
              <span>I certify that I have read and accepted the terms of the contrac</span>
          </label>

          <!-- Boutons d'action -->
          <div class="action-buttons">
            <button class="btn btn-primary"
                    (click)="submitSignature()"
                    [disabled]="isLoading || signForm.invalid || !isSignatureValid() || !pdfFile">
              <span *ngIf="!isLoading">
                <i class="fas fa-signature"></i> Sign and Download
              </span>
              <span *ngIf="isLoading">
                <span class="spinner"></span> Signing in progress...
              </span>
            </button>
          </div>
        </form>

        <!-- Messages de validation -->
        <div class="status-messages">
          <div *ngIf="errorMessage" class="alert error">
            <i class="fas fa-exclamation-circle"></i> {{ errorMessage }}
            <p *ngIf="errorMessage.includes('Failed to sign Word document')">
              <small>The document may still be signed. Please check your dashboard after this message disappears.</small>
            </p>
            <p *ngIf="errorMessage.includes('Failed to sign PDF document')">
              <small>The document may still be signed. Please check your dashboard after this message disappears.</small>
            </p>
          </div>
          <div *ngIf="successMessage" class="alert success">
            <i class="fas fa-check-circle"></i> {{ successMessage }}
            <p><small>You will be redirected to your dashboard shortly.</small></p>
          </div>
        </div>
        <div class="field-errors">
          <div *ngFor="let error of fieldErrors | keyvalue" class="error">
            <i class="fas fa-exclamation-triangle"></i> {{ error.value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>


