<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\PhpWord;
use setasign\Fpdi\Tcpdf\Fpdi;

class DocumentSigningService
{
    /**
     * Ajoute une signature à un document Word
     *
     * @param string $filePath Chemin du fichier dans le stockage
     * @param string $signatureData Données de la signature en base64
     * @param string $signerName Nom du signataire
     * @return string|null Chemin du fichier signé ou null en cas d'erreur
     */
    public function signWordDocument(string $filePath, string $signatureData, string $signerName): ?string
    {
        try {
            Log::info('Début du processus de signature du document Word', [
                'filePath' => $filePath
            ]);

            // Vérifier si le fichier existe
            if (!Storage::disk('public')->exists($filePath)) {
                Log::error('Le fichier Word à signer n\'existe pas', ['filePath' => $filePath]);
                return null;
            }

            // Chemin complet du fichier
            $fullPath = Storage::disk('public')->path($filePath);

            // Charger le document Word
            $phpWord = IOFactory::load($fullPath);

            // Obtenir la dernière section ou en créer une nouvelle si nécessaire
            $sections = $phpWord->getSections();
            $lastSection = end($sections);

            if (!$lastSection) {
                Log::warning('Aucune section trouvée dans le document, création d\'une nouvelle section');
                $lastSection = $phpWord->addSection();
            }

            // Ajouter un saut de page avant la signature si nécessaire
            // Si le document a déjà du contenu, ajouter un saut de page
            if (count($sections) > 0 && count($lastSection->getElements()) > 0) {
                $lastSection->addPageBreak();
            }

            // Ajouter les informations de signature dans le format exact demandé
            $dateText = date('d/m/Y'); // Format jour/mois/année comme dans l'exemple

            // Créer un paragraphe pour chaque ligne avec un espacement approprié
            // Ligne "Signé par : [nom]"
            $lastSection->addText("Signé par : {$signerName}", ['size' => 12], ['spaceAfter' => 120]);

            // Ligne "Fait le : [date]"
            $lastSection->addText("Fait le : {$dateText}", ['size' => 12], ['spaceAfter' => 120]);

            // Ligne "Signature électronique"
            $lastSection->addText("Signature électronique", ['size' => 12], ['spaceAfter' => 240]);

            // Traiter l'image de signature (supprimer le préfixe data URL)
            $signatureBase64 = '';
            if (strpos($signatureData, 'data:image/png;base64,') === 0) {
                $signatureBase64 = substr($signatureData, 22); // Enlever 'data:image/png;base64,'
            } else {
                $signatureBase64 = $signatureData;
            }

            // Créer un fichier temporaire pour l'image de signature
            $tempSignatureFile = tempnam(sys_get_temp_dir(), 'signature_') . '.png';
            file_put_contents($tempSignatureFile, base64_decode($signatureBase64));

            Log::info('Signature image created', ['tempFile' => $tempSignatureFile]);

            // Ajouter l'image de signature au document avec des dimensions appropriées
            try {
                $lastSection->addImage($tempSignatureFile, [
                    'width' => 180,
                    'height' => 60,
                    'alignment' => 'left',
                    'marginTop' => 0,
                    'marginBottom' => 10
                ]);
                Log::info('Signature image added to Word document');
            } catch (\Exception $e) {
                Log::error('Error adding signature image to Word document', [
                    'error' => $e->getMessage()
                ]);
                // Fallback to text signature if image fails
                $lastSection->addText("___________/\\___________", ['size' => 12], ['spaceAfter' => 240]);
            }

            // Générer un nouveau nom de fichier pour le document signé
            $originalFileName = basename($filePath);
            $originalExtension = pathinfo($originalFileName, PATHINFO_EXTENSION);

            // Make sure we preserve the original extension exactly as it is
            Log::info('Original file extension for Word document', ['extension' => $originalExtension]);

            $signedFileName = pathinfo($originalFileName, PATHINFO_FILENAME) . '_signed_' . time() . '.' . $originalExtension;
            $signedFilePath = 'contracts/signed/' . $signedFileName;

            Log::info('Generated signed file name', ['signedFileName' => $signedFileName]);

            // Determine the correct writer format based on file extension
            $extension = strtolower(pathinfo($originalFileName, PATHINFO_EXTENSION));
            $writerFormat = 'Word2007'; // Default for .docx

            if ($extension === 'doc') {
                $writerFormat = 'Word2007'; // We'll still use Word2007 for .doc files as Word97 is not reliable
                Log::info('Using Word2007 writer for .doc file');
            } else if ($extension === 'docx') {
                $writerFormat = 'Word2007';
                Log::info('Using Word2007 writer for .docx file');
            } else {
                Log::warning('Unknown Word document extension: ' . $extension . ', defaulting to Word2007');
            }

            // Sauvegarder le document modifié
            $fullSignedPath = Storage::disk('public')->path($signedFilePath);

            try {
                $objWriter = IOFactory::createWriter($phpWord, $writerFormat);
                $objWriter->save($fullSignedPath);

                // Verify the file was created successfully
                if (!file_exists($fullSignedPath) || filesize($fullSignedPath) === 0) {
                    Log::error('Failed to create signed Word document or file is empty', [
                        'path' => $fullSignedPath,
                        'exists' => file_exists($fullSignedPath),
                        'size' => file_exists($fullSignedPath) ? filesize($fullSignedPath) : 'N/A'
                    ]);
                    throw new \Exception('Failed to create signed Word document');
                }

                Log::info('Signed Word document created successfully', [
                    'path' => $fullSignedPath,
                    'size' => filesize($fullSignedPath),
                    'format' => $writerFormat
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to save Word document, falling back to simple copy', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // Fallback: just copy the file if saving fails
                copy($fullPath, $fullSignedPath);

                Log::info('Word document copied without modification as fallback', [
                    'originalPath' => $filePath,
                    'signedPath' => $signedFilePath
                ]);
            }

            // Supprimer le fichier temporaire de signature
            unlink($tempSignatureFile);

            Log::info('Document Word signé avec succès', [
                'originalPath' => $filePath,
                'signedPath' => $signedFilePath
            ]);

            return $signedFilePath;
        } catch (\Exception $e) {
            Log::error('Erreur lors de la signature du document Word', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Ajoute une signature à un document PDF
     *
     * @param string $filePath Chemin du fichier dans le stockage
     * @param string $signatureData Données de la signature en base64
     * @param string $signerName Nom du signataire
     * @param string $position Position de la signature (bottom-left, bottom-center, bottom-right)
     * @return string|null Chemin du fichier signé ou null en cas d'erreur
     */
    public function signPdfDocument(string $filePath, string $signatureData, string $signerName, string $position = 'bottom-right'): ?string
    {
        try {
            Log::info('Début du processus de signature du document PDF', [
                'filePath' => $filePath
            ]);

            // Vérifier si le fichier existe
            if (!Storage::disk('public')->exists($filePath)) {
                Log::error('Le fichier PDF à signer n\'existe pas', ['filePath' => $filePath]);
                return null;
            }

            // Chemin complet du fichier
            $fullPath = Storage::disk('public')->path($filePath);

            // Traiter l'image de signature (supprimer le préfixe data URL)
            $signatureBase64 = '';
            if (strpos($signatureData, 'data:image/png;base64,') === 0) {
                $signatureBase64 = substr($signatureData, 22); // Enlever 'data:image/png;base64,'
            } else {
                $signatureBase64 = $signatureData;
            }

            // Créer un fichier temporaire pour l'image de signature
            $tempSignatureFile = tempnam(sys_get_temp_dir(), 'signature_') . '.png';
            file_put_contents($tempSignatureFile, base64_decode($signatureBase64));

            Log::info('Signature image created', ['tempFile' => $tempSignatureFile]);

            // Générer un nouveau nom de fichier pour le document signé
            $originalFileName = basename($filePath);
            $originalExtension = pathinfo($originalFileName, PATHINFO_EXTENSION);

            // Make sure we preserve the original extension exactly as it is
            Log::info('Original file extension for PDF document', ['extension' => $originalExtension]);

            $signedFileName = pathinfo($originalFileName, PATHINFO_FILENAME) . '_signed_' . time() . '.' . $originalExtension;
            $signedFilePath = 'contracts/signed/' . $signedFileName;

            Log::info('Generated signed file name', ['signedFileName' => $signedFileName]);

            $fullSignedPath = Storage::disk('public')->path($signedFilePath);

            try {
                // Utiliser FPDI avec TCPDF pour ajouter la signature au PDF
                $pdf = new Fpdi();

                // Disable auto page break
                $pdf->SetAutoPageBreak(false);

                // Get the number of pages from the original PDF
                $pageCount = $pdf->setSourceFile($fullPath);
                Log::info('PDF page count: ' . $pageCount);

                // Process each page
                for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                    try {
                        // Import the page
                        $templateId = $pdf->importPage($pageNo);

                        // Get the page dimensions
                        $size = $pdf->getTemplateSize($templateId);

                        // Add a page with the same orientation as the imported page
                        $orientation = ($size['width'] > $size['height']) ? 'L' : 'P';
                        $pdf->AddPage($orientation, [$size['width'], $size['height']]);

                        // Use the imported page as a template
                        $pdf->useTemplate($templateId);

                        // Only add signature to the last page
                        if ($pageNo === $pageCount) {
                            // Set font for signature text
                            $pdf->SetFont('helvetica', '', 10);
                            $pdf->SetTextColor(0, 0, 0);

                            // Calculate signature position based on the specified position
                            $signatureWidth = 180;
                            $signatureHeight = 60;
                            $margin = 20;

                            // Default position (bottom-right)
                            $x = $size['width'] - $signatureWidth - $margin;
                            $y = $size['height'] - $signatureHeight - $margin;

                            if ($position === 'bottom-left') {
                                $x = $margin;
                                $y = $size['height'] - $signatureHeight - $margin;
                            } else if ($position === 'bottom-center') {
                                $x = ($size['width'] - $signatureWidth) / 2;
                                $y = $size['height'] - $signatureHeight - $margin;
                            }

                            // Add signature text
                            $pdf->SetXY($x, $y);
                            $pdf->Cell(0, 10, "Signé par : {$signerName}", 0, 2);
                            $pdf->SetXY($x, $y + 10);
                            $pdf->Cell(0, 10, "Fait le : " . date('d/m/Y'), 0, 2);
                            $pdf->SetXY($x, $y + 20);
                            $pdf->Cell(0, 10, "Signature électronique", 0, 2);

                            // Add signature image
                            $pdf->Image($tempSignatureFile, $x, $y + 30, 60, 20);
                        }
                    } catch (\Exception $e) {
                        Log::error('Error processing PDF page ' . $pageNo, [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        // Continue with next page
                    }
                }

                // Save the signed PDF
                $pdf->Output($fullSignedPath, 'F');

                Log::info('PDF signed successfully with signature added', [
                    'originalPath' => $filePath,
                    'signedPath' => $signedFilePath
                ]);
            } catch (\Exception $e) {
                Log::error('Error adding signature to PDF, falling back to simple copy', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // Fallback: just copy the file if FPDI fails
                copy($fullPath, $fullSignedPath);

                Log::info('PDF copied without modification as fallback', [
                    'originalPath' => $filePath,
                    'signedPath' => $signedFilePath
                ]);
            }

            // Supprimer le fichier temporaire de signature
            unlink($tempSignatureFile);

            Log::info('Document PDF signé avec succès (copie sans modification)', [
                'originalPath' => $filePath,
                'signedPath' => $signedFilePath
            ]);

            return $signedFilePath;
        } catch (\Exception $e) {
            Log::error('Erreur lors de la signature du document PDF', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }
}
