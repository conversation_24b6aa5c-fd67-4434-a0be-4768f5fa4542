import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

/**
 * A service that provides safe access to browser storage (localStorage/sessionStorage)
 * with fallbacks for server-side rendering
 */
@Injectable({
  providedIn: 'root'
})
export class BrowserStorageService {
  private isBrowser: boolean;
  private memoryStorage: Map<string, string> = new Map();

  constructor(@Inject(PLATFORM_ID) platformId: Object) {
    this.isBrowser = isPlatformBrowser(platformId);
    console.log(`BrowserStorageService initialized, isBrowser: ${this.isBrowser}`);
  }

  /**
   * Gets an item from localStorage with SSR safety
   */
  getItem(key: string): string | null {
    if (this.isBrowser) {
      try {
        return localStorage.getItem(key);
      } catch (error) {
        console.error('Error accessing localStorage:', error);
        return this.memoryStorage.get(key) || null;
      }
    } else {
      // Use in-memory fallback for SSR
      return this.memoryStorage.get(key) || null;
    }
  }

  /**
   * Sets an item in localStorage with SSR safety
   */
  setItem(key: string, value: string): void {
    // Always update memory storage
    this.memoryStorage.set(key, value);

    // Update localStorage only in browser
    if (this.isBrowser) {
      try {
        localStorage.setItem(key, value);
      } catch (error) {
        console.error('Error writing to localStorage:', error);
      }
    }
  }

  /**
   * Removes an item from localStorage with SSR safety
   */
  removeItem(key: string): void {
    // Always update memory storage
    this.memoryStorage.delete(key);

    // Update localStorage only in browser
    if (this.isBrowser) {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error('Error removing from localStorage:', error);
      }
    }
  }

  /**
   * Clears all items from localStorage with SSR safety
   */
  clear(): void {
    // Always clear memory storage
    this.memoryStorage.clear();

    // Clear localStorage only in browser
    if (this.isBrowser) {
      try {
        localStorage.clear();
      } catch (error) {
        console.error('Error clearing localStorage:', error);
      }
    }
  }
}
