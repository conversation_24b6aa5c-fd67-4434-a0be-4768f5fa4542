<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UnsignedContract;
use App\Services\DocumentGenerationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class DocumentGenerationController extends Controller
{
    protected $documentGenerator;

    public function __construct(DocumentGenerationService $documentGenerator)
    {
        $this->documentGenerator = $documentGenerator;
    }

    /**
     * Generate a document with placeholders replaced by values
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generate(Request $request)
    {
        Log::info('Document generation request received');
        Log::debug('Request data: ' . json_encode($request->all()));

        // Check if user is authenticated
        if (!Auth::check()) {
            Log::warning('Unauthenticated user trying to generate document');
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to generate documents'
            ], 401);
        }

        $userId = Auth::id();
        Log::info('Document generation for user', ['user_id' => $userId]);

        // Validate the request with more flexible rules
        $validator = Validator::make($request->all(), [
            'filename' => 'string|nullable',
            'file_path' => 'string|nullable',
            'replacements' => 'present|array',
            'document_name' => 'nullable|string',
            'is_partner_contract' => 'boolean|nullable'
        ]);

        if ($validator->fails()) {
            Log::error('Document generation validation failed: ' . json_encode($validator->errors()));
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', array_map(function($error) {
                    return $error[0];
                }, $validator->errors()->toArray())),
                'errors' => $validator->errors()
            ], 422);
        }

        // Additional validation: either filename or file_path must be provided
        if (empty($request->input('filename')) && empty($request->input('file_path'))) {
            Log::error('Document generation validation failed: Neither filename nor file_path provided');
            return response()->json([
                'success' => false,
                'message' => 'Either filename or file_path must be provided',
                'errors' => ['filename' => ['Either filename or file_path must be provided']]
            ], 422);
        }

        // Support both formats: filename or file_path
        $filePath = $request->input('file_path');
        $filename = $request->input('filename');
        $documentName = $request->input('document_name');
        $replacements = $request->input('replacements');
        $isPartnerContract = $request->input('is_partner_contract', false);

        // Log the request details for debugging
        Log::info('Document generation request details', [
            'file_path' => $filePath,
            'filename' => $filename,
            'document_name' => $documentName,
            'has_replacements' => !empty($replacements),
            'is_partner_contract' => $isPartnerContract
        ]);

        // If file_path is provided, use it directly
        if ($filePath) {
            $originalFilePath = $filePath;
            // Check if this is a partner contract path (should start with 'contracts/')
            Log::info('Using file_path for document generation: ' . $originalFilePath);
        } else if ($filename) {
            // Otherwise construct from filename for uploaded files
            $originalFilePath = 'uploads/' . $filename;
            Log::info('Using uploads path for document generation: ' . $originalFilePath);
        } else {
            // Neither provided
            Log::error('No file path or filename provided');
            return response()->json([
                'success' => false,
                'message' => 'Either filename or file_path must be provided'
            ], 422);
        }

        // Special handling for contract files
        if (strpos($originalFilePath, 'contracts/') === 0 || $isPartnerContract) {
            // This is a partner contract, look up the actual file
            $contractFiles = Storage::disk('public')->files('contracts');
            Log::info('Available contract files in contracts directory:', $contractFiles);

            // Check if there are any contract files available
            if (count($contractFiles) > 0) {
                // Try to find an exact match first
                $exactMatch = false;
                foreach ($contractFiles as $contractFile) {
                    if (basename($contractFile) === basename($originalFilePath)) {
                        $originalFilePath = $contractFile;
                        $exactMatch = true;
                        Log::info('Found exact match for contract file: ' . $originalFilePath);
                        break;
                    }
                }

                // If no exact match, use the first available contract file
                if (!$exactMatch) {
                    // If we have a filename, try to find a match based on the filename
                    if ($filename) {
                        foreach ($contractFiles as $contractFile) {
                            if (basename($contractFile) === $filename) {
                                $originalFilePath = $contractFile;
                                $exactMatch = true;
                                Log::info('Found match for contract file by filename: ' . $originalFilePath);
                                break;
                            }
                        }
                    }

                    // If still no match, use the first available contract file
                    if (!$exactMatch) {
                        $originalFilePath = $contractFiles[0];
                        Log::info('No exact match found, using first available contract file: ' . $originalFilePath);
                    }
                }
            } else {
                Log::error('No contract files found in contracts directory');
                return response()->json([
                    'success' => false,
                    'message' => 'No contract files found in the contracts directory'
                ], 404);
            }
        }

        // Final check if the file exists
        if (!Storage::disk('public')->exists($originalFilePath)) {
            Log::error('File not found in storage after all attempts: ' . $originalFilePath);

            // Check if the uploads directory exists
            if (!Storage::disk('public')->exists('uploads')) {
                Storage::disk('public')->makeDirectory('uploads');
                Log::info('Created uploads directory');
            }

            // Check if the contracts directory exists
            if (!Storage::disk('public')->exists('contracts')) {
                Storage::disk('public')->makeDirectory('contracts');
                Log::info('Created contracts directory');
            }

            // Try to find any file in the uploads directory as a fallback
            $uploadFiles = Storage::disk('public')->files('uploads');
            if (count($uploadFiles) > 0) {
                Log::info('Using fallback file from uploads directory: ' . $uploadFiles[0]);
                $originalFilePath = $uploadFiles[0];
            } else {
                // We need a valid DOCX file for document generation
                // First, check if we have a template in various locations
                $templateLocations = [
                    storage_path('app/sample_document.docx'),
                    storage_path('app/templates/sample_document.docx'),
                    base_path('resources/templates/sample_document.docx'),
                    public_path('templates/sample_document.docx'),
                    __DIR__ . '/../../../../../../resources/templates/sample_document.docx'
                ];

                $foundTemplate = false;
                foreach ($templateLocations as $templatePath) {
                    if (file_exists($templatePath)) {
                        Log::info('Found template file at: ' . $templatePath);
                        $sampleFilePath = 'uploads/contract_1.docx';
                        Storage::disk('public')->put($sampleFilePath, file_get_contents($templatePath));
                        $foundTemplate = true;
                        break;
                    }
                }

                if (!$foundTemplate) {
                    // As a last resort, create a basic DOCX file using PhpWord
                    Log::info('No template found, creating a basic DOCX file using PhpWord');

                    try {
                        // Create a new PhpWord instance
                        $phpWord = new \PhpOffice\PhpWord\PhpWord();

                        // Add a section
                        $section = $phpWord->addSection();

                        // Add text with placeholders
                        $section->addText('This is a sample document for testing.');
                        $section->addText('');
                        $section->addText('It contains placeholders like ${name} and ${date}.');

                        // Save the document
                        $sampleFilePath = 'uploads/contract_1.docx';
                        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');

                        // Save to a temporary file first
                        $tempFile = tempnam(sys_get_temp_dir(), 'docx_');
                        $objWriter->save($tempFile);

                        // Then move to storage
                        Storage::disk('public')->put($sampleFilePath, file_get_contents($tempFile));

                        // Clean up
                        unlink($tempFile);
                    } catch (\Exception $e) {
                        Log::error('Failed to create sample DOCX file: ' . $e->getMessage());
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to create a valid document template: ' . $e->getMessage()
                        ], 500);
                    }
                }

                Log::info('Created sample document for testing: ' . $sampleFilePath);
                $originalFilePath = $sampleFilePath;
            }

            // If we still don't have a valid file, return an error
            if (!Storage::disk('public')->exists($originalFilePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Original file not found: ' . $originalFilePath . ' (Check that the file exists in storage)'
                ], 404);
            }

            Log::info('Using fallback file: ' . $originalFilePath);
        }

        Log::info('Found file at path: ' . $originalFilePath);

        // Validate replacements before proceeding
        if (empty($replacements)) {
            Log::warning('No replacements provided for document generation');
        }

        // Log the replacements for debugging
        Log::info('Replacements for document generation:', [
            'count' => count($replacements),
            'keys' => array_keys($replacements)
        ]);

        // Generate the document
        try {
            // Check if the file exists and is accessible
            if (!Storage::disk('public')->exists($originalFilePath)) {
                Log::error('File not found before generation: ' . $originalFilePath);
                return response()->json([
                    'success' => false,
                    'message' => 'File not found before generation: ' . $originalFilePath
                ], 404);
            }

            // Check file size
            $fileSize = Storage::disk('public')->size($originalFilePath);
            if ($fileSize === 0) {
                Log::error('File is empty: ' . $originalFilePath);
                return response()->json([
                    'success' => false,
                    'message' => 'File is empty: ' . $originalFilePath
                ], 400);
            }

            // Log file details
            $filePath = Storage::disk('public')->path($originalFilePath);
            $mimeType = file_exists($filePath) ? mime_content_type($filePath) : 'unknown';

            Log::info('File details before generation:', [
                'path' => $originalFilePath,
                'size' => $fileSize,
                'mime' => $mimeType
            ]);

            $generatedFilePath = $this->documentGenerator->generateDocument($originalFilePath, $replacements, $documentName);

            if (!$generatedFilePath) {
                Log::error('Failed to generate document - service returned null');
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate document. The document generation service encountered an error.'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Exception during document generation: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during document generation: ' . $e->getMessage()
            ], 500);
        }

        // Determine file type
        $fileType = pathinfo($filename, PATHINFO_EXTENSION);
        if (in_array($fileType, ['doc', 'docx'])) {
            $fileType = 'word';
        } else if ($fileType === 'pdf') {
            $fileType = 'pdf';
        } else {
            $fileType = 'other';
        }

        // Save to unsigned_contracts table
        try {
            $unsignedContract = new UnsignedContract([
                'nom_contrat' => pathinfo($filename, PATHINFO_FILENAME),
                'file_path' => $generatedFilePath,
                'file_type' => $fileType,
                'form_data' => $replacements,
                'status' => 'pending'
            ]);

            // Always associate with the authenticated user (we've already checked authentication above)
            $unsignedContract->user_id = Auth::id();
            Log::info('Associated unsigned contract with user', ['user_id' => Auth::id()]);

            $unsignedContract->save();
            Log::info('Unsigned contract saved to database', ['id' => $unsignedContract->id]);
        } catch (\Exception $e) {
            Log::error('Failed to save unsigned contract to database', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Continue even if saving to database fails
        }

        // Create the response
        $response = response()->json([
            'success' => true,
            'message' => 'Document generated successfully',
            'file' => [
                'name' => basename($generatedFilePath),
                'path' => $generatedFilePath,
                'download_url' => url('api/documents/download/' . basename($generatedFilePath)),
                'unsigned_contract_id' => $unsignedContract->id ?? null
            ]
        ]);

        // Add the document ID to the response headers
        if (isset($unsignedContract) && $unsignedContract->id) {
            $response->header('X-Document-Id', $unsignedContract->id);

            // Also set a cookie with the document ID as a fallback
            $response->cookie('document_id', $unsignedContract->id, 60); // 60 minutes

            Log::info('Added document ID to response', ['id' => $unsignedContract->id]);
        }

        return $response;
    }

    /**
     * Download a generated document
     *
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function download($filename)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            Log::warning('Unauthenticated user trying to download generated document');
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to download documents'
            ], 401);
        }

        $userId = Auth::id();
        $path = 'generated/' . $filename;
        Log::info('Attempting to download generated file for user', ['user_id' => $userId, 'path' => $path]);

        // Check if the generated directory exists
        if (!Storage::disk('public')->exists('generated')) {
            Log::error('Generated directory does not exist');
            Storage::disk('public')->makeDirectory('generated');
            Log::info('Created generated directory');
        }

        if (!Storage::disk('public')->exists($path)) {
            Log::error('Generated file not found: ' . $path);
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        $file = Storage::disk('public')->path($path);
        $type = \Illuminate\Support\Facades\File::mimeType($file);

        $headers = [
            'Content-Type' => $type,
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        Log::info('Downloading generated file: ' . $path);
        return response()->download($file, $filename, $headers);
    }
}
