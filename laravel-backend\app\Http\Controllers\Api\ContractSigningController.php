<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SignedContract;
use App\Models\UnsignedContract;
use App\Services\DocumentSigningService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ContractSigningController extends Controller
{
    /**
     * @var DocumentSigningService
     */
    protected $documentSigningService;

    /**
     * Constructor
     */
    public function __construct(DocumentSigningService $documentSigningService)
    {
        $this->documentSigningService = $documentSigningService;
    }
    /**
     * Sign a PDF contract
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function signPdf(Request $request)
    {
        try {
            Log::info('Contract signing process started');

            // Check if user is authenticated
            if (!Auth::check()) {
                Log::warning('Unauthenticated user trying to sign PDF contract');
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to sign contracts'
                ], 401);
            }

            // Validate request
            $validator = Validator::make($request->all(), [
                'nom_contrat' => 'required|string',
                'email_signataire' => 'required|email',
                'signature_data' => 'required|string',
                'pdf_file' => 'required|file|max:10240', // Accept any file type, we'll check extension manually
                'unsigned_contract_id' => 'nullable|integer',
                'signature_position' => 'nullable|string|in:bottom-left,bottom-center,bottom-right',
            ]);

            if ($validator->fails()) {
                Log::warning('Contract signing validation failed', ['errors' => $validator->errors()]);
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get the uploaded file
            $uploadedFile = $request->file('pdf_file');
            $originalFileName = $uploadedFile->getClientOriginalName();
            $fileExtension = $uploadedFile->getClientOriginalExtension();
            $fileType = $uploadedFile->getMimeType();

            Log::info('Processing uploaded file', [
                'originalName' => $originalFileName,
                'extension' => $fileExtension,
                'type' => $fileType
            ]);

            // Determine file type and process accordingly
            // IMPORTANT: We're in the signPdf method, so we should ONLY process PDF documents here
            // If it's a Word document, redirect to the Word signing method

            // Check if this is a PDF or Word document
            $isPdf = in_array(strtolower($fileExtension), ['pdf']) ||
                     strpos($fileType, 'pdf') !== false;

            $isWord = in_array(strtolower($fileExtension), ['doc', 'docx']) ||
                      strpos($fileType, 'word') !== false ||
                      strpos($fileType, 'msword') !== false ||
                      strpos($fileType, 'officedocument') !== false;

            Log::info('File type detection in signPdf method', [
                'isPdf' => $isPdf,
                'isWord' => $isWord,
                'extension' => $fileExtension,
                'mimeType' => $fileType
            ]);

            // If it's a Word document, redirect to the Word signing method
            if ($isWord) {
                Log::info('Detected Word file, redirecting to Word signing method');
                return $this->signWord($request);
            }

            // If it's not a PDF, return an error
            if (!$isPdf) {
                Log::warning('Invalid file type for PDF signing', [
                    'extension' => $fileExtension,
                    'mimeType' => $fileType
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file type. Please upload a PDF document (.pdf).'
                ], 422);
            }

            // Generate a unique filename with timestamp
            $fileName = time() . '_' . $originalFileName;

            // Store the file in the public storage
            $filePath = $uploadedFile->storeAs('contracts/signed', $fileName, 'public');
            Log::info('File stored successfully', ['path' => $filePath]);

            // Get the signer name and signature data
            $signerName = $request->input('nom_contrat');
            $signatureData = $request->input('signature_data');
            $signaturePosition = $request->input('signature_position', 'bottom-right');

            // Process the PDF document with the signature
            $signedFilePath = $this->documentSigningService->signPdfDocument(
                $filePath,
                $signatureData,
                $signerName,
                $signaturePosition
            );

            if (!$signedFilePath) {
                Log::error('Failed to sign PDF document');
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to sign PDF document'
                ], 500);
            }

            // Create a signed contract record in the database
            $signedContract = new SignedContract();
            $signedContract->nom_contrat = $request->input('nom_contrat');
            $signedContract->email_signataire = $request->input('email_signataire');
            $signedContract->signature_data = $request->input('signature_data');
            $signedContract->file_path = $signedFilePath; // Use the signed file path
            $signedContract->file_type = $fileType;

            // Associate with user if authenticated
            if (Auth::check()) {
                $signedContract->user_id = Auth::id();
                Log::info('Associated contract with user', ['user_id' => Auth::id()]);
            } else {
                Log::info('No authenticated user for this contract');
            }

            // Link to unsigned contract if provided
            if ($request->has('unsigned_contract_id')) {
                $unsignedContractId = $request->input('unsigned_contract_id');
                $signedContract->unsigned_contract_id = $unsignedContractId;
                Log::info('Linked to unsigned contract', ['unsigned_contract_id' => $unsignedContractId]);
            }

            $signedContract->save();
            Log::info('Signed contract record created successfully', ['contract_id' => $signedContract->id]);

            // Delete the unsigned contract if it exists
            if ($request->has('unsigned_contract_id')) {
                $unsignedContractId = $request->input('unsigned_contract_id');
                $unsignedContract = UnsignedContract::find($unsignedContractId);

                if ($unsignedContract) {
                    // Delete the file if it exists
                    if ($unsignedContract->file_path && Storage::disk('public')->exists($unsignedContract->file_path)) {
                        Storage::disk('public')->delete($unsignedContract->file_path);
                        Log::info('Deleted unsigned contract file', ['file_path' => $unsignedContract->file_path]);
                    }

                    // Delete the record
                    $unsignedContract->delete();
                    Log::info('Deleted unsigned contract record', ['unsigned_contract_id' => $unsignedContractId]);
                }
            }

            // Return the signed file directly
            $fullPath = Storage::disk('public')->path($signedFilePath);
            $fileName = basename($signedFilePath);

            // Determine the correct MIME type based on file extension
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

            // IMPORTANT: Always use the correct MIME type based on extension
            // This ensures the browser knows how to handle the file
            if ($extension === 'pdf') {
                $contentType = 'application/pdf';
                Log::info('Setting PDF content type for download');
            } else if ($extension === 'doc') {
                $contentType = 'application/msword';
                Log::info('Setting DOC content type for download');
            } else if ($extension === 'docx') {
                $contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                Log::info('Setting DOCX content type for download');
            } else {
                // Fallback to the detected MIME type
                $contentType = $fileType;
                Log::info('Using detected MIME type as fallback', ['mimeType' => $fileType]);
            }

            Log::info('Returning file with content type', [
                'fileName' => $fileName,
                'extension' => $extension,
                'contentType' => $contentType
            ]);

            return response()->download($fullPath, $fileName, [
                'Content-Type' => $contentType,
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('Contract signing failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to sign contract: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sign a Word document
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function signWord(Request $request)
    {
        try {
            Log::info('Word document signing process started');

            // Check if user is authenticated
            if (!Auth::check()) {
                Log::warning('Unauthenticated user trying to sign Word document');
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to sign contracts'
                ], 401);
            }

            // Validate request
            $validator = Validator::make($request->all(), [
                'nom_contrat' => 'required|string',
                'email_signataire' => 'required|email',
                'signature_data' => 'required|string',
                'pdf_file' => 'required|file|max:10240', // Accept any file type, we'll check extension manually
                'unsigned_contract_id' => 'nullable|integer',
                'signature_position' => 'nullable|string|in:bottom-left,bottom-center,bottom-right',
            ]);

            if ($validator->fails()) {
                Log::warning('Word document signing validation failed', ['errors' => $validator->errors()]);
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get the uploaded file
            $uploadedFile = $request->file('pdf_file');
            $originalFileName = $uploadedFile->getClientOriginalName();
            $fileExtension = $uploadedFile->getClientOriginalExtension();
            $fileType = $uploadedFile->getMimeType();

            Log::info('File details for signing', [
                'originalName' => $originalFileName,
                'extension' => $fileExtension,
                'mimeType' => $fileType
            ]);

            // Accept any file type for signing - we'll handle it appropriately
            // This allows both PDF and Word documents to be signed through this endpoint

            // Determine file type and process accordingly
            // IMPORTANT: We're in the signWord method, so we should ONLY process Word documents here
            // Do NOT redirect to PDF signing method, as that would convert the file

            // Check if this is actually a Word document
            $isWord = in_array(strtolower($fileExtension), ['doc', 'docx']) ||
                      strpos($fileType, 'word') !== false ||
                      strpos($fileType, 'msword') !== false ||
                      strpos($fileType, 'officedocument') !== false;

            Log::info('File type detection in signWord method', [
                'isWord' => $isWord,
                'extension' => $fileExtension,
                'mimeType' => $fileType
            ]);

            // If not a Word document, return an error
            if (!$isWord) {
                Log::warning('Invalid file type for Word document signing', [
                    'extension' => $fileExtension,
                    'mimeType' => $fileType
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file type. Please upload a Word document (.doc or .docx).'
                ], 422);
            }

            Log::info('Processing uploaded Word document', [
                'originalName' => $originalFileName,
                'extension' => $fileExtension,
                'type' => $fileType
            ]);

            // Generate a unique filename with timestamp
            $fileName = time() . '_' . $originalFileName;

            // Store the file in the public storage
            $filePath = $uploadedFile->storeAs('contracts/signed', $fileName, 'public');
            Log::info('Word document stored successfully', ['path' => $filePath]);

            // Get the signer name and signature data
            $signerName = $request->input('nom_contrat');
            $signatureData = $request->input('signature_data');

            // Process the Word document with the signature
            $signedFilePath = $this->documentSigningService->signWordDocument($filePath, $signatureData, $signerName);

            if (!$signedFilePath) {
                Log::error('Failed to sign Word document');
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to sign Word document'
                ], 500);
            }

            // Create a signed contract record in the database
            $signedContract = new SignedContract();
            $signedContract->nom_contrat = $request->input('nom_contrat');
            $signedContract->email_signataire = $request->input('email_signataire');
            $signedContract->signature_data = $request->input('signature_data');
            $signedContract->file_path = $signedFilePath; // Use the signed file path
            $signedContract->file_type = $fileType;

            // Associate with user if authenticated
            if (Auth::check()) {
                $signedContract->user_id = Auth::id();
                Log::info('Associated Word document with user', ['user_id' => Auth::id()]);
            } else {
                Log::info('No authenticated user for this Word document');
            }

            // Link to unsigned contract if provided
            if ($request->has('unsigned_contract_id')) {
                $unsignedContractId = $request->input('unsigned_contract_id');
                $signedContract->unsigned_contract_id = $unsignedContractId;
                Log::info('Linked Word document to unsigned contract', ['unsigned_contract_id' => $unsignedContractId]);
            }

            $signedContract->save();
            Log::info('Signed Word document record created successfully', ['contract_id' => $signedContract->id]);

            // Delete the unsigned contract if it exists
            if ($request->has('unsigned_contract_id')) {
                $unsignedContractId = $request->input('unsigned_contract_id');
                $unsignedContract = UnsignedContract::find($unsignedContractId);

                if ($unsignedContract) {
                    // Delete the file if it exists
                    if ($unsignedContract->file_path && Storage::disk('public')->exists($unsignedContract->file_path)) {
                        Storage::disk('public')->delete($unsignedContract->file_path);
                        Log::info('Deleted unsigned contract file', ['file_path' => $unsignedContract->file_path]);
                    }

                    // Delete the record
                    $unsignedContract->delete();
                    Log::info('Deleted unsigned contract record', ['unsigned_contract_id' => $unsignedContractId]);
                }
            }

            // Return the signed file directly
            $fullPath = Storage::disk('public')->path($signedFilePath);
            $fileName = basename($signedFilePath);

            // Determine the correct MIME type based on file extension
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

            // IMPORTANT: Always use the correct MIME type based on extension
            // This ensures the browser knows how to handle the file
            if ($extension === 'pdf') {
                $contentType = 'application/pdf';
                Log::info('Setting PDF content type for download');
            } else if ($extension === 'doc') {
                $contentType = 'application/msword';
                Log::info('Setting DOC content type for download');
            } else if ($extension === 'docx') {
                $contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                Log::info('Setting DOCX content type for download');
            } else {
                // Fallback to the detected MIME type
                $contentType = $fileType;
                Log::info('Using detected MIME type as fallback', ['mimeType' => $fileType]);
            }

            Log::info('Returning file with content type', [
                'fileName' => $fileName,
                'extension' => $extension,
                'contentType' => $contentType
            ]);

            return response()->download($fullPath, $fileName, [
                'Content-Type' => $contentType,
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('Word document signing failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to sign Word document: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get an unsigned contract by ID
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnsignedContract($id)
    {
        try {
            Log::info('Fetching unsigned contract details', ['contract_id' => $id]);

            // Check if user is authenticated
            if (!Auth::check()) {
                Log::warning('Unauthenticated user trying to access unsigned contract', ['contract_id' => $id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to access contracts'
                ], 401);
            }

            // Find the contract for the authenticated user
            $contract = UnsignedContract::where('user_id', Auth::id())
                ->where('id', $id)
                ->first();

            if ($contract) {
                Log::info('Found unsigned contract for authenticated user', ['contract_id' => $id]);
                return response()->json($contract);
            }

            // If not found, return 404
            Log::warning('Unsigned contract not found for user', [
                'user_id' => Auth::id(),
                'contract_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unsigned contract not found or not authorized'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error fetching unsigned contract details', [
                'contract_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Unsigned contract not found'
            ], 404);
        }
    }

    /**
     * Download an unsigned contract file
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function downloadUnsignedContract($id)
    {
        try {
            Log::info('Downloading unsigned contract', ['contract_id' => $id]);

            // Check if user is authenticated
            if (!Auth::check()) {
                Log::warning('Unauthenticated user trying to download unsigned contract', ['contract_id' => $id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to download contracts'
                ], 401);
            }

            // Try to find the contract for the authenticated user
            $contract = UnsignedContract::where('user_id', Auth::id())
                ->where('id', $id)
                ->first();

            if (!$contract) {
                Log::warning('Unsigned contract not found for user or unauthorized access', [
                    'user_id' => Auth::id(),
                    'contract_id' => $id
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Unsigned contract not found or not authorized'
                ], 404);
            }

            Log::info('Found unsigned contract for download', [
                'user_id' => Auth::id(),
                'contract_id' => $id
            ]);

            // Check if the file exists
            if (!$contract->file_path || !Storage::disk('public')->exists($contract->file_path)) {
                Log::error('Unsigned contract file not found', [
                    'contract_id' => $id,
                    'file_path' => $contract->file_path
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Contract file not found'
                ], 404);
            }

            $filePath = Storage::disk('public')->path($contract->file_path);
            $fileName = basename($contract->file_path);
            $fileType = $contract->file_type ?? 'application/octet-stream';

            Log::info('Downloading unsigned contract file', [
                'contract_id' => $id,
                'file_path' => $filePath,
                'file_name' => $fileName,
                'file_type' => $fileType
            ]);

            return response()->download($filePath, $fileName, [
                'Content-Type' => $fileType,
            ]);
        } catch (\Exception $e) {
            Log::error('Error downloading unsigned contract', [
                'contract_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to download unsigned contract'
            ], 500);
        }
    }
}
