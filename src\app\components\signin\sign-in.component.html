<div class="signin-container">
  <!-- Left Side - Magenta Section with professional pattern -->
  <div class="signin-left">
    <div class="logo-section">
      <img src="assets/images/logo.png" alt="ContratPro Logo" class="logo" />
      <a routerLink="/" class="back-link"><i class="material-icons" style="font-size: 16px; vertical-align: middle; margin-right: 4px;text-align: right;">arrow_back</i> Back to website</a>
    </div>
    <div class="hero-content">
      <h2>Welcome Back to<br>ContratPro</h2>
      <p class="hero-subtitle">Sign in to access your contract management dashboard.</p>
      <div class="contract-icon">
        <i class="fas fa-file-contract"></i>
      </div>
    </div>
  </div>

  <div class="signin-right">
    <div class="signin-form-container">
      <h1>Sign In</h1>
      <p class="signup-link">Don't have an account? <a routerLink="/signup">Sign up</a></p>

      <form [formGroup]="signinForm" (ngSubmit)="onSubmit()" class="signin-form">
        <div *ngIf="message" class="message" [class.error]="message.includes('error')">
          {{ message }}
        </div>

        <div class="form-field">
          <input type="email"
                 formControlName="email"
                 placeholder="Email address" />
        </div>

        <div class="form-field password-field">
          <input [type]="showPassword ? 'text' : 'password'"
                 formControlName="password"
                 placeholder="Password" />
          <button type="button"
                  class="toggle-password"
                  (click)="togglePasswordVisibility()">
            <i class="bi" [class.bi-eye]="!showPassword" [class.bi-eye-slash]="showPassword"></i>
          </button>
        </div>

        <div class="form-options">
          <label class="remember-me">
            <input type="checkbox" formControlName="remember" />
            <span>Remember me</span>
          </label>
          <a routerLink="/forgot-password" class="forgot-password-link">Forgot Password?</a>
        </div>

        <div class="form-group">
          <button type="submit" [disabled]="isLoading || !signinForm.valid">
            <span *ngIf="!isLoading">Sign In</span>
            <span *ngIf="isLoading">Signing in...</span>
          </button>
        </div>

        <div class="social-login-divider">
          <span>or continue with</span>
        </div>

        <div class="social-login-buttons">
          <a [href]="googleAuthUrl" class="social-button google">
            <img src="assets/images/google.png" alt="Google" />
             Google
          </a>
          <a [href]="facebookAuthUrl" class="social-button facebook">
            <img src="assets/images/facebook.png" alt="Facebook" />
            Facebook
          </a>
        </div>


      </form>
    </div>
  </div>
</div>
