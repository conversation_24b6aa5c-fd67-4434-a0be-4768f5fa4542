import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private apiUrl = environment.apiUrl + '/api/admin';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Get auth headers for API requests
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken();
    let headers = new HttpHeaders()
      .set('Accept', 'application/json')
      .set('Content-Type', 'application/json');

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    } else {
      console.error('No auth token available for admin API request');
    }

    return headers;
  }

  /**
   * Get dashboard statistics
   */
  getDashboardStats(dateRange: string = 'last12Months'): Observable<any> {
    console.log('Fetching admin dashboard stats with auth token');
    return this.http.get(`${this.apiUrl}/dashboard-stats`, {
      headers: this.getAuthHeaders(),
      params: { date_range: dateRange },
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error fetching dashboard stats:', error);
        return throwError(() => new Error('Failed to load dashboard statistics. Please try again.'));
      })
    );
  }

  /**
   * Get all users
   */
  getUsers(): Observable<any> {
    return this.http.get(`${this.apiUrl}/users`, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error fetching users:', error);
        return throwError(() => new Error('Failed to load users. Please try again.'));
      })
    );
  }

  /**
   * Get a specific user by ID
   */
  getUser(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/users/${id}`, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error(`Error fetching user ${id}:`, error);
        return throwError(() => new Error('Failed to load user details. Please try again.'));
      })
    );
  }

  /**
   * Create a new user
   */
  createUser(userData: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/users`, userData, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error creating user:', error);
        return throwError(() => new Error('Failed to create user. Please try again.'));
      })
    );
  }

  /**
   * Update an existing user
   */
  updateUser(id: number, userData: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/users/${id}`, userData, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error(`Error updating user ${id}:`, error);
        return throwError(() => new Error('Failed to update user. Please try again.'));
      })
    );
  }

  /**
   * Delete a user
   */
  deleteUser(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/users/${id}`, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error(`Error deleting user ${id}:`, error);
        return throwError(() => new Error('Failed to delete user. Please try again.'));
      })
    );
  }


}
