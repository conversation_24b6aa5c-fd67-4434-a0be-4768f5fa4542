<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php', // ✅ Add API route handling
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Apply CORS middleware globally
        $middleware->append(\App\Http\Middleware\CorsMiddleware::class);

        // Also apply to API group
        $middleware->group('api', [
            \App\Http\Middleware\CorsMiddleware::class,
        ]);

        // Register named middleware
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->withProviders([
        \App\Providers\AppServiceProvider::class,
        \App\Providers\AuthServiceProvider::class,
        \App\Providers\RouteServiceProvider::class,
        \App\Providers\OAuthServiceProvider::class,
        \App\Providers\GuzzleServiceProvider::class,
        \App\Providers\SocialiteServiceProvider::class,
    ])
    ->create();
