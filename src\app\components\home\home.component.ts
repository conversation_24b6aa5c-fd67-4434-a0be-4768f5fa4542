import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Observable, Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit, OnDestroy {
  activeFeature: 'generer' | 'valider-signer' | 'analyser' = 'generer';
  errorMessage = '';
  isLoggedIn$: Observable<boolean>;
  private authSubscription: Subscription | null = null;
  isLoggedIn = false;

  constructor(
    private router: Router,
    private authService: AuthService
  ) {
    this.isLoggedIn$ = this.authService.authState$;
  }

  ngOnInit(): void {
    // Subscribe to auth state changes
    this.authSubscription = this.isLoggedIn$.subscribe(
      isLoggedIn => {
        this.isLoggedIn = isLoggedIn;
      }
    );
  }

  ngOnDestroy(): void {
    // Clean up subscription
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  showContent(feature: 'generer' | 'valider-signer' | 'analyser'): void {
    this.activeFeature = feature;
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  // Navigate to file upload
  goToUpload(): void {
    if (this.isLoggedIn) {
      this.router.navigate(['/file-upload']);
    } else {
      this.router.navigate(['/signin'], { queryParams: { returnUrl: '/file-upload' } });
    }
  }

  // Navigate to electronic signature
  goToSignature(): void {
    if (this.isLoggedIn) {
      this.router.navigate(['/security']);
    } else {
      this.router.navigate(['/signin'], { queryParams: { returnUrl: '/security' } });
    }
  }

  // Navigate to dashboard
  goToDashboard(): void {
    if (this.isLoggedIn) {
      this.router.navigate(['/dashboard']);
    } else {
      this.router.navigate(['/signin'], { queryParams: { returnUrl: '/dashboard' } });
    }
  }
}
