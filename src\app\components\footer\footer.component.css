/* Modern Footer Styles */

/* Variables */
:root {
  --footer-bg: #29032825;
  --footer-text: #ffffff;
  --footer-text-secondary: rgba(255, 255, 255, 0.7);
  --footer-accent: #4ECDC4;
  --footer-accent-hover: #6CDED6;
  --footer-divider: rgba(255, 255, 255, 0.1);
  --footer-input-bg: rgba(255, 255, 255, 0.05);
  --footer-input-border: rgba(255, 255, 255, 0.1);
  --footer-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Footer Container */
.footer-container {
  margin-top: 10px;
  position: relative;
  width: 100%;
  margin-top: 4rem;
}

/* Wave Divider */
.footer-wave {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.footer-wave svg {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
  left: 0;
  fill: var(--footer-bg);
}

/* Main Footer */
.footer {
  background-color: var(--footer-bg);
  color: var(--footer-text);
  padding: 0 0 1.5rem 0;
  width: 100%;
  position: relative;
  z-index: 10;
}

/* Footer Content Grid */
.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  padding-bottom: 3rem;
}

/* Footer Sections */
.footer-section {
  margin-bottom: 1rem;
}

.footer-section h3 {
  color: var(--footer-text);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--footer-accent), var(--footer-accent-hover));
  border-radius: 3px;
}

.footer-section p {
  color: var(--footer-text-secondary);
  line-height: 1.7;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

/* Company Info Section */
.company-info {
  display: flex;
  flex-direction: column;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
}

.footer-logo img {
  height: 40px;
  margin-right: 0.75rem;
}

.footer-logo span {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--footer-text);
  letter-spacing: 0.5px;
}

/* Social Links */
.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--footer-input-bg);
  color: var(--footer-text);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--footer-input-border);
}

.social-links a i {
  font-size: 1rem;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.social-links a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--footer-accent), var(--footer-accent-hover));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.social-links a:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(78, 205, 196, 0.3);
}

.social-links a:hover::before {
  opacity: 1;
}

.social-links a:hover i {
  color: white;
  transform: scale(1.1);
}

/* Quick Links Section */
.footer-section.links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section.links li {
  margin-bottom: 0.9rem;
  transition: transform 0.3s ease;
}

.footer-section.links li:hover {
  transform: translateX(5px);
}

.footer-section.links a {
  color: var(--footer-text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  font-size: 0.95rem;
}

.footer-section.links a i {
  font-size: 0.7rem;
  margin-right: 0.5rem;
  color: var(--footer-accent);
  transition: transform 0.3s ease;
}

.footer-section.links a:hover {
  color: var(--footer-text);
}

.footer-section.links a:hover i {
  transform: translateX(3px);
}

/* Contact Section */
.contact-info {
  list-style: none;
  padding: 0;
  margin: 0;
}

.contact-info li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.25rem;
  color: var(--footer-text-secondary);
}

.contact-info i {
  margin-right: 1rem;
  color: var(--footer-accent);
  font-size: 1rem;
  margin-top: 0.25rem;
}

.contact-info span {
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Newsletter Section */
.newsletter-form {
  margin-top: 1.25rem;
}

.input-group {
  display: flex;
  position: relative;
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.newsletter-form input {
  padding: 0.9rem 1.25rem;
  border: 1px solid var(--footer-input-border);
  background: var(--footer-input-bg);
  color: var(--footer-text);
  flex: 1;
  border-radius: 50px 0 0 50px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.newsletter-form input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--footer-accent);
}

.btn-subscribe {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--footer-accent), var(--footer-accent-hover));
  color: white;
  border: none;
  border-radius: 0 50px 50px 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-subscribe i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.btn-subscribe:hover {
  background: linear-gradient(135deg, var(--footer-accent-hover), var(--footer-accent));
}

.btn-subscribe:hover i {
  transform: translateX(3px);
}

.newsletter-consent {
  margin-top: 1rem;
  font-size: 0.8rem;
  color: var(--footer-text-secondary);
}

.newsletter-consent a {
  color: var(--footer-text);
  text-decoration: none;
  transition: color 0.3s ease;
}

.newsletter-consent a:hover {
  color: var(--footer-accent);
}

/* Footer Divider */
.footer-divider {
  height: 1px;
  background: var(--footer-divider);
  margin: 0 2rem;
}

/* Footer Bottom */
.footer-bottom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright p {
  color: var(--footer-text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-links a {
  color: var(--footer-text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  position: relative;
}

.footer-links a::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--footer-accent);
  transition: width 0.3s ease;
}

.footer-links a:hover {
  color: var(--footer-text);
}

.footer-links a:hover::after {
  width: 100%;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .footer-section {
    margin-bottom: 0;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .footer-links {
    justify-content: center;
    flex-wrap: wrap;
  }

  .footer-wave {
    height: 80px;
  }
}

@media (max-width: 480px) {
  .footer-content {
    padding: 0 1rem;
  }

  .footer-bottom {
    padding: 1.5rem 1rem;
  }

  .social-links {
    justify-content: center;
  }

  .footer-section h3 {
    text-align: center;
  }

  .footer-section h3::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-section p {
    text-align: center;
  }

  .footer-logo {
    justify-content: center;
  }
}
