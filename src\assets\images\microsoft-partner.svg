<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="450" viewBox="0 0 600 450" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1a1a1a"/>
      <stop offset="100%" stop-color="#2d2d2d"/>
    </linearGradient>
    <linearGradient id="awardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4ECDC4"/>
      <stop offset="100%" stop-color="#3DACA4"/>
    </linearGradient>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="4" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Main Background -->
  <rect width="600" height="450" rx="20" fill="url(#bgGradient)"/>

  <!-- Decorative Elements -->
  <circle cx="50" cy="400" r="100" fill="url(#awardGradient)" opacity="0.05"/>
  <circle cx="550" cy="50" r="80" fill="url(#awardGradient)" opacity="0.05"/>
  <path d="M0,200 Q150,150 300,200 T600,200" stroke="url(#awardGradient)" stroke-width="1" fill="none" opacity="0.1"/>
  <path d="M0,250 Q150,300 300,250 T600,250" stroke="url(#awardGradient)" stroke-width="1" fill="none" opacity="0.1"/>

  <!-- Microsoft Logo -->
  <g transform="translate(300, 120)" filter="url(#shadow)">
    <rect x="-70" y="-70" width="50" height="50" fill="#f25022"/>
    <rect x="20" y="-70" width="50" height="50" fill="#7fba00"/>
    <rect x="-70" y="0" width="50" height="50" fill="#00a4ef"/>
    <rect x="20" y="0" width="50" height="50" fill="#ffb900"/>
  </g>

  <!-- Award Badge -->
  <g transform="translate(300, 250)">
    <circle cx="0" cy="0" r="120" fill="none" stroke="url(#awardGradient)" stroke-width="2" opacity="0.1"/>
    <circle cx="0" cy="0" r="100" fill="none" stroke="url(#awardGradient)" stroke-width="2" opacity="0.2"/>
    <circle cx="0" cy="0" r="80" fill="none" stroke="url(#awardGradient)" stroke-width="2" opacity="0.3"/>
  </g>

  <!-- Award Text -->
  <g filter="url(#shadow)">
    <text x="300" y="220" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">MICROSOFT PARTNER</text>
    <text x="300" y="270" font-family="Arial, sans-serif" font-size="42" font-weight="bold" fill="url(#awardGradient)" text-anchor="middle" filter="url(#glow)">AWARD 2023</text>
    <text x="300" y="310" font-family="Arial, sans-serif" font-size="20" font-weight="normal" fill="white" text-anchor="middle" opacity="0.8">TUNISIA</text>
  </g>

  <!-- Award Star -->
  <g transform="translate(300, 370)">
    <path d="M0,-30 L6.9,-9.3 L29.4,-9.3 L11.3,3.5 L18.2,24.3 L0,11.5 L-18.2,24.3 L-11.3,3.5 L-29.4,-9.3 L-6.9,-9.3 Z"
          fill="url(#awardGradient)" filter="url(#glow)"/>
  </g>

  <!-- Ribbon -->
  <path d="M450,400 C470,390 490,410 500,430 L520,410 L540,430 L520,450 C510,430 490,450 470,440 Z"
        fill="url(#awardGradient)" filter="url(#shadow)"/>
</svg>
