
<!-- Contact content is now wrapped by the shared container in app.component.html -->
<section class="contact-section" aria-labelledby="contact-heading">
    <div class="feature-content">
      <!-- Header with animation -->
      <header class="section-header">
        <div class="animated-element">
          <h2 id="contact-heading" class="animate__animated animate__fadeInDown">
            Contact the <span class="highlight">ContratPro</span> team
          </h2>
          <p class="section-subtitle animate__animated animate__fadeIn animate__delay-1s">We're here to help and respond within 24 hours</p>
        </div>
        <div class="decorative-dots"></div>
      </header>

      <!-- Responsive grid layout -->
      <div class="contact-grid animate__animated animate__fadeIn animate__delay-1s">
        <!-- Contact details column -->
        <article class="contact-info" aria-labelledby="coordinates-heading">
          <div class="card-decoration"></div>
          <header>
            <h3 id="coordinates-heading" class="info-title">
              <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
              Our Contact Details
            </h3>
          </header>
          <address>
            <div class="info-item">
              <i class="fas fa-building" aria-hidden="true"></i>
              <div>
                <p class="info-label">Headquarters</p>
                <p class="info-value">Tunisia</p>
              </div>
            </div>

            <div class="info-item">
              <i class="fas fa-phone" aria-hidden="true"></i>
              <div>
                <p class="info-label">Phone</p>
                <p class="info-value">
                  <a href="tel:+21670000000" class="contact-link" aria-label="Call the number +216 70 000 000">+216 70 000 000</a>
                </p>
              </div>
            </div>

            <div class="info-item">
              <i class="fas fa-envelope" aria-hidden="true"></i>
              <div>
                <p class="info-label">Email</p>
                <p class="info-value">
                  <a href="mailto:<EMAIL>" class="contact-link" aria-label="Send an <NAME_EMAIL>">contact&#64;insomea.com</a>
                </p>
              </div>
            </div>

            <div class="info-item">
              <i class="fas fa-clock" aria-hidden="true"></i>
              <div>
                <p class="info-label">Working Hours</p>
                <p class="info-value">Mon-Fri: 9AM - 6PM</p>
              </div>
            </div>
          </address>

          <div class="social-links">
            <a href="#" aria-label="Visit our Facebook page" class="social-link">
              <i class="fab fa-facebook-f" aria-hidden="true"></i>
            </a>
            <a href="#" aria-label="Visit our Twitter profile" class="social-link">
              <i class="fab fa-twitter" aria-hidden="true"></i>
            </a>
            <a href="#" aria-label="Visit our LinkedIn profile" class="social-link">
              <i class="fab fa-linkedin-in" aria-hidden="true"></i>
            </a>
          </div>
        </article>

        <!-- Map column -->
        <figure class="contact-map">
          <div class="feature-image">
            <div class="map-overlay"></div>
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d204405.32993923574!2d10.056054837210921!3d36.79500263543107!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12fd337f5e7ef543%3A0xd671924e714a0275!2sTunis%2C%20Tunisia!5e0!3m2!1sen!2suk!4v1590059823508!5m2!1sen!2suk"
              title="Location of ContratPro headquarters on the map"
              loading="lazy"
              aria-hidden="false"
              tabindex="0"
            ></iframe>
          </div>
          <figcaption class="map-caption">
            <i class="fas fa-map-pin" aria-hidden="true"></i>
            Location of our headquarters
          </figcaption>
        </figure>
      </div>

      <!-- Success Message with enhanced animation -->
      <div *ngIf="showSuccessMessage" class="success-message-container animate__animated animate__fadeInUp">
        <div class="success-message">
          <div class="success-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="success-content">
            <h3>Message Sent Successfully!</h3>
            <p>Thank you for contacting us. Our team will review your message and respond within 24 hours.</p>
            <p class="redirect-text">Redirecting to home page...</p>
          </div>
        </div>
      </div>

      <!-- Error Message with enhanced animation -->
      <div *ngIf="errorMessage" class="error-message-container animate__animated animate__fadeInUp">
        <div class="error-message-box">
          <i class="fas fa-exclamation-circle"></i>
          <p>{{ errorMessage }}</p>
        </div>
      </div>

      <!-- Contact Form Container -->
      <div class="form-container animate__animated animate__fadeIn animate__delay-1s">
        <!-- Enhanced Contact Form -->
        <form
          *ngIf="!showSuccessMessage"
          [formGroup]="contactForm"
          (ngSubmit)="onSubmit()"
          class="contact-form"
          aria-labelledby="form-heading"
        >
        <div class="form-decoration"></div>
        <h3 id="form-heading" class="form-title">Send us a message</h3>
        <p class="form-subtitle">Fill out the form below and we'll get back to you as soon as possible.</p>

        <div class="form-grid">
          <!-- Name Group -->
          <div class="form-group">
            <label for="name" class="input-label">
              Full Name
              <span class="required" aria-hidden="true">*</span>
            </label>
            <input
              type="text"
              id="name"
              formControlName="name"
              class="form-control"
              placeholder="Your name"
              aria-required="true"
              [class.is-invalid]="contactForm.get('name')?.invalid && contactForm.get('name')?.touched"
            >
            <div class="error-message" *ngIf="contactForm.get('name')?.invalid && contactForm.get('name')?.touched">
              This field is required
            </div>
          </div>

          <!-- Email Group -->
          <div class="form-group">
            <label for="email" class="input-label">
              Email address
              <span class="required" aria-hidden="true">*</span>
            </label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="form-control"
              placeholder="<EMAIL>"
              aria-required="true"
              [class.is-invalid]="contactForm.get('email')?.invalid && contactForm.get('email')?.touched"
            >
            <div class="error-message"
              *ngIf="contactForm.get('email')?.errors?.['required'] && contactForm.get('email')?.touched">
              Email is required
            </div>
            <div class="error-message"
              *ngIf="contactForm.get('email')?.errors?.['email'] && contactForm.get('email')?.touched">
              Invalid email format
            </div>
          </div>

          <!-- Subject Group -->
          <div class="form-group">
            <label for="subject" class="input-label">
              Subject
              <span class="required" aria-hidden="true">*</span>
            </label>
            <input
              type="text"
              id="subject"
              formControlName="subject"
              class="form-control"
              placeholder="Subject of your message"
              aria-required="true"
              [class.is-invalid]="contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched"
            >
            <div class="error-message" *ngIf="contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched">
              Please specify a subject
            </div>
          </div>

          <!-- Message Group -->
          <div class="form-group full-width">
            <label for="message" class="input-label">
              Your message
              <span class="required" aria-hidden="true">*</span>
            </label>
            <textarea
              id="message"
              formControlName="message"
              class="form-control"
              rows="5"
              placeholder="Describe your request..."
              aria-required="true"
              [class.is-invalid]="contactForm.get('message')?.invalid && contactForm.get('message')?.touched"
            ></textarea>
            <div class="error-message"
              *ngIf="contactForm.get('message')?.invalid && contactForm.get('message')?.touched">
              Please write your message
            </div>
          </div>
        </div>

        <!-- Enhanced Submit Button -->
        <div class="form-actions">
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="!contactForm.valid || isSubmitting"
            aria-label="Send message"
          >
            <i *ngIf="!isSubmitting" class="fas fa-paper-plane" aria-hidden="true"></i>
            <i *ngIf="isSubmitting" class="fas fa-spinner fa-spin" aria-hidden="true"></i>
            {{ isSubmitting ? 'Sending...' : 'Send Message' }}
          </button>
        </div>
        <p class="privacy-note">By submitting this form, you agree to our <a href="#" class="privacy-link">Privacy Policy</a>.</p>
      </form>
      </div>
    </div>
  </section>

