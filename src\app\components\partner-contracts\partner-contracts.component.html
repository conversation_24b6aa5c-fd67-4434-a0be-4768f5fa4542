<div class="partner-contracts-container">
  <header class="section-header">
    <h2 class="animate__fadeInDown">
      <span class="highlight">Partner</span> Contracts
    </h2>
    <p class="section-subtitle">Select a partner to view available contracts or upload documents</p>
  </header>

  <div class="partners-grid-container" [@fadeIn]>
    <!-- Loading spinner for partners -->
    <div *ngIf="isLoadingPartners" class="loading-partners">
      <app-spinner [active]="true"></app-spinner>
      <p>Loading partners...</p>
    </div>

    <!-- No partners message -->
    <div *ngIf="!isLoadingPartners && partners.length === 0" class="no-partners">
      <i class="fas fa-users-slash"></i>
      <p>No partners available</p>
    </div>

    <!-- Partners grid -->
    <div *ngIf="!isLoadingPartners && partners.length > 0" class="partners-grid">
      <div *ngFor="let partner of partners"
           class="partner-card"
           [class.selected]="selectedPartnerId === partner.id"
           (click)="onPartnerSelected(partner.id)"
           [@slideIn]>
        <div class="partner-logo">
          <i *ngIf="partner.logo?.startsWith('fa')" [class]="partner.logo"></i>
          <img *ngIf="partner.logo && !partner.logo.startsWith('fa')" [src]="partner.logo" alt="{{partner.name}}">
        </div>
        <div class="partner-info">
          <h3 class="partner-name">{{ partner.name }}</h3>
          <p *ngIf="partner.description" class="partner-description">{{ partner.description }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Contracts section - Only show if a partner is selected and there are contracts available -->
  <div *ngIf="selectedPartnerId" class="contracts-container" [@fadeIn]>
    <div class="contracts-header">
      <h3 class="contracts-title">
        <i class="fas fa-file-contract"></i>
        Available Contracts for {{ getSelectedPartnerName() }}
      </h3>

    </div>

    <!-- Loading spinner for contracts -->
    <div *ngIf="isLoadingContracts" class="loading-contracts">
      <app-spinner [active]="true"></app-spinner>
      <p>Loading contracts...</p>
    </div>

    <!-- No contracts message -->
    <div *ngIf="!isLoadingContracts && contracts.length === 0" class="no-contracts">
      <i class="fas fa-file-contract"></i>
      <p>No pre-made contracts available for this partner</p>
      <button class="btn-upload-suggestion" (click)="navigateToUpload()">
        <i class="fas fa-cloud-upload-alt"></i>
        Upload your own document
      </button>
    </div>

    <!-- Contracts grid -->
    <div *ngIf="!isLoadingContracts && contracts.length > 0" class="contracts-grid">
      <div *ngFor="let contract of contracts"
           class="contract-card"
           [class.selected]="selectedContractId === contract.id"
           (click)="contract.id !== undefined && onContractSelected(contract.id)"
           [@slideIn]>
        <div class="contract-icon">
          <i class="fas fa-file-pdf" *ngIf="contract.type === 'pdf'"></i>
          <i class="fas fa-file-word" *ngIf="contract.type === 'word'"></i>
        </div>
        <div class="contract-info">
          <h4 class="contract-name">{{ contract.name }}</h4>
          <p *ngIf="contract.description" class="contract-description">{{ contract.description }}</p>
          <span class="contract-type" [class.pdf]="contract.type === 'pdf'" [class.word]="contract.type === 'word'">
            {{ contract.type === 'pdf' ? 'PDF' : 'Word' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
