:host {
  display: block;
  min-height: 100vh;
  background: var(--background);
  color: var(--text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.notifications-section {
  padding: 2rem;
  color: var(--text);
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  margin-bottom: 2.5rem;
  text-align: center;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
}

.section-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  color: var(--text);
}

.section-title .material-icons {
  font-size: 2.25rem;
  color: var(--primary);
  filter: drop-shadow(0 2px 8px rgba(78, 205, 196, 0.6));
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 400;
}

.badge-unread {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  border-radius: 50px;
  padding: 0.25rem 0.75rem;
  font-size: 0.9rem;
  margin-left: 0.75rem;
  box-shadow: 0 2px 5px rgba(78, 205, 196, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(78, 205, 196, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(78, 205, 196, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(78, 205, 196, 0);
  }
}

/* Loading and Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background-color: var(--surface);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  margin-bottom: 2rem;
}

.spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 1.5rem;
}

.spinner:before,
.spinner:after {
  content: '';
  position: absolute;
  border-radius: 50%;
}

.spinner:before {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 0%, transparent 50%, var(--primary) 50%, var(--primary) 100%);
  animation: spin 1.2s linear infinite;
}

.spinner:after {
  width: 85%;
  height: 85%;
  background-color: var(--surface);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.error-message, .success-message {
  display: flex;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-radius: 10px;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-message {
  background-color: rgba(239, 68, 68, 0.08);
  border-left: 5px solid var(--error);
  color: var(--error);
}

.success-message {
  background-color: rgba(34, 197, 94, 0.08);
  border-left: 5px solid var(--success);
  color: var(--success);
}

.error-message i, .success-message i {
  margin-right: 0.75rem;
  font-size: 1.5rem;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background-color: var(--surface);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.empty-state i {
  font-size: 5rem;
  color: var(--primary);
  opacity: 0.5;
  margin-bottom: 1.5rem;
  display: block;
}

.empty-state h2 {
  font-size: 1.75rem;
  margin-bottom: 1rem;
  color: var(--text);
  font-weight: 600;
}

.empty-state p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  max-width: 500px;
  margin: 0 auto;
}

/* Controls */
.controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1.25rem;
  background-color: var(--surface);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 1rem 1.25rem 1rem 3rem;
  border: 2px solid var(--border);
  border-radius: 50px;
  background-color: var(--surface);
  color: var(--text);
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.3);
  outline: none;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-box .material-icons {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 1.25rem;
  transition: color 0.3s ease;
}

.search-input:focus + .material-icons {
  color: var(--primary);
}

.filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.filter-select, .sort-select {
  padding: 0.85rem 1.25rem;
  border: 2px solid var(--border);
  border-radius: 50px;
  background-color: var(--surface);
  color: var(--text);
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  appearance: none;
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
  padding-right: 2.5rem;
}

/* Light mode dropdown arrow */
body:not(.dark-mode) .filter-select,
body:not(.dark-mode) .sort-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

/* Dark mode dropdown arrow */
body.dark-mode .filter-select,
body.dark-mode .sort-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

/* Additional light mode specific overrides */
body:not(.dark-mode) .message-item:hover .message-date {
  background-color: var(--primary);
  color: white;
}

.filter-select:focus, .sort-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.3);
  outline: none;
}

.btn-mark-all-read {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.85rem 1.5rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.95rem;
  box-shadow: 0 4px 10px rgba(78, 205, 196, 0.3), 0 0 20px rgba(78, 205, 196, 0.3);
}

.btn-mark-all-read:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
  box-shadow: 0 6px 15px rgba(78, 205, 196, 0.4), 0 0 30px rgba(78, 205, 196, 0.4);
  transform: translateY(-2px);
}

.btn-mark-all-read:active {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(78, 205, 196, 0.3);
}

/* Messages List */
.messages-list {
  background-color: var(--surface);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border);
  backdrop-filter: blur(10px);
}

.message-item {
  display: flex;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item:hover {
  background-color: var(--surface-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  z-index: 1;
}

.message-item.unread {
  background-color: rgba(78, 205, 196, 0.15);
}

.message-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: linear-gradient(to bottom, var(--primary), var(--primary-light));
  border-radius: 0 2px 2px 0;
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
}

.message-status {
  margin-right: 1.25rem;
  display: flex;
  align-items: center;
}

.status-icon {
  font-size: 1.75rem;
  transition: all 0.3s ease;
}

.status-icon.unread {
  color: var(--primary-light);
  filter: drop-shadow(0 0 8px rgba(78, 205, 196, 0.6));
}

.status-icon.read {
  color: var(--text-secondary);
}

.message-item:hover .status-icon {
  transform: scale(1.1);
}

.message-content {
  flex: 1;
  min-width: 0; /* Allows text truncation to work */
  padding-right: 1rem;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.message-subject {
  font-weight: 700;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
  font-size: 1.1rem;
  color: var(--text);
  transition: color 0.3s ease;
}

.message-item:hover .message-subject {
  color: var(--primary);
  text-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
}

.message-date {
  font-size: 0.85rem;
  color: var(--text-secondary);
  background-color: var(--surface-hover);
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  transition: all 0.3s ease;
}

/* Ensure better contrast for message date in light mode */
body:not(.dark-mode) .message-date {
  color: #4a5568; /* Darker gray for better contrast in light mode */
  background-color: #f1f5f9; /* Light gray background */
}

body.dark-mode .message-date {
  color: rgba(255, 255, 255, 0.6); /* Light gray for dark mode */
  background-color: rgba(255, 255, 255, 0.1); /* Semi-transparent white */
}

.message-item:hover .message-date {
  background-color: rgba(78, 205, 196, 0.3);
  color: #ffffff;
}

.message-preview {
  display: flex;
  flex-direction: column;
}

.sender-name {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

/* Ensure better contrast for sender name in light mode */
body:not(.dark-mode) .sender-name {
  color: #4a5568; /* Darker gray for better contrast in light mode */
}

body.dark-mode .sender-name {
  color: rgba(255, 255, 255, 0.7); /* Light gray for dark mode */
}

.message-item:hover .sender-name {
  color: var(--primary);
}

.message-excerpt {
  color: var(--text-secondary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  max-height: 3em;
  font-size: 0.95rem;
}

/* Ensure better contrast for message excerpt in light mode */
body:not(.dark-mode) .message-excerpt {
  color: #4a5568; /* Darker gray for better contrast in light mode */
}

body.dark-mode .message-excerpt {
  color: rgba(255, 255, 255, 0.6); /* Light gray for dark mode */
}

.message-actions {
  display: flex;
  align-items: center;
}

.btn-icon {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(239, 68, 68, 0.2);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
}

.btn-icon:hover::before {
  transform: scale(1);
}

.btn-icon i {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.btn-icon:hover i {
  color: #ff4d4d;
  transform: scale(1.1);
  filter: drop-shadow(0 0 5px rgba(255, 77, 77, 0.5));
}

.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
  background-color: var(--surface);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border);
}

.no-results i {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  color: var(--primary);
  opacity: 0.7;
  display: block;
  filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.4));
}

.btn-clear-filters {
  background-color: rgba(78, 205, 196, 0.2);
  color: var(--primary-light);
  border: 1px solid var(--primary);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 500;
  margin-top: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(78, 205, 196, 0.2);
}

.btn-clear-filters:hover {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.4);
  transform: translateY(-2px);
}



/* Message Detail View */
.message-detail {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-detail-header {
  display: flex;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-back, .btn-delete {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.95rem;
}

.btn-back {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.btn-back:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: var(--primary);
  color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15), 0 0 10px rgba(78, 205, 196, 0.2);
}

.btn-delete {
  background: linear-gradient(135deg, #ff4d4d, #ef4444);
  color: white;
  box-shadow: 0 4px 10px rgba(239, 68, 68, 0.3), 0 0 20px rgba(239, 68, 68, 0.2);
}

.btn-delete:hover {
  background: linear-gradient(135deg, #e03131, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(239, 68, 68, 0.4), 0 0 30px rgba(239, 68, 68, 0.3);
}

.message-detail-content {
  padding: 2rem;
}

.message-subject {
  font-size: 1.75rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
  line-height: 1.3;
  position: relative;
  padding-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.message-subject::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
}

.message-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 1.25rem;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.07);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sender-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sender-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.sender-email {
  color: var(--primary-light);
  font-weight: 500;
  font-size: 0.95rem;
  text-shadow: 0 0 5px rgba(78, 205, 196, 0.3);
}

.message-date {
  background-color: rgba(78, 205, 196, 0.2);
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
  align-self: flex-start;
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.2);
}

.message-body {
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.05rem;
  background-color: rgba(255, 255, 255, 0.07);
  padding: 1.5rem;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notifications-section {
    padding: 1.5rem 1rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .controls {
    flex-direction: column;
    align-items: stretch;
    padding: 1.25rem;
    gap: 1rem;
  }

  .filters {
    flex-direction: column;
    width: 100%;
  }

  .filter-select, .sort-select {
    width: 100%;
  }

  .message-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .message-subject {
    max-width: 100%;
    margin-bottom: 0.75rem;
    font-size: 1.5rem;
  }

  .message-meta {
    flex-direction: column;
    gap: 1rem;
  }

  .message-detail-header {
    padding: 1rem;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .btn-back, .btn-delete {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }

  .message-detail-content {
    padding: 1.5rem;
  }

  .btn-mark-all-read {
    width: 100%;
    justify-content: center;
  }

  .search-input {
    padding: 0.85rem 1rem 0.85rem 2.75rem;
  }

  .search-box .material-icons {
    left: 1rem;
  }
}
