/* Variables */
:host {
  --primary: #4ECDC4;
  --primary-light: #6CDED6;
  --primary-dark: #3DACA4;
  --border-radius: 16px;
  --border-radius-sm: 8px;
  --transition: all 0.3s ease;
  
  /* Theme-adaptive variables - default light mode */
  --settings-bg: var(--background, #f8fafc);
  --settings-card-bg: var(--surface, #ffffff);
  --settings-card-bg-light: #f1f5f9;
  --settings-input-bg: #f8fafc;
  --settings-border-color: #e2e8f0;
  --settings-text-primary: var(--text-primary, #1e293b);
  --settings-text-secondary: var(--text-secondary, #64748b);
  --settings-text-muted: #94a3b8;
  --settings-box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
  --settings-success: #28a745;
  --settings-success-bg: rgba(40, 167, 69, 0.1);
  --settings-warning: #ffc107;
  --settings-warning-bg: rgba(255, 193, 7, 0.1);
  --settings-danger: #dc3545;
  --settings-danger-bg: rgba(220, 53, 69, 0.1);
  --settings-item-bg: rgba(0, 0, 0, 0.03);
  --settings-item-hover-bg: rgba(0, 0, 0, 0.05);
  --settings-toggle-bg: #e2e8f0;
}

/* Dark Mode */
body.dark-mode :host {
  --settings-bg: #1a1a1a;
  --settings-card-bg: #252525;
  --settings-card-bg-light: #2d2d2d;
  --settings-input-bg: #333333;
  --settings-border-color: #444444;
  --settings-text-primary: #ffffff;
  --settings-text-secondary: #cccccc;
  --settings-text-muted: #999999;
  --settings-box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  --settings-item-bg: rgba(255, 255, 255, 0.03);
  --settings-item-hover-bg: rgba(255, 255, 255, 0.05);
  --settings-toggle-bg: #444444;
}

/* Container and Background Elements */
.security-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  color: var(--settings-text-primary);
  position: relative;
  overflow: hidden;
}

.bg-element {
  position: absolute;
  z-index: -1;
  opacity: 0.5;
  pointer-events: none;
}

.bg-circle-1 {
  width: 400px;
  height: 400px;
  border-radius: 50%;
  background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
  top: -200px;
  right: -100px;
  animation: float 15s ease-in-out infinite;
}

.bg-circle-2 {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(circle, var(--primary-dark) 0%, transparent 70%);
  bottom: -150px;
  left: -100px;
  animation: float 20s ease-in-out infinite reverse;
}

.bg-pattern {
  width: 100%;
  height: 100%;
  background-image: radial-gradient(var(--border-color) 1px, transparent 1px);
  background-size: 30px 30px;
  opacity: 0.1;
  top: 0;
  left: 0;
}

@keyframes float {
  0% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
  100% { transform: translateY(0) rotate(0deg); }
}

.security-card {
  background-color: var(--settings-card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--settings-box-shadow);
  padding: 2.5rem;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid var(--settings-border-color);
}

/* Header */
.security-header {
  display: flex;
  align-items: center;
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--settings-border-color);
}

.header-icon-container {
  position: relative;
  margin-right: 1.5rem;
}

.header-icon-bg {
  position: absolute;
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.2; }
  100% { transform: scale(1); opacity: 0.3; }
}

.header-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.header-icon i {
  font-size: 30px;
  color: var(--settings-text-primary);
}

.header-text h1 {
  font-size: 2.2rem;
  color: var(--settings-text-primary);
  margin-bottom: 0.5rem;
  font-weight: 600;
  letter-spacing: -0.5px;
}

.header-text p {
  color: var(--settings-text-secondary);
  font-size: 1.1rem;
}

/* Alerts */
.alert {
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius-sm);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  animation: slideIn 0.4s ease;
  position: relative;
  overflow: hidden;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.alert-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.alert i {
  font-size: 24px;
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 1.1rem;
}

.alert-message {
  font-size: 0.95rem;
  opacity: 0.9;
}

.alert-close {
  background: none;
  border: none;
  color: inherit;
  opacity: 0.7;
  cursor: pointer;
  padding: 0.5rem;
  transition: var(--transition);
  border-radius: 50%;
}

.alert-close:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

.alert-success {
  background-color: var(--success-bg);
  color: var(--success);
}

.alert-success::before {
  background-color: var(--success);
}

.alert-success .alert-icon-container {
  background-color: rgba(40, 167, 69, 0.2);
}

.alert-error {
  background-color: var(--danger-bg);
  color: var(--danger);
}

.alert-error::before {
  background-color: var(--danger);
}

.alert-error .alert-icon-container {
  background-color: rgba(220, 53, 69, 0.2);
}

/* Status Card */
.status-card {
  display: flex;
  align-items: center;
  background-color: var(--settings-card-bg-light);
  border-radius: var(--border-radius-sm);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--settings-border-color);
}

.status-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.25rem;
  flex-shrink: 0;
}

.status-icon i {
  font-size: 24px;
  color: var(--settings-text-primary);
}

.status-content {
  flex: 1;
}

.status-content h3 {
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  color: var(--settings-text-primary);
}

.status-meter {
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin-bottom: 0.75rem;
  overflow: hidden;
}

.status-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 3px;
  transition: width 1s ease-in-out;
}

.status-content p {
  font-size: 0.9rem;
  color: var(--settings-text-secondary);
  margin: 0;
}

.status-action {
  margin-left: 1.5rem;
  flex-shrink: 0;
}

.status-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: var(--primary);
  color: var(--text-white);
  border-radius: 20px;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Forms */
.security-form {
  width: 100%;
}

.form-section {
  margin-bottom: 3rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--settings-border-color);
}

.form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.section-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 20px;
  color: var(--settings-text-primary);
}

.section-header h2 {
  font-size: 1.5rem;
  color: var(--settings-text-primary);
  margin: 0;
  font-weight: 600;
}

.form-group {
  margin-bottom: 2.5rem;
  position: relative;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  color: var(--settings-text-primary);
  font-weight: 500;
  font-size: 1.1rem;
  position: relative;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  transition: var(--transition);
}

.input-container:focus-within {
  transform: translateY(-2px);
}

.input-icon {
  position: absolute;
  left: 1.25rem;
  color: var(--settings-text-muted);
  font-size: 20px;
  transition: var(--transition);
}

.input-container:focus-within .input-icon {
  color: var(--primary);
}

.form-control {
  width: 100%;
  padding: 1.25rem 3.5rem 1.25rem 3.5rem;
  font-size: 1rem;
  border: 2px solid var(--settings-border-color);
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
  background-color: var(--settings-input-bg);
  color: var(--settings-text-primary);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.form-control::placeholder {
  color: var(--settings-text-muted);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.15), 0 8px 20px rgba(0, 0, 0, 0.1);
}

.form-control.error {
  border-color: var(--danger);
}

.toggle-button {
  position: absolute;
  right: 1.25rem;
  background: none;
  border: none;
  color: var(--settings-text-muted);
  cursor: pointer;
  padding: 0.5rem;
  transition: var(--transition);
  border-radius: 50%;
  z-index: 2;
}

.toggle-button:hover {
  color: var(--settings-text-primary);
  background-color: var(--settings-item-hover-bg);
}

.toggle-button i {
  font-size: 22px;
}

.error-message {
  color: var(--settings-danger);
  font-size: 0.9rem;
  margin-top: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: fadeIn 0.3s ease;
}

.error-icon {
  font-size: 18px;
}

/* Password Strength */
.password-strength {
  margin-top: 1rem;
}

.strength-meter {
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin-bottom: 0.75rem;
  overflow: hidden;
}

.strength-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-bar.weak {
  width: 33.33%;
  background-color: var(--danger);
}

.strength-bar.medium {
  width: 66.66%;
  background-color: var(--warning);
}

.strength-bar.strong {
  width: 100%;
  background-color: var(--success);
}

.strength-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--settings-text-secondary);
}

.strength-text i {
  font-size: 18px;
}

.strength-text i.weak {
  color: var(--danger);
}

.strength-text i.medium {
  color: var(--warning);
}

.strength-text i.strong {
  color: var(--success);
}

/* Password Tips */
.password-tips {
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--settings-item-bg);
  border-radius: var(--border-radius);
}

.tip-title {
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--settings-text-secondary);
}

.tip-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tip-list li {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: var(--settings-text-muted);
  font-size: 0.9rem;
  transition: var(--transition);
}

.tip-list li::before {
  content: '○';
  margin-right: 0.5rem;
  font-size: 1rem;
  transition: var(--transition);
}

.tip-list li.active {
  color: var(--primary-light);
}

.tip-list li.active::before {
  content: '●';
  color: var(--primary);
}

/* Security Recommendations */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  padding: 1.5rem;
  background-color: var(--settings-item-bg);
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
  border: 1px solid var(--settings-border-color);
}

.recommendation-item:hover {
  background-color: var(--settings-item-hover-bg);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.recommendation-status {
  margin-right: 1rem;
  padding-top: 0.25rem;
}

.status-icon {
  font-size: 24px;
  color: var(--settings-text-muted);
}

.recommendation-item.completed .status-icon {
  color: var(--settings-success);
}

.recommendation-item.in-progress .status-icon {
  color: var(--settings-warning);
}

.recommendation-content {
  flex: 1;
}

.recommendation-content h3 {
  font-size: 1.1rem;
  color: var(--settings-text-primary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.recommendation-content p {
  font-size: 0.9rem;
  color: var(--settings-text-secondary);
  margin-bottom: 1rem;
}

.recommendation-progress {
  height: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.recommendation-progress .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 2px;
  transition: width 0.5s ease;
}

.recommendation-item.completed .progress-bar {
  background: linear-gradient(90deg, var(--success), #4caf50);
}

.recommendation-item.in-progress .progress-bar {
  background: linear-gradient(90deg, var(--warning), #ffb74d);
}

.recommendation-action {
  margin-top: 0.5rem;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  border-radius: 34px;
  transition: var(--transition);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: var(--text-white);
  border-radius: 50%;
  transition: var(--transition);
}

.toggle-switch input:checked + .toggle-slider {
  background-color: var(--primary);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Text Button */
.btn-text {
  background: none;
  border: none;
  color: var(--primary);
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.btn-text:hover {
  background-color: rgba(78, 205, 196, 0.1);
}

.btn-text i {
  font-size: 18px;
}

/* Activity Log */
.activity-log {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  padding: 1.5rem;
  margin-bottom: 1rem;
  background-color: var(--settings-item-bg);
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
  border: 1px solid var(--settings-border-color);
}

.activity-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--settings-box-shadow);
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-right: 1.5rem;
  background-color: var(--settings-item-hover-bg);
  border-radius: 50%;
  font-size: 20px;
  color: var(--primary);
}

.activity-content {
  flex: 1;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.activity-content h3 {
  font-size: 1.1rem;
  color: var(--settings-text-primary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.activity-time {
  font-size: 0.85rem;
  color: var(--settings-text-muted);
}

.activity-content p {
  font-size: 0.95rem;
  color: var(--settings-text-secondary);
  margin: 0.75rem 0;
  line-height: 1.6;
}

.activity-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--settings-text-muted);
}

.activity-location i {
  font-size: 16px;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--settings-border-color);
}

.btn-primary {
  position: relative;
  padding: 1.25rem 2.5rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border: none;
  border-radius: var(--border-radius-sm);
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  overflow: hidden;
  min-width: 250px;
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.btn-icon {
  font-size: 22px;
}

.btn-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  transition: 0.5s;
  opacity: 0;
}

.btn-primary:not(:disabled):hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 30px rgba(78, 205, 196, 0.5);
}

.btn-primary:not(:disabled):hover .btn-shine {
  left: 100%;
  opacity: 1;
}

.btn-primary:not(:disabled):active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .security-container {
    max-width: 900px;
  }

  .bg-circle-1 {
    width: 300px;
    height: 300px;
  }

  .bg-circle-2 {
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 992px) {
  .security-card {
    padding: 2rem;
  }

  .status-card {
    flex-wrap: wrap;
  }

  .status-action {
    margin-left: 0;
    margin-top: 1rem;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .activity-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .activity-time {
    margin-top: 0.25rem;
  }
}

@media (max-width: 768px) {
  .security-container {
    padding: 1.5rem;
  }

  .security-card {
    padding: 1.5rem;
  }

  .security-header {
    flex-direction: column;
    text-align: center;
  }

  .header-icon-container {
    margin-right: 0;
    margin-bottom: 1.5rem;
  }

  .recommendation-item {
    flex-direction: column;
  }

  .recommendation-status {
    margin-right: 0;
    margin-bottom: 1rem;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .activity-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .activity-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .activity-location {
    justify-content: center;
  }

  .form-actions {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .security-container {
    padding: 1rem;
  }

  .security-card {
    padding: 1.25rem;
  }

  .status-card {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .status-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .status-content {
    width: 100%;
    margin-bottom: 1rem;
  }

  .section-header {
    flex-direction: column;
    text-align: center;
  }

  .section-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-primary {
    width: 100%;
    min-width: unset;
  }

  .recommendation-action {
    display: flex;
    justify-content: center;
  }
}
