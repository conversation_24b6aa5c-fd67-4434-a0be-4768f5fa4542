<section class="notifications-section">
  <div class="container">
    <header class="section-header">
      <h1 class="section-title">
        <i class="material-icons">notifications</i>
        Contact Messages
        <span *ngIf="getUnreadCount() > 0" class="badge badge-unread">{{ getUnreadCount() }}</span>
      </h1>
      <p class="section-subtitle">Manage contact form submissions</p>
    </header>

    <!-- Loading Indicator -->
    <div *ngIf="isLoading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading messages...</p>
    </div>

    <!-- Error Message -->
    <div *ngIf="errorMessage" class="error-message">
      <i class="material-icons">error</i>
      <p>{{ errorMessage }}</p>
    </div>

    <!-- Success Message -->
    <div *ngIf="successMessage" class="success-message">
      <i class="material-icons">check_circle</i>
      <p>{{ successMessage }}</p>
    </div>

    <!-- No Messages -->
    <div *ngIf="!isLoading && !errorMessage && messages.length === 0" class="empty-state">
      <i class="material-icons">inbox</i>
      <h2>No Messages</h2>
      <p>There are no contact messages to display.</p>
    </div>

    <!-- Messages List View -->
    <div *ngIf="!isLoading && !errorMessage && messages.length > 0" class="messages-container">
      <!-- Controls -->
      <div class="controls">
        <div class="search-box">
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="onSearch()"
            placeholder="Search messages..."
            class="search-input"
          >
          <i class="material-icons">search</i>
        </div>

        <div class="filters">
          <select [(ngModel)]="filterOption" (change)="onFilterChange()" class="filter-select">
            <option value="all">All Messages</option>
            <option value="unread">Unread</option>
            <option value="read">Read</option>
          </select>

          <select [(ngModel)]="sortOption" (change)="onSortChange()" class="sort-select">
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
          </select>

          <button
            *ngIf="getUnreadCount() > 0"
            (click)="markAllAsRead()"
            class="btn-mark-all-read"
          >
            <i class="material-icons">mark_email_read</i>
            Mark All as Read
          </button>
        </div>
      </div>

      <!-- Message Detail View -->
      <div *ngIf="selectedMessage" class="message-detail">
        <div class="message-detail-header">
          <button (click)="closeMessageView()" class="btn-back">
            <i class="material-icons">arrow_back</i>
            Back to List
          </button>
          <button (click)="deleteMessage(selectedMessage)" class="btn-delete">
            <i class="material-icons">delete</i>
            Delete
          </button>
        </div>

        <div class="message-detail-content">
          <h2 class="message-subject">{{ selectedMessage.subject }}</h2>

          <div class="message-meta">
            <div class="sender-info">
              <span class="sender-name">From: {{ selectedMessage.name }}</span>
              <span class="sender-email">{{ selectedMessage.email }}</span>
            </div>
            <div class="message-date">
              {{ getFormattedDate(selectedMessage.created_at) }}
            </div>
          </div>

          <div class="message-body">
            <p>{{ selectedMessage.message }}</p>
          </div>
        </div>
      </div>

      <!-- Messages List -->
      <div *ngIf="!selectedMessage" class="messages-list">
        <div *ngIf="filteredMessages.length === 0" class="no-results">
          <i class="material-icons">search_off</i>
          <p>No messages match your search criteria</p>
          <button (click)="clearFilters()" class="btn-clear-filters">Clear filters</button>
        </div>

        <div
          *ngFor="let message of filteredMessages"
          class="message-item"
          [class.unread]="!message.is_read"
          (click)="viewMessage(message)"
        >
          <div class="message-status">
            <i *ngIf="!message.is_read" class="material-icons status-icon unread">mark_email_unread</i>
            <i *ngIf="message.is_read" class="material-icons status-icon read">mark_email_read</i>
          </div>

          <div class="message-content">
            <div class="message-header">
              <h3 class="message-subject">{{ message.subject }}</h3>
              <span class="message-date">{{ getFormattedDate(message.created_at) }}</span>
            </div>

            <div class="message-preview">
              <span class="sender-name">From: {{ message.name }}</span>
              <p class="message-excerpt">{{ message.message.substring(0, 120) }}{{ message.message.length > 120 ? '...' : '' }}</p>
            </div>
          </div>

          <div class="message-actions">
            <button
              (click)="deleteMessage(message, $event)"
              class="btn-icon"
              title="Delete message"
            >
              <i class="material-icons">delete</i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
