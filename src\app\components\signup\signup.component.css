/* Signup Styles */

.signup-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  border-radius: 20px;
}

.signup-left {
  flex: 1;
  background: var(--primary); /* Use theme variable */
  padding: 5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: white;
  position: relative;
  overflow: hidden;
}

/* Professional contract-themed overlay */
.signup-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M0 0h20L0 20z'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
}

/* Document-like pattern overlay */
.signup-left::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20.83l2.83-2.83 1.41 1.41L1.41 22H0v-1.17zM0 3.07l2.83-2.83 1.41 1.41L1.41 4.24H0V3.07zm28.24 35.52l1.41-1.41 2.83 2.83V40h-1.41l-2.83-2.83zm14.18-14.17l1.41-1.41 2.83 2.83V26h-1.41l-2.83-2.83zM3.07 0l1.41 1.41-2.83 2.83H.24V3.07L3.07 0zm39.17 0l-1.41 1.41 2.83 2.83h1.17V3.07L42.24 0zm-16.93 0l-1.41 1.41 2.83 2.83h1.17V3.07L25.31 0zM20.83 0l-1.41 1.41 2.83 2.83h1.17V3.07L20.83 0zm-16.93 0l-1.41 1.41 2.83 2.83h1.17V3.07L3.9 0zM40 14.18V16h-1.17l-2.83-2.83 1.41-1.41L40 14.18zm0-11.11V4.24h-1.17l-2.83-2.83 1.41-1.41L40 3.07zm0 16.93v1.17h-1.17l-2.83-2.83 1.41-1.41L40 20zm0 16.93v1.17h-1.17l-2.83-2.83 1.41-1.41L40 36.93zM26 40h1.17l2.83-2.83-1.41-1.41L26 38.59V40zm-11.17 0h1.17l2.83-2.83-1.41-1.41-2.59 2.59V40zM14.18 40h1.17l2.83-2.83-1.41-1.41-2.59 2.59V40zM3.07 40h1.17l2.83-2.83-1.41-1.41L3.07 38.34V40z'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.1;
}

.logo-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.logo {
  height: 3rem;
}

.back-link {
  color: white;
  text-decoration: none;
  font-size: 0.875rem;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.back-link:hover {
  opacity: 1;
}

.hero-content {
  margin-top: auto;
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
}

.hero-content h2 {
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.5px;
}

.hero-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.5;
  max-width: 400px;
  margin-bottom: 2rem;
}

.contract-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 5rem;
  opacity: 0.2;
  transform: rotate(10deg);
}

.hero-dots {
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
}

.dot.active {
  background: white;
  width: 24px;
  border-radius: 4px;
}

.signup-right {
  flex: 1.5; /* Increased from 1 to give more space to the form */
  background: var(--background); /* Use theme variable */
  padding: 4rem; /* Increased from 3rem */
  display: flex;
  align-items: center;
  justify-content: center;
}

.signup-form-container {
  width: 100%;
  max-width: 800px; /* Increased from 440px */
  color: var(--text);
}

.signup-form-container h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.login-link {
  color: var(--textSecondary);
  margin-bottom: 2rem;
}

.login-link a {
  color: var(--primary); /* Use theme variable */
  text-decoration: none;
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  padding: 0.75rem 1rem;
  margin-bottom: 1.25rem;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  animation: fadeIn 0.3s ease;
  position: relative;
  padding-left: 2.5rem;
  background: var(--success-light);
  color: var(--success);
  border-left: 4px solid var(--success);
}

.message::before {
  font-family: 'Material Icons';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  content: 'check_circle';
  color: var(--success);
}

.message.error {
  background: var(--error-light);
  color: var(--error);
  border-left: 4px solid var(--error);
}

.message.error::before {
  content: 'error';
  color: var(--error);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.name-fields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-field {
  position: relative;
}

.form-field input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: var(--elevation2);
  color: var(--text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-field input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 78, 205, 196), 0.2), 0 2px 5px var(--shadow) inset;
  transform: translateY(-1px);
}

.password-field {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
}

.terms-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.terms-checkbox input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
}

.terms-link {
  color: var(--primary);
  text-decoration: none;
}

.signup-button {
  width: 100%;
  padding: 0.875rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(var(--primary-rgb, 78, 205, 196), 0.3);
}

.signup-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.signup-button:hover {
  background: var(--primary-dark, #3DACA4);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(var(--primary-rgb, 78, 205, 196), 0.4);
}

.signup-button:hover::after {
  transform: translateX(100%);
}

.signup-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb, 78, 205, 196), 0.3);
}

.signup-button:disabled {
  background: rgba(var(--primary-rgb, 78, 205, 196), 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.divider {
  text-align: center;
  position: relative;
  margin: 1.5rem 0;
  color: var(--textSecondary);
}

.divider::before,
.divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background: var(--border);
}

.divider::before {
  left: 0;
}

.divider::after {
  right: 0;
}

.social-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.875rem;
  background: var(--elevation2);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  color: var(--text);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-button:hover {
  background: var(--surfaceHover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.social-button:active {
  transform: translateY(0);
}

.social-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.social-button img {
  height: 1.5rem;
  width: auto;
}

.social-button.google {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
}

.social-button.google:hover {
  background: var(--primary-dark, #3DACA4);
}

.social-button.facebook {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
}

.social-button.facebook:hover {
  background: var(--primary-dark, #3DACA4);
}
.social-login-buttons button, .social-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.875rem;
  background-color: var(--elevation2);
  border: 1px solid var(--border);
  border-radius: 8px;
  color: var(--text);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-weight: 500;
  position: relative;
}

.social-login-buttons button:hover:not(:disabled),
.social-login-buttons a.social-btn:hover,
.social-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.social-login-buttons button:active,
.social-login-buttons a.social-btn:active,
.social-button:active {
  transform: translateY(1px);
}

.social-login-buttons button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.social-login-buttons img, .social-buttons img {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  border-radius: 3%;
}

.social-button.google, .google-btn {
  background-color: rgba(78, 205, 196, 0.1); /* Teal/Turquoise with opacity */
  border-color: rgba(78, 205, 196, 0.3); /* Teal/Turquoise with opacity */
  color: #4ECDC4; /* Teal/Turquoise color */
}

.social-button.google:hover, .google-btn:hover {
  background-color: rgba(78, 205, 196, 0.2); /* Teal/Turquoise with opacity */
}

.social-button.facebook, .facebook-btn {
  background-color: rgba(78, 205, 196, 0.1); /* Teal/Turquoise with opacity */
  border-color: rgba(78, 205, 196, 0.3); /* Teal/Turquoise with opacity */
  color: #4ECDC4; /* Teal/Turquoise color */
}

.social-button.facebook:hover, .facebook-btn:hover {
  background-color: rgba(78, 205, 196, 0.2); /* Teal/Turquoise with opacity */
}
