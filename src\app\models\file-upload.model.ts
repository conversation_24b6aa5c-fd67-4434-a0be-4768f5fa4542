export interface FileUploadResponse {
  success: boolean;
  message: string;
  file?: {
    name: string;
    path: string;
    extractedData?: ExtractedDataItem[];
  };
}

export interface FileListResponse {
  success: boolean;
  files: FileItem[];
}

export interface FileItem {
  filename: string;
  size: number;
  last_modified: number;
  url: string;
}

export interface ExtractedDataItem {
  key: string;
  value: string;
  original?: string;
}

export interface ExtractedDataResponse {
  success: boolean;
  data: ExtractedDataItem[];
}

export interface DocumentGenerationResponse {
  success: boolean;
  message?: string;
  file?: {
    download_url: string;
    unsigned_contract_id?: number;
  };
}
