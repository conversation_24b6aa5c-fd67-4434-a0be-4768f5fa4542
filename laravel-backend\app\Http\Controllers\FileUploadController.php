<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Services\DocumentProcessingService;

class FileUploadController extends Controller
{
    protected $documentProcessor;

    public function __construct(DocumentProcessingService $documentProcessor)
    {
        $this->documentProcessor = $documentProcessor;
    }

    /**
     * Upload a file
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(Request $request)
    {
        try {
            Log::info('Upload request received', [
                'has_file' => $request->hasFile('document'),
                'content_type' => $request->header('Content-Type'),
                'content_length' => $request->header('Content-Length')
            ]);

            $request->validate([
                'document' => 'required|file|max:10240', // 10MB max size
            ]);

            if (!$request->hasFile('document')) {
                Log::warning('No file in request');
                return response()->json([
                    'success' => false,
                    'message' => 'No file uploaded'
                ], 400);
            }

            $file = $request->file('document');

            // Log file information
            Log::info('File received', [
                'original_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize(),
                'extension' => $file->getClientOriginalExtension()
            ]);

            // Vérifier si le fichier est valide
            if (!$file->isValid()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file: ' . $file->getErrorMessage()
                ], 400);
            }

            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();

            // Vérifier le type de fichier
            $allowedMimeTypes = [
                'application/pdf', // PDF
                'application/msword', // DOC
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
                'application/vnd.ms-word', // Alternative DOC
                'application/vnd.ms-word.document.macroEnabled.12', // DOCM
                'application/vnd.openxmlformats-officedocument.wordprocessingml.template', // DOTX
            ];

            $mimeType = $file->getMimeType();
            $extension = $file->getClientOriginalExtension();

            Log::info('File upload attempt', [
                'filename' => $originalName,
                'mime_type' => $mimeType,
                'extension' => $extension,
                'size' => $file->getSize()
            ]);

            // Check if the file extension is valid even if the mime type is not recognized correctly
            $validExtension = in_array(strtolower($extension), ['pdf', 'doc', 'docx']);

            if (!in_array($mimeType, $allowedMimeTypes) && !$validExtension) {
                Log::warning('File upload rejected - invalid type', [
                    'mime_type' => $mimeType,
                    'extension' => $extension
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Format de fichier non supporté. Veuillez utiliser PDF ou Word (.pdf, .doc, .docx).',
                    'details' => [
                        'detected_mime' => $mimeType,
                        'extension' => $extension
                    ]
                ], 400);
            }

            // Get the authenticated user
            $user = Auth::user();

            // Check if user is authenticated
            if (!$user) {
                Log::warning('Unauthenticated user trying to upload file');
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to upload files'
                ], 401);
            }

            $userId = $user->id;

            // Add user ID to the filename for user-specific files
            $safeFilename = Str::slug(pathinfo($originalName, PATHINFO_FILENAME)) . '_' . $userId . '_' . time() . '.' . $extension;

            // Ensure the uploads directory exists
            $uploadsPath = storage_path('app/public/uploads');
            if (!file_exists($uploadsPath)) {
                Log::info('Creating uploads directory at: ' . $uploadsPath);
                mkdir($uploadsPath, 0755, true);
            }

            // Also create a copy with the original name for document generation
            $originalFilename = 'contract_1.docx';
            if ($extension === 'docx' || $extension === 'doc') {
                // Store a copy with a simple name for document generation
                $simplePath = $file->storeAs('uploads', $originalFilename, 'public');
                Log::info('Stored file with simple name for document generation', [
                    'original_name' => $originalFilename,
                    'path' => $simplePath
                ]);
            }

            // Store the file in the public uploads directory
            $path = $file->storeAs('uploads', $safeFilename, 'public');

            // Log the file upload with user information
            Log::info('File uploaded by user', [
                'user_id' => $userId,
                'filename' => $safeFilename,
                'path' => $path,
                'storage_exists' => Storage::disk('public')->exists($path),
                'file_size' => Storage::disk('public')->exists($path) ? Storage::disk('public')->size($path) : 0
            ]);

            if (!$path) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to store file'
                ], 500);
            }

            // Double-check that the file was actually stored
            if (!Storage::disk('public')->exists($path)) {
                Log::error('File was not stored properly at: ' . $path);
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to store file in storage'
                ], 500);
            }

            try {
                // Extract data from the file
                $extractedData = $this->documentProcessor->processFile($file);

                // Also create a copy with the original name for document generation
                $originalFilename = 'contract_1.docx';
                if ($extension === 'docx' || $extension === 'doc') {
                    // Store a copy with a simple name for document generation
                    $simplePath = $file->storeAs('uploads', $originalFilename, 'public');
                    Log::info('Stored file with simple name for document generation', [
                        'original_name' => $originalFilename,
                        'path' => $simplePath,
                        'extracted_fields' => count($extractedData)
                    ]);

                    // Extract data from the simple file as well to ensure we have the fields
                    if (empty($extractedData)) {
                        $simpleFile = new \Illuminate\Http\UploadedFile(
                            Storage::disk('public')->path($simplePath),
                            $originalFilename,
                            $file->getMimeType(),
                            null,
                            true
                        );
                        $extractedData = $this->documentProcessor->processFile($simpleFile);
                        Log::info('Extracted data from simple file', [
                            'fields_count' => count($extractedData)
                        ]);
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'file' => [
                        'name' => $safeFilename,
                        'path' => $path,
                        'extractedData' => $extractedData,
                        'user_id' => $userId
                    ]
                ]);
            } catch (\Exception $e) {
                Log::error('Error during data extraction: ' . $e->getMessage());

                return response()->json([
                    'success' => true,
                    'message' => 'File uploaded but data extraction failed: ' . $e->getMessage(),
                    'file' => [
                        'name' => $safeFilename,
                        'path' => $path,
                        'extractedData' => [],
                        'user_id' => $userId
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error during file upload: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * List all uploaded files for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function listFiles()
    {
        // Get the authenticated user
        $user = Auth::user();

        // Check if user is authenticated
        if (!$user) {
            Log::warning('Unauthenticated user trying to access files');
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to view files'
            ], 401);
        }

        // Log the user information
        Log::info('User requesting files', [
            'user_id' => $user->id,
            'is_authenticated' => true
        ]);

        // Get all files in the uploads directory
        $files = Storage::disk('public')->files('uploads');

        // Filter files to show only user-specific files
        $userId = $user->id;
        $userIdStr = '_' . $userId . '_'; // Format used in filenames

        $fileList = [];
        foreach ($files as $file) {
            $fileInfo = pathinfo($file);
            $filename = $fileInfo['basename'];

            // Only include files that belong to this user
            if (strpos($filename, $userIdStr) !== false) {
                $fileList[] = [
                    'filename' => $filename,
                    'size' => Storage::disk('public')->size($file),
                    'last_modified' => Storage::disk('public')->lastModified($file),
                    'url' => asset('storage/' . $file),
                    'user_id' => $userId
                ];
            }
        }

        return response()->json([
            'success' => true,
            'files' => $fileList
        ]);
    }

    /**
     * Download a file
     *
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function download($filename)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Check if user is authenticated
        if (!$user) {
            Log::warning('Unauthenticated user trying to download file', ['filename' => $filename]);
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to download files'
            ], 401);
        }

        $path = 'uploads/' . $filename;

        if (!Storage::disk('public')->exists($path)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        // Check if the file belongs to the authenticated user
        $userId = $user->id;
        $userIdStr = '_' . $userId . '_'; // Format used in filenames

        if (strpos($filename, $userIdStr) === false) {
            Log::warning('User trying to download unauthorized file', [
                'user_id' => $userId,
                'filename' => $filename
            ]);
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to download this file'
            ], 403);
        }

        $file = Storage::disk('public')->path($path);
        $type = File::mimeType($file);

        $headers = [
            'Content-Type' => $type,
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        Log::info('User downloading file', [
            'user_id' => $userId,
            'filename' => $filename
        ]);

        return Response::download($file, $filename, $headers);
    }

    /**
     * Delete all files belonging to the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteAll()
    {
        // Get the authenticated user
        $user = Auth::user();

        // Check if user is authenticated
        if (!$user) {
            Log::warning('Unauthenticated user trying to delete all files');
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to delete files'
            ], 401);
        }

        $userId = $user->id;
        $userIdStr = '_' . $userId . '_'; // Format used in filenames
        $files = Storage::disk('public')->files('uploads');
        $deletedCount = 0;

        foreach ($files as $file) {
            $filename = basename($file);

            // Only delete files that belong to this user
            if (strpos($filename, $userIdStr) !== false) {
                Storage::disk('public')->delete($file);
                $deletedCount++;
            }
        }

        Log::info('User deleted all their files', [
            'user_id' => $userId,
            'deleted_count' => $deletedCount
        ]);

        return response()->json([
            'success' => true,
            'message' => 'All your files deleted successfully',
            'count' => $deletedCount
        ]);
    }

    /**
     * Delete a specific file
     *
     * @param string $filename
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteFile($filename)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Check if user is authenticated
        if (!$user) {
            Log::warning('Unauthenticated user trying to delete file', ['filename' => $filename]);
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to delete files'
            ], 401);
        }

        $path = 'uploads/' . $filename;

        if (!Storage::disk('public')->exists($path)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        // Check if the file belongs to the authenticated user
        $userId = $user->id;
        $userIdStr = '_' . $userId . '_'; // Format used in filenames

        if (strpos($filename, $userIdStr) === false) {
            Log::warning('User trying to delete unauthorized file', [
                'user_id' => $userId,
                'filename' => $filename
            ]);
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to delete this file'
            ], 403);
        }

        Storage::disk('public')->delete($path);

        Log::info('User deleted file', [
            'user_id' => $userId,
            'filename' => $filename
        ]);

        return response()->json([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }

    /**
     * Get extracted data from a file
     *
     * @param string $filename
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExtractedData($filename)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Check if user is authenticated
        if (!$user) {
            Log::warning('Unauthenticated user trying to extract data from file', ['filename' => $filename]);
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to extract data from files'
            ], 401);
        }

        // First try to find the file in uploads directory
        $filePath = Storage::disk('public')->path('uploads/' . $filename);
        $isUserFile = true;

        // If not found in uploads, try contracts directory
        if (!File::exists($filePath)) {
            $filePath = Storage::disk('public')->path('contracts/' . $filename);
            $isUserFile = false;
        }

        if (!File::exists($filePath)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        $userId = $user->id;

        // Only check user authorization for uploaded files, not contract files
        if ($isUserFile) {
            $userIdStr = '_' . $userId . '_'; // Format used in filenames

            if (strpos($filename, $userIdStr) === false) {
                Log::warning('User trying to extract data from unauthorized file', [
                    'user_id' => $userId,
                    'filename' => $filename
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to access this file'
                ], 403);
            }
        }

        Log::info('User extracting data from file', [
            'user_id' => $userId,
            'filename' => $filename,
            'file_path' => $filePath,
            'is_user_file' => $isUserFile
        ]);

        try {
            $file = new \Illuminate\Http\UploadedFile($filePath, $filename);
            $extractedData = $this->documentProcessor->processFile($file);

            Log::info('User extracted data from file', [
                'user_id' => $userId,
                'filename' => $filename,
                'extracted_fields_count' => count($extractedData)
            ]);

            return response()->json([
                'success' => true,
                'data' => $extractedData
            ]);
        } catch (\Exception $e) {
            Log::error('Data extraction failed', [
                'user_id' => $userId,
                'filename' => $filename,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Data extraction failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get extracted data from a contract file by contract ID
     *
     * @param int $contractId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getContractExtractedData($contractId)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Check if user is authenticated
        if (!$user) {
            Log::warning('Unauthenticated user trying to extract data from contract', ['contract_id' => $contractId]);
            return response()->json([
                'success' => false,
                'message' => 'Authentication required to extract data from contracts'
            ], 401);
        }

        try {
            // Find the contract
            $contract = \App\Models\Contract::findOrFail($contractId);

            if (!$contract->file_path) {
                return response()->json([
                    'success' => false,
                    'message' => 'Contract has no associated file'
                ], 404);
            }

            $filePath = Storage::disk('public')->path($contract->file_path);

            if (!File::exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Contract file not found'
                ], 404);
            }

            $userId = $user->id;

            Log::info('User extracting data from contract', [
                'user_id' => $userId,
                'contract_id' => $contractId,
                'file_path' => $contract->file_path
            ]);

            $file = new \Illuminate\Http\UploadedFile($filePath, $contract->filename);
            $extractedData = $this->documentProcessor->processFile($file);

            Log::info('User extracted data from contract', [
                'user_id' => $userId,
                'contract_id' => $contractId,
                'extracted_fields_count' => count($extractedData)
            ]);

            return response()->json([
                'success' => true,
                'data' => $extractedData
            ]);
        } catch (\Exception $e) {
            Log::error('Contract data extraction failed', [
                'user_id' => $user->id,
                'contract_id' => $contractId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Contract data extraction failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
