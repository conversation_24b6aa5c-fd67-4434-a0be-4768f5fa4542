<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Auth;
use Illuminate\Auth\AuthManager;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Bind auth.driver to the Auth facade
        $this->app->bind('auth.driver', function () {
            return Auth::guard();
        });

        // Bind auth to the AuthManager
        $this->app->bind('auth', function ($app) {
            return new AuthManager($app);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Schema ::defaultStringLength(191);
    }
}
