
<div class="upload-page-container">
  <div class="upload-content">
      <header class="section-header">
        <h2 id="upload-heading" class="animate__fadeInDown">
          <span class="highlight">Documents</span>
        </h2>
        <p class="section-subtitle">Upload, manage and share your documents</p>
      </header>

      <div class="upload-container">
        <!-- Upload Card -->
        <div class="modern-card upload-card"
             aria-labelledby="upload-card-heading"
             [@slideIn]>
        <header>
          <div class="card-header-with-back">
            <h3 id="upload-card-heading" class="card-title">
              <i class="fas fa-cloud-upload-alt" aria-hidden="true"></i>
              {{ selectedContractId ? 'Selected Contract' : 'Upload a Document' }}
            </h3>
          </div>

        </header>

        <div class="upload-form">
          <div class="upload-area"
               [class.drag-over]="isDragOver"
               [class.has-file]="selectedFile"
               (click)="!selectedFile && fileInput.click()"
               (dragover)="$event.preventDefault(); isDragOver = true"
               (dragleave)="$event.preventDefault(); isDragOver = false"
               (drop)="$event.preventDefault(); isDragOver = false; onFileDropped($event)">
            <input type="file" (change)="onFileSelected($event)" accept=".doc,.docx,.pdf" #fileInput hidden>

            <!-- Upload state (no file selected) -->
            <div class="upload-state" *ngIf="!selectedFile">
              <div class="upload-icon-container">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
              </div>
              <p class="upload-text">Drag and drop your file here or</p>
              <button class="btn-choose">Select a file</button>

              <div class="supported-file-types">
                <p class="file-types-label">Accepted file types:</p>
                <div class="file-type-icons">
                  <div class="file-type-icon">
                    <i class="fas fa-file-pdf"></i>
                    <span>PDF</span>
                  </div>
                  <div class="file-type-icon">
                    <i class="fas fa-file-word"></i>
                    <span>Word</span>
                  </div>
                </div>
                <p class="file-size-limit">Maximum size: 10 MB</p>
              </div>
            </div>

            <!-- Contract loading notification -->
            <div class="contract-selected-notification contract-loading" *ngIf="selectedContractId && isLoadingContract">
              <div class="notification-icon">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <div class="notification-content">
                <h4>Loading Contract</h4>
                <p>Please wait while we load the contract details...</p>
              </div>
            </div>

            <!-- Contract processing notification - hide when showing extracted data form -->
            <div class="contract-selected-notification contract-ready" *ngIf="selectedContractId && !isLoadingContract && selectedFile && !showExtractedData">
              <div class="notification-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="notification-content">
                <h4>Contract Ready</h4>
                <p>Contract loaded successfully. Click "Process Contract" to continue.</p>
              </div>
            </div>

            <!-- Contract error notification -->
            <div class="contract-selected-notification contract-error" *ngIf="selectedContractId && !isLoadingContract && !selectedFile">
              <div class="notification-icon">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
              <div class="notification-content">
                <h4>Contract Loading Issue</h4>
                <p>There was an issue loading the contract. Please try again or select a different contract.</p>
                <button class="btn-retry" (click)="loadContractDetails(selectedContractId)">
                  <i class="fas fa-redo-alt"></i> Retry
                </button>
              </div>
            </div>

            <!-- Preview state (file selected) -->
            <div class="file-preview" *ngIf="selectedFile">
              <div class="file-preview-header">
                <div class="file-icon-container">
                  <i class="fas fa-file-pdf" *ngIf="selectedFile.type === 'application/pdf'"></i>
                  <i class="fas fa-file-word" *ngIf="selectedFile.type.includes('word') || selectedFile.name.endsWith('.doc') || selectedFile.name.endsWith('.docx')"></i>
                </div>
                <div class="file-info">
                  <h4 class="file-name">{{ selectedFile.name }}</h4>
                  <p class="file-meta">
                    <span class="file-type">{{ getFileType(selectedFile) }}</span> •
                    <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
                    <span *ngIf="selectedContractId" class="contract-badge">Contract</span>
                  </p>
                </div>
                <button class="btn-remove-file" (click)="removeSelectedFile($event)" title="Remove file" *ngIf="!selectedContractId">
                  <i class="fas fa-times"></i>
                </button>
              </div>

              <div class="file-preview-content" *ngIf="selectedFile.type === 'application/pdf' && filePreviewUrl">
                <div class="pdf-preview">
                  <iframe [src]="filePreviewUrl" frameborder="0" height="200"></iframe>
                </div>
              </div>

              <div class="file-preview-content" *ngIf="(selectedFile.type.includes('word') || selectedFile.name.endsWith('.doc') || selectedFile.name.endsWith('.docx')) && !filePreviewUrl">
                <div class="word-preview">
                  <i class="fas fa-file-word document-icon"></i>
                  <p>Preview not available for Word documents</p>
                </div>
              </div>


            </div>
          </div>

          <div *ngIf="uploadMessage && uploadMessage !== 'Please select a file to upload.' && !uploadMessage.includes('selected. Ready for processing.')" class="message"
               [class.success]="uploadMessage.includes('successfully') || uploadMessage.includes('succès')"
               [class.error]="!uploadMessage.includes('successfully') && !uploadMessage.includes('succès')">
            <div class="message-icon">
              <i [class]="uploadMessage.includes('successfully') || uploadMessage.includes('succès') ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
            </div>
            <div class="message-content">
              <p class="message-text">{{ uploadMessage }}</p>
              <button *ngIf="!uploadMessage.includes('successfully') && !uploadMessage.includes('succès')" class="btn-try-again" (click)="fileInput.click()">
                <i class="fas fa-redo-alt"></i> Try Again
              </button>
            </div>
          </div>

          <div *ngIf="uploadProgress > 0" class="progress-container">
            <div class="progress-status">
              <span class="progress-label">Upload in progress</span>
              <span class="progress-percentage">{{ uploadProgress }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-value" [style.width.%]="uploadProgress" [class.complete]="uploadProgress === 100"></div>
            </div>
            <div class="progress-info" *ngIf="uploadProgress < 100">
              <i class="fas fa-spinner fa-spin"></i>
              <span>Please wait while uploading...</span>
            </div>
            <div class="progress-info success" *ngIf="uploadProgress === 100">
              <i class="fas fa-check-circle"></i>
              <span>Upload completed successfully!</span>
            </div>
          </div>



          <div class="form-actions action-buttons">
            <button class="btn-primary btn" (click)="uploadFile()" [disabled]="!selectedFile || isUploading" *ngIf="!selectedContractId">
              <div class="button-content">
                <div class="spinner" *ngIf="isUploading"></div>
                <span class="button-text">
                  <i class="fas fa-upload" aria-hidden="true" *ngIf="!isUploading"></i>
                  {{ isUploading ? 'Uploading...' : 'Upload' }}
                </span>
              </div>
            </button>

            <!-- Generate button for uploaded files (non-contract) -->
            <button class="btn-primary btn" (click)="getExtractedData(currentFileName)" [disabled]="!selectedFile || !currentFileName" *ngIf="!selectedContractId && currentFileName && !showExtractedData">
              <div class="button-content">
                <span class="button-text">
                  <i class="fas fa-edit" aria-hidden="true"></i>
                  Extract & Generate
                </span>
              </div>
            </button>

            <!-- Generate button for selected contracts -->
            <button class="btn-primary btn" (click)="getContractExtractedData(selectedContractId)" [disabled]="!selectedFile || !selectedContractId" *ngIf="selectedContractId && !showExtractedData">
              <div class="button-content">
                <span class="button-text">
                  <i class="fas fa-edit" aria-hidden="true"></i>
                  Extract & Generate
                </span>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- Extracted data display -->
      <article class="extracted-data-card" *ngIf="extractedData.length > 0 && showExtractedData" aria-labelledby="extracted-data-heading">
        <header>
          <h3 id="extracted-data-heading" class="card-title">
            <i class="fas fa-edit" aria-hidden="true"></i>
            Extracted Data
            <button class="btn-icon" (click)="showExtractedData = false" title="Hide form">
              <i class="fas fa-times" aria-hidden="true"></i>
            </button>
          </h3>
        </header>

        <div class="extracted-data-form">
          <p class="info-text">Please verify and complete the information extracted from the document before generating the final version.</p>

          <div class="form-fields">
            <div class="form-group" *ngFor="let field of extractedData; let i = index">
              <label [for]="'field-' + i">
                <i class="fas fa-edit"></i>
                {{ field.key || 'Field ' + (i + 1) }}
              </label>
              <input
                [id]="'field-' + i"
                type="text"
                [(ngModel)]="field.value"
                class="form-control modern-input"
                placeholder="Enter a value"
              >
            </div>
          </div>

          <div class="form-actions action-buttons">
            <button class="btn-primary btn" (click)="generateDocument()" [disabled]="isGenerating || !currentFileName">
              <div class="button-content">
                <div class="spinner" *ngIf="isGenerating"></div>
                <span class="button-text">
                  <i class="fas fa-file-export" aria-hidden="true" *ngIf="!isGenerating"></i>
                  {{ isGenerating ? 'Generating...' : 'Generate document' }}
                </span>
              </div>
            </button>
          </div>
        </div>
      </article>

      <!-- Error Message -->
      <div *ngIf="generationError" class="error-message-container">
        <div class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          <p>{{ generationError }}</p>
        </div>
        <div class="error-details" *ngIf="generationError && (generationError.includes('422') || generationError.includes('Invalid data'))">
          <p>The server could not process the document generation request. This might be due to:</p>
          <ul>
            <li>Missing or invalid replacements data</li>
            <li>File path issues with the selected contract</li>
            <li>Server-side validation errors</li>
          </ul>
          <p>Please try again or select a different document.</p>
        </div>
      </div>

      <!-- Generated Document Info -->
      <div *ngIf="generatedDocumentUrl" class="generated-document-info">
        <p class="success-message">
          <i class="fas fa-check-circle"></i>
          Document generated successfully!
        </p>
        <div class="generated-actions action-buttons">
          <a [href]="generatedDocumentUrl" class="btn-download btn" target="_blank">
            <i class="fas fa-download"></i>
            Download document
          </a>

          <!-- Sign Document button - only shown when a document has been generated -->
          <button class="btn-sign btn" (click)="navigateToSignDocument()"
                  [disabled]="!generatedDocumentId && !generatedUnsignedContractId">
            <i class="fas fa-signature"></i>
            Sign document
          </button>
        </div>
      </div>

      <!-- Colonne de liste des fichiers -->
      <article class="files-card" aria-labelledby="files-card-heading">
        <header class="files-header">
          <h3 id="files-card-heading" class="card-title">
            <i class="fas fa-file-alt" aria-hidden="true"></i>
            My Documents
          </h3>
          <div class="files-actions">
            <button
              class="btn-danger btn-delete-all"
              (click)="deleteAllFiles()"
              *ngIf="!isLoadingFiles && fileList.length > 0"
              title="Delete all documents">
              <i class="fas fa-trash-alt"></i> Delete all
            </button>
          </div>
        </header>

        <div class="files-container">
          <!-- Search and filter controls -->
          <div class="files-controls" *ngIf="!isLoadingFiles && fileList.length > 0">
            <div class="search-container">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                class="search-input modern-input"
                placeholder="Search for a document..."
                [(ngModel)]="searchTerm"
                (input)="filterFiles()">
              <button *ngIf="searchTerm" class="btn-clear-search" (click)="clearSearch()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="filter-container">
              <div class="filter-options">
                <label for="fileTypeFilter">Filter by:</label>
                <button
                  class="filter-option"
                  [class.active]="fileTypeFilter === 'all'"
                  (click)="setFileTypeFilter('all')">
                  <i class="fas fa-file-alt"></i> All
                </button>
                <button
                  class="filter-option"
                  [class.active]="fileTypeFilter === 'pdf'"
                  (click)="setFileTypeFilter('pdf')">
                  <i class="fas fa-file-pdf"></i> PDF
                </button>
                <button
                  class="filter-option"
                  [class.active]="fileTypeFilter === 'word'"
                  (click)="setFileTypeFilter('word')">
                  <i class="fas fa-file-word"></i> Word
                </button>
              </div>
            </div>
          </div>

          <div *ngIf="isLoadingFiles" class="loading-files">
            <app-spinner [active]="true"></app-spinner>
            <p>Loading documents...</p>
          </div>

          <div *ngIf="!isLoadingFiles && fileList.length === 0" class="no-files">
            <i class="fas fa-folder-open"></i>
            <p>No documents available</p>
          </div>

          <div *ngIf="!isLoadingFiles && fileList.length > 0 && filteredFiles.length === 0" class="no-search-results">
            <i class="fas fa-search"></i>
            <p>No results found for "{{ searchTerm }}"</p>
            <button class="btn-clear-filter" (click)="clearSearch()">Clear search</button>
          </div>

          <table *ngIf="!isLoadingFiles && filteredFiles.length > 0" class="files-table">
            <thead>
              <tr>
                <th class="th-file">File</th>
                <th class="th-type">Type</th>
                <th class="th-date">Date</th>
                <th class="th-actions">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let file of filteredFiles" class="file-row" [class.animate-in]="true">
                <td class="td-file">
                  <div class="file-info-cell">
                    <i class="fas fa-file-word file-icon" *ngIf="file.endsWith('.docx') || file.endsWith('.doc')"></i>
                    <i class="fas fa-file-pdf file-icon" *ngIf="file.endsWith('.pdf')"></i>
                    <span class="file-name-text">{{ file }}</span>
                  </div>
                </td>
                <td class="td-type">
                  <span class="file-type-badge" [class.pdf]="file.endsWith('.pdf')" [class.word]="file.endsWith('.docx') || file.endsWith('.doc')">
                    {{ file.endsWith('.pdf') ? 'PDF' : 'Word' }}
                  </span>
                </td>
                <td class="td-date">
                  <span class="file-date">{{ formattedDate }}</span>
                </td>
                <td class="td-actions">
                  <div class="file-actions">

                    <a [href]="getDownloadUrl(file)" class="btn-file-action" target="_blank" title="Download">
                      <i class="fas fa-download"></i>
                    </a>
                    <button class="btn-file-action delete" (click)="deleteFile(file)" title="Delete">
                      <i class="fas fa-trash-alt"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </article>
      </div>
    </div>
</div>


