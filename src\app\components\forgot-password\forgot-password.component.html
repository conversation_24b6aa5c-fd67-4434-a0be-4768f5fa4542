<div class="forgot-password-container">
  <!-- Left Side - Blue Section with modern pattern -->
  <div class="forgot-password-left">
    <div class="logo-section">
      <img src="assets/images/logo.png" alt="Logo" class="logo">
    </div>

    <div class="hero-content">
      <h2>Reset your password</h2>
      <p class="hero-subtitle">We'll help you securely regain access to your account.</p>
      <div class="contract-icon">
        <i class="fas fa-file-contract"></i>
      </div>
    </div>
  </div>

  <!-- Right Side - Dark Section -->
  <div class="forgot-password-right">
    <!-- Form View -->
    <div *ngIf="!emailSent" class="forgot-password-form-container">
      <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit(); $event.preventDefault()" class="forgot-password-form">
        <div class="form-field">
          <label for="email">Email Address</label>
          <input
            type="email"
            id="email"
            formControlName="email"
            [class.is-invalid]="email?.invalid && email?.touched"
            placeholder="Enter your email address"
            autocomplete="email"
          />
          <div class="error-message" *ngIf="email?.invalid && email?.touched">
            <span *ngIf="email?.errors?.['required']">Email is required</span>
            <span *ngIf="email?.errors?.['email']">Please enter a valid email address</span>
          </div>
        </div>

        <button
          type="submit"
          class="submit-button"
          [disabled]="!forgotPasswordForm.valid || loading"
        >
          <span *ngIf="loading" class="spinning"><i class="fas fa-spinner"></i></span>
          <span *ngIf="!loading">Send Reset Link</span>
        </button>
      </form>

      <!-- Error Message -->
      <div *ngIf="errorMessage" class="message error">
        <i class="fas fa-exclamation-triangle"></i>
        {{ errorMessage }}
      </div>
    </div>

    <!-- Success View - Exactly matching the screenshot -->
    <div *ngIf="emailSent" class="success-container">
      <div class="email-icon">
        <i class="fas fa-envelope"></i>
      </div>
      <h2>Check your inbox</h2>
      <div class="success-message">
        <i class="fas fa-check-circle"></i>
        We have emailed your password reset link!
      </div>
      <p class="instruction-text">
        We've sent password reset instructions to your email address. Please
        check your inbox and follow the link to reset your password.
      </p>

      <!-- Success message for resend - only shown when resending -->
      <div *ngIf="successMessage && successMessage !== 'Password reset link sent successfully!'" class="success-message" style="margin-top: 15px;">
        <i class="fas fa-check-circle"></i>
        {{ successMessage }}
      </div>

      <!-- Error message for resend -->
      <div *ngIf="errorMessage" class="message error" style="margin-top: 15px;">
        <i class="fas fa-exclamation-triangle"></i>
        {{ errorMessage }}
      </div>

      <div class="action-buttons">
        <button class="resend-button" (click)="resendEmail()" [disabled]="loading">
          <span *ngIf="loading" class="spinning"><i class="fas fa-spinner"></i></span>
          <span *ngIf="!loading"><i class="fas fa-redo"></i> Resend Email</span>
        </button>
        <a routerLink="/signin" class="login-button">
          <i class="fas fa-sign-in-alt"></i>
          Return to Login
        </a>
      </div>
    </div>
  </div>
</div>

