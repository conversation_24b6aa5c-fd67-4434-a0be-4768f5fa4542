<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\GoogleController;
use App\Http\Controllers\Auth\FacebookController;
use App\Mail\TestResetPasswordMail;
use Illuminate\Support\Facades\Mail;

// Main routes

Route::get('/', function () {
    return view('welcome');
});

// Email preview route
Route::get('/email/preview', function () {
    return new TestResetPasswordMail();
});

// OAuth routes in web routes for direct browser access
Route::prefix('auth')->group(function () {
    // Google OAuth routes
    Route::get('google', [GoogleController::class, 'redirectToGoogle']);
    Route::get('google/callback', [GoogleController::class, 'handleGoogleCallback']);

    // Facebook OAuth routes
    Route::get('facebook', [FacebookController::class, 'redirectToFacebook']);
    Route::get('facebook/redirect', [FacebookController::class, 'redirectToFacebook']);
    Route::get('facebook/callback', [FacebookController::class, 'handleFacebookCallback']);
});
