<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Contract;
use App\Models\Partner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ContractController extends Controller
{
    /**
     * Get all contracts or contracts for a specific partner
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $partnerId = $request->input('partnerId');
        
        if ($partnerId) {
            $contracts = Contract::where('partner_id', $partnerId)->get();
            
            return response()->json([
                'success' => true,
                'contracts' => $contracts
            ]);
        }
        
        $contracts = Contract::with('partner')->get();
        
        return response()->json([
            'success' => true,
            'contracts' => $contracts
        ]);
    }
    
    /**
     * Store a new contract
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'partner_id' => 'required|exists:partners,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'file' => 'required|file|mimes:pdf,doc,docx,txt|max:10240',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        // Handle file upload
        $file = $request->file('file');
        $originalName = $file->getClientOriginalName();
        $fileType = $this->getFileType($file->getClientOriginalExtension());
        $path = $file->store('contracts', 'public');
        
        $contract = Contract::create([
            'partner_id' => $request->input('partner_id'),
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'filename' => $originalName,
            'type' => $fileType,
            'file_path' => $path
        ]);
        
        return response()->json([
            'success' => true,
            'contract' => $contract,
            'message' => 'Contract created successfully'
        ], 201);
    }
    
    /**
     * Get a specific contract
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $contract = Contract::with('partner')->findOrFail($id);
        
        return response()->json([
            'success' => true,
            'contract' => $contract
        ]);
    }
    
    /**
     * Update a contract
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'partner_id' => 'sometimes|required|exists:partners,id',
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'file' => 'nullable|file|mimes:pdf,doc,docx,txt|max:10240',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $contract = Contract::findOrFail($id);
        
        // Update basic information
        $contract->fill($request->only(['partner_id', 'name', 'description']));
        
        // Handle file upload if a new file is provided
        if ($request->hasFile('file')) {
            // Delete old file if it exists
            if ($contract->file_path) {
                Storage::disk('public')->delete($contract->file_path);
            }
            
            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $fileType = $this->getFileType($file->getClientOriginalExtension());
            $path = $file->store('contracts', 'public');
            
            $contract->filename = $originalName;
            $contract->type = $fileType;
            $contract->file_path = $path;
        }
        
        $contract->save();
        
        return response()->json([
            'success' => true,
            'contract' => $contract,
            'message' => 'Contract updated successfully'
        ]);
    }
    
    /**
     * Delete a contract
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $contract = Contract::findOrFail($id);
        
        // Delete file if it exists
        if ($contract->file_path) {
            Storage::disk('public')->delete($contract->file_path);
        }
        
        $contract->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Contract deleted successfully'
        ]);
    }
    
    /**
     * Get contracts for a specific partner
     *
     * @param  int  $partnerId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPartnerContracts($partnerId)
    {
        $partner = Partner::findOrFail($partnerId);
        $contracts = $partner->contracts;
        
        return response()->json([
            'success' => true,
            'contracts' => $contracts
        ]);
    }
    
    /**
     * Determine file type based on extension
     *
     * @param  string  $extension
     * @return string
     */
    private function getFileType($extension)
    {
        $types = [
            'pdf' => 'pdf',
            'doc' => 'word',
            'docx' => 'word',
            'txt' => 'text'
        ];
        
        return $types[$extension] ?? 'other';
    }
}
