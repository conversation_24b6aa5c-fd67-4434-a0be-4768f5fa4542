import { RenderMode, ServerRoute } from '@angular/ssr';

// We're using standard routes with provideServerRendering() in app.config.server.ts
export const serverRoutes: ServerRoute[] = [
  {
    path: '',
    renderMode: RenderMode.Server
  },

  {
    path: 'signin',
    renderMode: RenderMode.Server
  },
  {
    path: 'signup',
    renderMode: RenderMode.Server
  },
  {
    path: 'profile',
    renderMode: RenderMode.Server
  },
  {
    path: 'auth/callback',
    renderMode: RenderMode.Server
  },
  {
    path: 'security',
    renderMode: RenderMode.Server
  },
  {
    path: 'admin',
    renderMode: RenderMode.Server
  },
  {
    path: 'admin/dashboard',
    renderMode: RenderMode.Server
  },
  {
    path: 'admin/users',
    renderMode: RenderMode.Server
  },

  {
    path: '**',
    renderMode: RenderMode.Server
  }
];
