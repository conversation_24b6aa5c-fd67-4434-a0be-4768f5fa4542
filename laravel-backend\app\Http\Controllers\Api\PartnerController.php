<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Partner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PartnerController extends Controller
{
    /**
     * Get all partners
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $partners = Partner::all();
        
        return response()->json([
            'success' => true,
            'partners' => $partners
        ]);
    }
    
    /**
     * Store a new partner
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'logo' => 'nullable|string|max:255',
            'description' => 'nullable|string'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $partner = Partner::create($request->all());
        
        return response()->json([
            'success' => true,
            'partner' => $partner,
            'message' => 'Partner created successfully'
        ], 201);
    }
    
    /**
     * Get a specific partner
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $partner = Partner::findOrFail($id);
        
        return response()->json([
            'success' => true,
            'partner' => $partner
        ]);
    }
    
    /**
     * Update a partner
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'logo' => 'nullable|string|max:255',
            'description' => 'nullable|string'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $partner = Partner::findOrFail($id);
        $partner->update($request->all());
        
        return response()->json([
            'success' => true,
            'partner' => $partner,
            'message' => 'Partner updated successfully'
        ]);
    }
    
    /**
     * Delete a partner
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $partner = Partner::findOrFail($id);
        $partner->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Partner deleted successfully'
        ]);
    }
}
