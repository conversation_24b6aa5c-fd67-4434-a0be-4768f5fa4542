<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CorsMiddleware
{
    /**
     * Handle preflight OPTIONS requests
     */
    protected function handlePreflightRequest(Request $request)
    {
        // Get the origin from the request
        $origin = $request->header('Origin');

        // Get allowed origins from config
        $allowedOrigins = config('cors.allowed_origins', ['http://localhost:4200']);

        // Set the allowed origin based on the request
        $allowedOrigin = in_array($origin, $allowedOrigins) ? $origin : '*';

        $headers = [
            'Access-Control-Allow-Origin' => $allowedOrigin,
            'Access-Control-Allow-Methods' => 'POST, GET, OPTIONS, PUT, DELETE, PATCH',
            'Access-Control-Allow-Headers' => 'Content-Type, X-Auth-Token, Origin, Authorization, X-Requested-With, Accept, X-File-Size, X-File-Name, X-File-Type, Content-Disposition',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
            'Content-Type' => 'text/plain'
        ];

        return response('', 200, $headers);
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        // Always allow OPTIONS requests for CORS preflight
        if ($request->isMethod('OPTIONS')) {
            return $this->handlePreflightRequest($request);
        }

        // Get allowed origins from config
        $allowedOrigins = config('cors.allowed_origins', ['http://localhost:4200', 'http://localhost:4000', 'http://localhost:3000']);

        // Get the origin from the request
        $origin = $request->header('Origin');

        // Set the allowed origin based on the request
        $allowedOrigin = in_array($origin, $allowedOrigins) ? $origin : $allowedOrigins[0];

        // For OAuth routes, we need to be more permissive
        if ($request->is('api/auth/*')) {
            Log::info('OAuth route detected', ['path' => $request->path(), 'origin' => $origin]);
            $allowedOrigin = $origin ?: '*';
        }

        // Document processing routes have been removed

        $response = $next($request);

        // For non-OPTIONS requests, add CORS headers to the response
        // Make sure we don't overwrite headers if they're already set
        if (!$response->headers->has('Access-Control-Allow-Origin')) {
            $response->headers->set('Access-Control-Allow-Origin', $allowedOrigin);
        }

        if (!$response->headers->has('Access-Control-Allow-Methods')) {
            $response->headers->set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS, PUT, DELETE');
        }

        if (!$response->headers->has('Access-Control-Allow-Headers')) {
            $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, X-Auth-Token, Origin, Authorization, X-Requested-With, Accept, X-File-Size, X-File-Name, X-File-Type, Content-Disposition');
        }

        if (!$response->headers->has('Access-Control-Allow-Credentials')) {
            $response->headers->set('Access-Control-Allow-Credentials', 'true');
        }

        if (!$response->headers->has('Access-Control-Max-Age')) {
            $response->headers->set('Access-Control-Max-Age', '86400');
        }

        return $response;
    }
}
