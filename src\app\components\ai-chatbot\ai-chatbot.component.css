.ai-chatbot-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  background: var(--background);
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  font-family: var(--font-family, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
  border: none;
  margin: 0;
  position: relative;
}

/* Header */
.chat-header {
  background: var(--background);
  color: var(--text);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border);
  position: relative;
  backdrop-filter: blur(20px);
}

.chat-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.05), rgba(var(--primary-rgb), 0.02));
  pointer-events: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-info .material-icons {
  font-size: 2rem;
  color: var(--primary);
  background: rgba(var(--primary-rgb), 0.1);
  padding: 0.75rem;
  border-radius: 16px;
  border: 2px solid rgba(var(--primary-rgb), 0.2);
}

.header-text h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  color: var(--text);
}

.header-text .status {
  margin: 0;
  font-size: 0.9rem;
  color: var(--textSecondary);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-text .status::before {
  content: '';
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.header-actions .btn-icon {
  background: var(--elevation1);
  border: 2px solid var(--border);
  color: var(--text);
  padding: 0.75rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.header-actions .btn-icon:hover {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.3);
}

/* Contract Selection */
.contract-selection {
  padding: 1.5rem;
  background: var(--elevation1);
  border-bottom: 1px solid var(--border);
  backdrop-filter: blur(10px);
}

.contract-selection label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text);
  font-size: 0.95rem;
}

.contract-select {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border);
  border-radius: 12px;
  font-size: 0.95rem;
  background: var(--background);
  color: var(--text);
  transition: all 0.3s ease;
  font-family: inherit;
}

.contract-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  transform: translateY(-1px);
}

.selected-contract {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(var(--primary-rgb), 0.1);
  border-radius: 12px;
  font-size: 0.9rem;
  color: var(--primary);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
  font-weight: 500;
}

.no-contracts-message {
  text-align: center;
  padding: 3rem 2rem;
  background: var(--elevation1);
  border-radius: 16px;
  border: 1px solid var(--border);
  margin: 1rem;
}

.no-contracts-message .material-icons {
  font-size: 4rem;
  color: var(--textSecondary);
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.no-contracts-message p {
  margin: 0 0 2rem 0;
  color: var(--textSecondary);
  font-size: 1rem;
  line-height: 1.6;
}

.upload-link {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.upload-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.4);
  text-decoration: none;
  color: white;
}

.upload-link .material-icons {
  font-size: 1.25rem;
}

/* Quick Actions */
.quick-actions {
  padding: 1.5rem;
  background: var(--elevation1);
  border-bottom: 1px solid var(--border);
  backdrop-filter: blur(10px);
}

.quick-actions h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: var(--text);
  font-weight: 700;
  letter-spacing: -0.01em;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.75rem;
  background: var(--background);
  border: 2px solid var(--border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.action-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.3);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.action-btn .material-icons {
  font-size: 1.5rem;
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background: var(--background);
  scroll-behavior: smooth;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 10px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--textSecondary);
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.message {
  display: flex;
  gap: 1rem;
  max-width: 85%;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-assistant {
  align-self: flex-start;
}

.message-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-user .message-avatar {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
}

.message-assistant .message-avatar {
  background: var(--elevation2);
  color: var(--primary);
  border: 2px solid rgba(var(--primary-rgb), 0.2);
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-sender {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text);
}

.message-time {
  font-size: 0.8rem;
  color: var(--textSecondary);
}

.message-text {
  background: var(--elevation1);
  padding: 1rem 1.25rem;
  border-radius: 18px;
  line-height: 1.6;
  font-size: 0.95rem;
  word-wrap: break-word;
  border: 1px solid var(--border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.message-user .message-text {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.analysis-type {
  font-weight: 500;
  color: #1e3c72;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #6c757d;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

.typing-text {
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
}

/* Input Area */
.input-area {
  padding: 1.5rem;
  background: var(--elevation1);
  border-top: 1px solid var(--border);
  backdrop-filter: blur(10px);
}

.input-container {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  padding: 1rem 1.25rem;
  border: 2px solid var(--border);
  border-radius: 24px;
  resize: none;
  font-family: inherit;
  font-size: 0.95rem;
  line-height: 1.5;
  max-height: 120px;
  min-height: 48px;
  background: var(--background);
  color: var(--text);
  transition: all 0.3s ease;
}

.message-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  transform: translateY(-1px);
}

.message-input:disabled {
  background: var(--elevation1);
  opacity: 0.6;
  cursor: not-allowed;
}

.message-input::placeholder {
  color: var(--textSecondary);
}

.send-button {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.4);
}

.send-button:disabled {
  background: var(--textSecondary);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.input-hints {
  margin-top: 1rem;
  text-align: center;
}

.input-hints p {
  margin: 0;
  font-size: 0.85rem;
  color: var(--textSecondary);
  font-style: italic;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--background-rgb), 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  text-align: center;
  color: var(--primary);
  padding: 2rem;
  background: var(--elevation1);
  border-radius: 16px;
  border: 1px solid var(--border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.loading-spinner .material-icons {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.loading-spinner p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-chatbot-container {
    height: 100vh;
    border-radius: 0;
    margin: 0;
  }

  .chat-header {
    padding: 1rem;
  }

  .header-info .material-icons {
    font-size: 2rem;
    padding: 0.4rem;
  }

  .header-text h3 {
    font-size: 1.1rem;
  }

  .contract-selection,
  .quick-actions,
  .input-area {
    padding: 1rem;
  }

  .action-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .action-btn {
    padding: 1rem 0.75rem;
    font-size: 0.75rem;
  }

  .message {
    max-width: 95%;
    gap: 0.75rem;
  }

  .message-avatar {
    width: 36px;
    height: 36px;
  }

  .messages-container {
    padding: 1rem;
  }

  .message-text {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }

  .input-container {
    gap: 0.75rem;
  }

  .send-button {
    width: 44px;
    height: 44px;
  }
}

@media (max-width: 480px) {
  .header-text h3 {
    font-size: 1rem;
  }

  .header-text .status {
    font-size: 0.8rem;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .message-text {
    font-size: 0.85rem;
  }
}

/* Dark Theme Integration */
.dark-theme .ai-chatbot-container {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark-theme .loading-overlay {
  background: rgba(var(--background-rgb), 0.95);
}

.dark-theme .message-avatar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-theme .message-text {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dark-theme .message-user .message-text {
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4);
}

.dark-theme .send-button {
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4);
}

.dark-theme .upload-link {
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4);
}

/* Focus and Accessibility */
.action-btn:focus,
.contract-select:focus,
.message-input:focus,
.send-button:focus,
.upload-link:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}
