import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class ContactService {
  private apiUrl = environment.apiUrl + '/api/contact';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Get auth headers for API requests
   */
  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getAuthToken();
    let headers = new HttpHeaders()
      .set('Accept', 'application/json')
      .set('Content-Type', 'application/json');

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Submit a contact form
   */
  submitContactForm(formData: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/submit`, formData, {
      headers: new HttpHeaders().set('Content-Type', 'application/json'),
    }).pipe(
      catchError(error => {
        console.error('Error submitting contact form:', error);
        return throwError(() => new Error('Failed to submit contact form. Please try again.'));
      })
    );
  }

  /**
   * Get all contact messages (admin only)
   */
  getContactMessages(): Observable<any> {
    return this.http.get(`${this.apiUrl}/messages`, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error fetching contact messages:', error);
        return throwError(() => new Error('Failed to load contact messages. Please try again.'));
      })
    );
  }

  /**
   * Get unread message count (admin only)
   */
  getUnreadCount(): Observable<any> {
    return this.http.get(`${this.apiUrl}/messages/unread-count`, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error fetching unread count:', error);
        return throwError(() => new Error('Failed to load unread count. Please try again.'));
      })
    );
  }

  /**
   * Mark a message as read (admin only)
   */
  markAsRead(id: number): Observable<any> {
    return this.http.put(`${this.apiUrl}/messages/${id}/mark-read`, {}, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error marking message as read:', error);
        return throwError(() => new Error('Failed to mark message as read. Please try again.'));
      })
    );
  }

  /**
   * Mark all messages as read (admin only)
   */
  markAllAsRead(): Observable<any> {
    return this.http.put(`${this.apiUrl}/messages/mark-all-read`, {}, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error marking all messages as read:', error);
        return throwError(() => new Error('Failed to mark all messages as read. Please try again.'));
      })
    );
  }

  /**
   * Delete a message (admin only)
   */
  deleteMessage(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/messages/${id}`, {
      headers: this.getAuthHeaders(),
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Error deleting message:', error);
        return throwError(() => new Error('Failed to delete message. Please try again.'));
      })
    );
  }
}
