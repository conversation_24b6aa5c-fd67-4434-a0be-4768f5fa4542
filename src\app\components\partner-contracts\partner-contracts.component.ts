import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';
import { trigger, state, style, transition, animate } from '@angular/animations';

import { PartnerService } from '../../services/partner.service';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';

// Interfaces moved from partner.model.ts
export interface Partner {
  id: number;
  name: string;
  logo?: string | null;
  description?: string | null;
}

export interface Contract {
  id?: number;
  partnerId?: number;
  name?: string;
  description?: string;
  filename?: string;
  type?: string;
  file_path?: string;
}

@Component({
  selector: 'app-partner-contracts',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    SpinnerComponent
  ],
  templateUrl: './partner-contracts.component.html',
  styleUrls: ['./partner-contracts.component.css'],
  animations: [
    trigger('fadeIn', [
      state('void', style({
        opacity: 0
      })),
      transition('void => *', [
        animate('0.3s ease-in')
      ])
    ]),
    trigger('slideIn', [
      state('void', style({
        transform: 'translateY(20px)',
        opacity: 0
      })),
      transition('void => *', [
        animate('0.4s ease-out')
      ])
    ])
  ]
})
export class PartnerContractsComponent implements OnInit, OnDestroy {
  partners: Partner[] = [];
  contracts: Contract[] = [];
  isLoadingPartners = true;
  isLoadingContracts = false;
  selectedPartnerId: number | null = null;
  selectedContractId: number | null = null;
  private subscriptions: Subscription[] = [];

  constructor(
    private partnerService: PartnerService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadPartners();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load all partners from the API
   */
  loadPartners(): void {
    this.isLoadingPartners = true;
    const subscription = this.partnerService.getPartners().subscribe({
      next: (response) => {
        if (response.success) {
          this.partners = response.partners;
        } else {
          this.partners = [];
          console.error('Failed to load partners: Invalid response format');
        }
      },
      error: (error) => {
        this.partners = [];
        console.error('Error loading partners:', error);
      },
      complete: () => {
        this.isLoadingPartners = false;
      }
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Handle partner selection
   * @param partnerId The ID of the selected partner
   */
  onPartnerSelected(partnerId: number): void {
    this.selectedPartnerId = partnerId;
    this.selectedContractId = null;

    // Load contracts for the selected partner
    this.loadPartnerContracts(partnerId);
  }

  /**
   * Load contracts for a specific partner
   * @param partnerId The ID of the partner
   */
  loadPartnerContracts(partnerId: number): void {
    this.isLoadingContracts = true;
    const subscription = this.partnerService.getPartnerContracts(partnerId).subscribe({
      next: (response) => {
        if (response.success && response.contracts) {
          this.contracts = response.contracts;
        } else {
          this.contracts = [];
          console.error('Failed to load contracts: Invalid response format');
        }
      },
      error: (error) => {
        this.contracts = [];
        console.error('Error loading contracts:', error);
      },
      complete: () => {
        this.isLoadingContracts = false;
      }
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Handle contract selection
   * @param contractId The ID of the selected contract
   */
  onContractSelected(contractId: number): void {
    if (contractId === undefined || contractId === null) {
      console.error('Cannot select contract: Contract ID is undefined or null');
      return;
    }

    this.selectedContractId = contractId;

    // Navigate to file upload with the selected contract
    this.router.navigate(['/file-upload'], {
      queryParams: {
        partnerId: this.selectedPartnerId,
        contractId: contractId
      }
    });
  }

  /**
   * Get the name of the selected partner
   */
  getSelectedPartnerName(): string {
    if (!this.selectedPartnerId) return '';
    const partner = this.partners.find(p => p.id === this.selectedPartnerId);
    return partner ? partner.name : '';
  }

  /**
   * Navigate to file upload with the selected partner
   */
  navigateToUpload(): void {
    if (this.selectedPartnerId) {
      this.router.navigate(['/file-upload'], {
        queryParams: { partnerId: this.selectedPartnerId }
      });
    }
  }
}
