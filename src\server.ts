import { APP_BASE_HREF } from '@angular/common';
import { AngularNodeAppEngine, writeResponseToNodeResponse } from '@angular/ssr/node';
import express from 'express';
import { dirname, resolve, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import { existsSync } from 'node:fs';
import bootstrap from './main.server';

// Get directory paths
const currentFilePath = fileURLToPath(import.meta.url);
const serverDistFolder = dirname(currentFilePath);
const browserDistFolder = resolve(serverDistFolder, '../browser');
const indexHtml = resolve(browserDistFolder, 'index.html');

// Ensure the index.html file exists
if (!existsSync(indexHtml)) {
  console.error(`File not found: ${indexHtml}`);
  console.error('Make sure you have built the application with "ng build"');
}

// Create Express app
const app = express();
const appEngine = new AngularNodeAppEngine();

/**
 * Example Express Rest API endpoints can be defined here.
 * Uncomment and define endpoints as necessary.
 *
 * Example:
 * ```ts
 * app.get('/api/**', (req, res) => {
 *   // Handle API request
 * });
 * ```
 */

/**
 * Serve static files from /browser
 */
app.use(
  express.static(browserDistFolder, {
    maxAge: '1y',
    index: false,
    redirect: false,
  }),
);

/**
 * Handle all other requests by rendering the Angular application.
 */
app.get('*', (req, res, next) => {
  const { protocol, originalUrl, baseUrl, headers } = req;

  // Log the request for debugging
  console.log(`Handling request for: ${originalUrl}`);

  appEngine
    .handle(req, { server: 'express' })
    .then((response) => {
      if (response) {
        // Use the helper function to write the response to the Express response
        writeResponseToNodeResponse(response, res);
      } else {
        // Fall back to serving the static index.html if no response is generated by SSR
        console.log('Falling back to static index.html');
        res.sendFile(resolve(browserDistFolder, 'index.html'));
      }
    })
    .catch((err: Error) => {
      console.error(`Error rendering: ${originalUrl}`, err);
      next(err);
    });
});

/**
 * Start the server if this module is the main entry point.
 * The server listens on the port defined by the `PORT` environment variable, or defaults to 4000.
 */
if (isMainModule(import.meta.url)) {
  const port = process.env['PORT'] || 4000;
  app.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

/**
 * Helper function to determine if this is the main module
 */
export function isMainModule(url: string): boolean {
  return url === `file://${process.argv[1]}`;
}

export default app;
